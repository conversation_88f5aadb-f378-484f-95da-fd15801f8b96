#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查训练任务状态
Check Training Jobs Status

Author: License Plate Recognition Team
Date: 2025-07-30
"""

import requests
import json

def check_jobs():
    """检查所有训练任务"""
    try:
        response = requests.get('http://127.0.0.1:8007/api/training/jobs')
        jobs = response.json()['jobs']
        
        print(f"📋 当前训练任务数: {len(jobs)}")
        print("=" * 50)
        
        if not jobs:
            print("暂无训练任务")
            return
        
        for job in jobs:
            print(f"🔹 任务ID: {job['job_id']}")
            print(f"   实验名称: {job['config']['experiment_name']}")
            print(f"   状态: {job['status']}")
            print(f"   进度: {job['progress']:.1f}%")
            print(f"   轮次: {job['current_epoch']}/{job['config']['epochs']}")
            print(f"   验证准确率: {job['val_acc']*100:.2f}%")
            print(f"   最佳准确率: {job['best_acc']*100:.2f}%")
            print(f"   消息: {job['message']}")
            print("-" * 30)
            
    except Exception as e:
        print(f"❌ 检查失败: {e}")

if __name__ == "__main__":
    check_jobs()
