#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
演示训练客户端
Demo Training Client

演示如何通过API接口调用训练服务

Author: License Plate Recognition Team
Date: 2025-07-30
"""

import requests
import time
import json
from typing import Dict, Any

class DemoTrainingClient:
    """演示训练客户端"""
    
    def __init__(self, api_base_url: str = "http://127.0.0.1:8003"):
        """
        初始化客户端
        
        Args:
            api_base_url (str): API服务器地址
        """
        self.api_base_url = api_base_url.rstrip('/')
        self.session = requests.Session()
        
        print(f"训练客户端初始化完成，API地址: {api_base_url}")
    
    def test_connection(self) -> bool:
        """测试API连接"""
        try:
            response = self.session.get(f"{self.api_base_url}/api/test")
            response.raise_for_status()
            
            result = response.json()
            print(f"✅ API连接测试成功: {result['message']}")
            return True
            
        except requests.exceptions.RequestException as e:
            print(f"❌ API连接测试失败: {e}")
            return False
    
    def get_server_status(self) -> Dict[str, Any]:
        """获取服务器状态"""
        try:
            response = self.session.get(f"{self.api_base_url}/api/status")
            response.raise_for_status()
            
            status = response.json()
            print("📊 服务器状态:")
            print(f"  - 服务器状态: {status['server']}")
            print(f"  - PyTorch版本: {status['pytorch_version']}")
            print(f"  - CUDA可用: {status['cuda_available']}")
            print(f"  - 计算设备: {status['device']}")
            
            return status
            
        except requests.exceptions.RequestException as e:
            print(f"❌ 获取服务器状态失败: {e}")
            return {}
    
    def demo_basic_functionality(self):
        """演示基本功能"""
        print("\n" + "=" * 60)
        print("🚀 CCPD训练系统演示")
        print("=" * 60)
        
        # 1. 测试连接
        print("\n1️⃣ 测试API连接...")
        if not self.test_connection():
            print("❌ 无法连接到API服务器，请确保服务器正在运行")
            return
        
        # 2. 获取服务器状态
        print("\n2️⃣ 获取服务器状态...")
        status = self.get_server_status()
        if not status:
            print("❌ 无法获取服务器状态")
            return
        
        # 3. 演示完成
        print("\n✅ 基本功能演示完成！")
        print("\n📋 可用功能:")
        print("  - ✅ API连接测试")
        print("  - ✅ 服务器状态查询")
        print("  - 🔄 训练任务管理 (开发中)")
        print("  - 📊 训练进度监控 (开发中)")
        print("  - 📈 训练数据可视化 (开发中)")
        
        print(f"\n🌐 访问Web界面: {self.api_base_url}")
        print(f"📚 API文档: {self.api_base_url}/docs")
    
    def demo_advanced_features(self):
        """演示高级功能（模拟）"""
        print("\n" + "=" * 60)
        print("🔬 高级功能演示（模拟）")
        print("=" * 60)
        
        # 模拟训练配置
        training_configs = [
            {
                "name": "ResNet18基础训练",
                "model": "resnet18",
                "batch_size": 32,
                "learning_rate": 0.001,
                "epochs": 20
            },
            {
                "name": "EfficientNet优化训练",
                "model": "efficientnet",
                "batch_size": 16,
                "learning_rate": 0.0005,
                "epochs": 30
            },
            {
                "name": "高精度长时间训练",
                "model": "resnet50",
                "batch_size": 8,
                "learning_rate": 0.0001,
                "epochs": 100
            }
        ]
        
        print("\n📋 可用训练配置:")
        for i, config in enumerate(training_configs, 1):
            print(f"  {i}. {config['name']}")
            print(f"     模型: {config['model']}, 批次: {config['batch_size']}, "
                  f"学习率: {config['learning_rate']}, 轮数: {config['epochs']}")
        
        print("\n🔄 模拟训练流程:")
        steps = [
            "📥 下载CCPD数据集",
            "🔍 数据预处理和增强",
            "🏗️ 构建神经网络模型",
            "⚙️ 配置优化器和损失函数",
            "🚀 开始训练循环",
            "📊 实时监控训练指标",
            "💾 保存最佳模型",
            "📈 生成训练报告和图表"
        ]
        
        for i, step in enumerate(steps, 1):
            print(f"  {i}. {step}")
            time.sleep(0.5)  # 模拟处理时间
        
        print("\n✅ 高级功能演示完成！")
    
    def interactive_demo(self):
        """交互式演示"""
        print("\n" + "=" * 60)
        print("🎮 交互式演示")
        print("=" * 60)
        
        while True:
            print("\n请选择操作:")
            print("1. 测试API连接")
            print("2. 查看服务器状态")
            print("3. 演示基本功能")
            print("4. 演示高级功能")
            print("5. 打开Web界面")
            print("0. 退出")
            
            try:
                choice = input("\n请输入选择 (0-5): ").strip()
                
                if choice == "0":
                    print("👋 再见！")
                    break
                elif choice == "1":
                    self.test_connection()
                elif choice == "2":
                    self.get_server_status()
                elif choice == "3":
                    self.demo_basic_functionality()
                elif choice == "4":
                    self.demo_advanced_features()
                elif choice == "5":
                    print(f"🌐 正在打开Web界面: {self.api_base_url}")
                    import webbrowser
                    webbrowser.open(self.api_base_url)
                else:
                    print("❌ 无效选择，请重试")
                    
            except KeyboardInterrupt:
                print("\n\n👋 用户中断，再见！")
                break
            except Exception as e:
                print(f"❌ 操作失败: {e}")


def main():
    """主函数"""
    print("=" * 60)
    print("🚗 CCPD车牌识别训练系统 - 演示客户端")
    print("=" * 60)
    print("基于API的分布式训练架构演示")
    print("通过接口调用避免程序之间的不利影响")
    
    # 创建客户端
    client = DemoTrainingClient()
    
    # 运行演示
    try:
        # 首先运行基本功能演示
        client.demo_basic_functionality()
        
        # 询问是否继续
        print("\n" + "=" * 60)
        response = input("是否继续交互式演示？(y/n): ").strip().lower()
        
        if response in ['y', 'yes', '是', '继续']:
            client.interactive_demo()
        else:
            print("\n✅ 演示完成！")
            print(f"💡 提示: 可以访问 {client.api_base_url} 查看Web界面")
            
    except KeyboardInterrupt:
        print("\n\n👋 用户中断，演示结束")
    except Exception as e:
        print(f"\n❌ 演示过程中发生错误: {e}")


if __name__ == "__main__":
    main()
