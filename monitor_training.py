#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
监控训练进度
Monitor Training Progress

Author: License Plate Recognition Team
Date: 2025-07-30
"""

import requests
import time
import os
from datetime import datetime

def monitor_training(job_id):
    """监控指定训练任务"""
    print(f"📊 开始监控训练任务: {job_id}")
    print("按 Ctrl+C 停止监控")
    print("=" * 80)
    
    last_epoch = 0
    
    try:
        while True:
            try:
                response = requests.get(f'http://127.0.0.1:8007/api/training/status/{job_id}')
                job = response.json()
                
                # 每个新轮次清屏显示完整状态
                if job['current_epoch'] != last_epoch:
                    os.system('cls' if os.name == 'nt' else 'clear')
                    display_detailed_status(job)
                    last_epoch = job['current_epoch']
                else:
                    # 只更新进度行
                    print(f"\r📈 实时状态: {job['status']} | 进度: {job['progress']:.1f}% | "
                          f"轮次: {job['current_epoch']}/{job['config']['epochs']} | "
                          f"验证准确率: {job['val_acc']*100:.2f}% | "
                          f"最佳: {job['best_acc']*100:.2f}% | "
                          f"学习率: {job['learning_rate']:.6f}", end="")
                
                if job['status'] in ['completed', 'failed', 'stopped']:
                    print(f"\n\n🏁 训练结束! 状态: {job['status']}")
                    display_final_results(job)
                    break
                
                time.sleep(2)
                
            except requests.exceptions.RequestException as e:
                print(f"\n❌ 网络错误: {e}")
                time.sleep(5)
                
    except KeyboardInterrupt:
        print(f"\n\n⏹️ 监控已停止")
        print(f"训练任务 {job_id} 仍在后台运行")
        print("可以通过网页界面 http://127.0.0.1:8007 继续监控")

def display_detailed_status(job):
    """显示详细状态"""
    print("🎯 车牌识别训练 - 实时监控")
    print("=" * 80)
    
    # 基本信息
    print(f"📋 任务信息:")
    print(f"  任务ID: {job['job_id']}")
    print(f"  实验名称: {job['config']['experiment_name']}")
    print(f"  状态: {job['status'].upper()}")
    print(f"  进度: {job['progress']:.1f}%")
    print(f"  当前轮次: {job['current_epoch']}/{job['config']['epochs']}")
    
    # 训练指标
    print(f"\n📊 训练指标:")
    print(f"  训练损失: {job['train_loss']:.4f}")
    print(f"  验证损失: {job['val_loss']:.4f}")
    print(f"  训练准确率: {job['train_acc']*100:.2f}%")
    print(f"  验证准确率: {job['val_acc']*100:.2f}%")
    print(f"  🏆 最佳准确率: {job['best_acc']*100:.2f}%")
    print(f"  当前学习率: {job['learning_rate']:.6f}")
    
    # 模型信息
    print(f"\n🔧 模型配置:")
    config = job['config']
    print(f"  模型类型: {config['model_type']}")
    print(f"  批次大小: {config['batch_size']}")
    print(f"  优化器: {config['optimizer_type']}")
    print(f"  调度器: {config['scheduler_type']}")
    print(f"  数据增强: {'✅' if config['data_augmentation'] else '❌'}")
    print(f"  早停机制: {'✅' if config['early_stopping'] else '❌'}")
    
    if job.get('model_params'):
        print(f"  模型参数: {job['model_params']:,} ({job['model_params']/1000000:.1f}M)")
    
    # 性能信息
    if job.get('epoch_times') and len(job['epoch_times']) > 0:
        avg_time = sum(job['epoch_times']) / len(job['epoch_times'])
        last_time = job['epoch_times'][-1]
        print(f"\n⏱️ 性能信息:")
        print(f"  平均轮次耗时: {avg_time:.1f}s")
        print(f"  最近轮次耗时: {last_time:.1f}s")
        
        if job['current_epoch'] > 0:
            remaining_epochs = job['config']['epochs'] - job['current_epoch']
            estimated_time = remaining_epochs * avg_time
            print(f"  预计剩余时间: {estimated_time/60:.1f}分钟")
    
    # 当前状态消息
    print(f"\n💬 状态消息: {job['message']}")
    
    # 最近日志
    if job.get('detailed_log') and len(job['detailed_log']) > 0:
        print(f"\n📝 最近训练日志:")
        recent_logs = job['detailed_log'][-3:]  # 显示最近3条
        for log in recent_logs:
            print(f"  {log}")
    
    print("\n" + "=" * 80)
    print(f"更新时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

def display_final_results(job):
    """显示最终结果"""
    print("\n🎉 训练完成! 最终结果报告")
    print("=" * 60)
    
    print(f"📊 最终训练结果:")
    print(f"  实验名称: {job['config']['experiment_name']}")
    print(f"  模型类型: {job['config']['model_type']}")
    print(f"  完成轮次: {job['current_epoch']}/{job['config']['epochs']}")
    print(f"  最终训练损失: {job['train_loss']:.4f}")
    print(f"  最终验证损失: {job['val_loss']:.4f}")
    print(f"  最终训练准确率: {job['train_acc']*100:.2f}%")
    print(f"  最终验证准确率: {job['val_acc']*100:.2f}%")
    print(f"  🏆 最佳验证准确率: {job['best_acc']*100:.2f}%")
    
    if job.get('model_params'):
        print(f"  模型参数量: {job['model_params']:,} ({job['model_params']/1000000:.1f}M)")
    
    if job.get('epoch_times') and len(job['epoch_times']) > 0:
        avg_time = sum(job['epoch_times']) / len(job['epoch_times'])
        total_time = sum(job['epoch_times'])
        print(f"  平均轮次耗时: {avg_time:.1f}s")
        print(f"  总训练时间: {total_time:.1f}s ({total_time/60:.1f}分钟)")
    
    # 显示完整训练日志
    if job.get('detailed_log') and len(job['detailed_log']) > 0:
        print(f"\n📝 完整训练日志:")
        for log in job['detailed_log']:
            print(f"  {log}")
    
    print("\n" + "=" * 60)

def main():
    """主函数"""
    # 获取正在运行的任务
    try:
        response = requests.get('http://127.0.0.1:8007/api/training/jobs')
        jobs = response.json()['jobs']
        
        running_jobs = [job for job in jobs if job['status'] == 'running']
        
        if not running_jobs:
            print("📝 暂无正在运行的训练任务")
            
            # 显示所有任务
            if jobs:
                print(f"\n📋 所有任务 ({len(jobs)}个):")
                for job in jobs:
                    status_emoji = {"pending": "⏳", "running": "🔄", "completed": "✅", "failed": "❌", "stopped": "⏹️"}
                    emoji = status_emoji.get(job["status"], "❓")
                    print(f"  {emoji} {job['job_id']} - {job['config']['experiment_name']} ({job['status']})")
            return
        
        if len(running_jobs) == 1:
            # 只有一个运行中的任务，直接监控
            job_id = running_jobs[0]['job_id']
            print(f"🔄 发现正在运行的任务: {running_jobs[0]['config']['experiment_name']}")
            monitor_training(job_id)
        else:
            # 多个运行中的任务，让用户选择
            print(f"🔄 发现 {len(running_jobs)} 个正在运行的任务:")
            for i, job in enumerate(running_jobs):
                print(f"  {i+1}. {job['job_id']} - {job['config']['experiment_name']}")
            
            try:
                choice = int(input("请选择要监控的任务编号: ").strip()) - 1
                if 0 <= choice < len(running_jobs):
                    monitor_training(running_jobs[choice]['job_id'])
                else:
                    print("❌ 无效选择")
            except (ValueError, KeyboardInterrupt):
                print("❌ 无效输入")
                
    except Exception as e:
        print(f"❌ 获取任务列表失败: {e}")

if __name__ == "__main__":
    main()
