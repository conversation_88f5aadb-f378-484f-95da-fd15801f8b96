# -*- coding: utf-8 -*-
"""
快速测试改进后的百度图片爬虫
Quick Test for Improved Baidu Image Crawler
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.utils.chinese_support import setup_chinese_environment
from src.data_collection.baidu_image_crawler import BaiduImageCrawler


def main():
    """快速测试主函数"""
    print("快速测试改进后的百度图片爬虫")
    print("=" * 50)
    
    # 设置中文环境
    setup_chinese_environment()
    
    # 创建爬虫实例
    crawler = BaiduImageCrawler(
        output_dir="data/quick_test",
        max_workers=2
    )
    
    # 测试关键词
    test_keywords = ["车牌", "蓝牌车"]
    
    for keyword in test_keywords:
        print(f"\n测试关键词: {keyword}")
        
        try:
            # 搜索少量图片
            image_urls = crawler.search_baidu_images(keyword, max_images=5)
            print(f"找到 {len(image_urls)} 张图片")
            
            if len(image_urls) > 0:
                print("✓ 搜索成功")
                
                # 显示第一个结果
                img_info = image_urls[0]
                print(f"  示例图片:")
                print(f"    缩略图: {img_info.get('thumb_url', 'N/A')[:60]}...")
                print(f"    标题: {img_info.get('title', 'N/A')[:40]}...")
            else:
                print("✗ 搜索失败")
                
        except Exception as e:
            print(f"✗ 搜索出错: {str(e)}")
    
    # 测试小规模下载
    print(f"\n测试下载功能...")
    try:
        stats = crawler.crawl_license_plates(target_images=5)
        print(f"下载统计: {stats}")
        
        if stats['successful_downloads'] > 0:
            print("✓ 下载功能正常")
        else:
            print("✗ 下载功能异常")
            
    except Exception as e:
        print(f"✗ 下载测试失败: {str(e)}")
    
    print("\n测试完成！")


if __name__ == "__main__":
    main()
