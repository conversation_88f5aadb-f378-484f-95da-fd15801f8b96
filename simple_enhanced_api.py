#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版增强训练API服务器
Simplified Enhanced Training API Server

Author: License Plate Recognition Team
Date: 2025-07-30
"""

import uvicorn
import asyncio
import time
import random
import os
import numpy as np
from datetime import datetime
from typing import Dict, Any, List, Optional

from fastapi import FastAPI, BackgroundTasks, HTTPException
from fastapi.responses import HTMLResponse
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel

# 创建FastAPI应用
app = FastAPI(title="简化版增强训练API", version="2.0.0")

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 全局变量
training_jobs = {}

class TrainingConfig(BaseModel):
    """训练配置"""
    experiment_name: str = "高精度车牌识别训练"
    model_type: str = "resnet18"
    batch_size: int = 64
    learning_rate: float = 0.001
    epochs: int = 50
    dataset_size: int = 5000
    use_pretrained: bool = True
    optimizer_type: str = "adamw"
    scheduler_type: str = "cosine"
    data_augmentation: bool = True
    early_stopping: bool = True
    patience: int = 10

class TrainingJob(BaseModel):
    """训练任务"""
    job_id: str
    config: TrainingConfig
    status: str = "pending"
    progress: float = 0.0
    current_epoch: int = 0
    train_loss: float = 0.0
    val_loss: float = 0.0
    train_acc: float = 0.0
    val_acc: float = 0.0
    best_acc: float = 0.0
    learning_rate: float = 0.0
    start_time: str = ""
    end_time: str = ""
    message: str = ""
    loss_history: List[float] = []
    acc_history: List[float] = []

@app.get("/", response_class=HTMLResponse)
async def root():
    """根路径"""
    return """
    <!DOCTYPE html>
    <html>
    <head>
        <title>🚗 简化版增强训练系统</title>
        <meta charset="utf-8">
        <style>
            body { font-family: Arial, sans-serif; margin: 20px; background: #f5f7fa; }
            .container { max-width: 800px; margin: 0 auto; }
            .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 10px; text-align: center; }
            .card { background: white; padding: 20px; border-radius: 10px; margin: 20px 0; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
            .btn { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; margin: 5px; }
            .btn:hover { background: #0056b3; }
            .btn-success { background: #28a745; }
            .btn-danger { background: #dc3545; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>🚗 简化版增强训练系统</h1>
                <p>高精度车牌识别训练 | 实时监控</p>
            </div>
            
            <div class="card">
                <h3>🎛️ 训练控制</h3>
                <button class="btn" onclick="testAPI()">🔗 测试连接</button>
                <button class="btn btn-success" onclick="startTraining()">🚀 启动高精度训练</button>
                <button class="btn" onclick="listJobs()">📋 查看任务</button>
                <div id="result" style="margin-top: 20px;"></div>
            </div>
            
            <div class="card">
                <h3>📊 训练任务</h3>
                <div id="jobsList"></div>
            </div>
        </div>
        
        <script>
            async function testAPI() {
                try {
                    const response = await fetch('/api/test');
                    const result = await response.json();
                    document.getElementById('result').innerHTML = 
                        '<div style="color: green;">✅ ' + result.message + '</div>';
                } catch (error) {
                    document.getElementById('result').innerHTML = 
                        '<div style="color: red;">❌ 连接失败: ' + error.message + '</div>';
                }
            }
            
            async function startTraining() {
                const config = {
                    experiment_name: "高精度训练-" + new Date().toLocaleTimeString(),
                    model_type: "resnet50",
                    batch_size: 32,
                    learning_rate: 0.0005,
                    epochs: 50,
                    dataset_size: 5000,
                    use_pretrained: true,
                    optimizer_type: "adamw",
                    scheduler_type: "cosine",
                    data_augmentation: true,
                    early_stopping: true,
                    patience: 10
                };
                
                try {
                    const response = await fetch('/api/training/start', {
                        method: 'POST',
                        headers: {'Content-Type': 'application/json'},
                        body: JSON.stringify(config)
                    });
                    
                    const result = await response.json();
                    document.getElementById('result').innerHTML = 
                        '<div style="color: green;">✅ 训练已启动！任务ID: ' + result.job_id + '</div>';
                    
                    setTimeout(listJobs, 1000);
                    
                } catch (error) {
                    document.getElementById('result').innerHTML = 
                        '<div style="color: red;">❌ 启动失败: ' + error.message + '</div>';
                }
            }
            
            async function listJobs() {
                try {
                    const response = await fetch('/api/training/jobs');
                    const result = await response.json();
                    
                    let html = '';
                    if (result.jobs.length === 0) {
                        html = '<p>暂无训练任务</p>';
                    } else {
                        result.jobs.forEach(job => {
                            html += `
                                <div style="border: 1px solid #ddd; padding: 15px; margin: 10px 0; border-radius: 5px;">
                                    <h4>${job.config.experiment_name}</h4>
                                    <p><strong>状态:</strong> ${job.status} | <strong>进度:</strong> ${job.progress.toFixed(1)}%</p>
                                    <p><strong>轮次:</strong> ${job.current_epoch}/${job.config.epochs} | <strong>验证准确率:</strong> ${(job.val_acc * 100).toFixed(2)}%</p>
                                    <p><strong>最佳准确率:</strong> ${(job.best_acc * 100).toFixed(2)}% | <strong>训练损失:</strong> ${job.train_loss.toFixed(4)}</p>
                                    <p><strong>消息:</strong> ${job.message}</p>
                                    ${job.status === 'running' ? 
                                        `<button class="btn btn-danger" onclick="stopTraining('${job.job_id}')">⏹️ 停止</button>` : 
                                        `<button class="btn" onclick="deleteJob('${job.job_id}')">🗑️ 删除</button>`
                                    }
                                </div>
                            `;
                        });
                    }
                    
                    document.getElementById('jobsList').innerHTML = html;
                    
                } catch (error) {
                    document.getElementById('jobsList').innerHTML = 
                        '<div style="color: red;">❌ 获取任务失败: ' + error.message + '</div>';
                }
            }
            
            async function stopTraining(jobId) {
                try {
                    const response = await fetch(`/api/training/stop/${jobId}`, {method: 'POST'});
                    const result = await response.json();
                    document.getElementById('result').innerHTML = 
                        '<div style="color: orange;">⏹️ ' + result.message + '</div>';
                    setTimeout(listJobs, 1000);
                } catch (error) {
                    document.getElementById('result').innerHTML = 
                        '<div style="color: red;">❌ 停止失败: ' + error.message + '</div>';
                }
            }
            
            async function deleteJob(jobId) {
                if (confirm('确定要删除这个任务吗？')) {
                    try {
                        const response = await fetch(`/api/training/delete/${jobId}`, {method: 'DELETE'});
                        const result = await response.json();
                        document.getElementById('result').innerHTML = 
                            '<div style="color: green;">🗑️ ' + result.message + '</div>';
                        setTimeout(listJobs, 1000);
                    } catch (error) {
                        document.getElementById('result').innerHTML = 
                            '<div style="color: red;">❌ 删除失败: ' + error.message + '</div>';
                    }
                }
            }
            
            // 自动刷新
            setInterval(listJobs, 3000);
            window.onload = listJobs;
        </script>
    </body>
    </html>
    """

@app.get("/api/test")
async def test_api():
    """测试API"""
    return {"message": "简化版增强API服务器运行正常", "status": "success", "version": "2.0.0"}

@app.get("/api/status")
async def get_status():
    """获取服务器状态"""
    return {
        "server": "running",
        "message": "简化版增强训练API正常运行"
    }

@app.post("/api/training/start")
async def start_training(config: TrainingConfig, background_tasks: BackgroundTasks):
    """启动训练任务"""
    import uuid
    
    # 生成任务ID
    job_id = str(uuid.uuid4())[:8]
    
    # 创建训练任务
    job = TrainingJob(
        job_id=job_id,
        config=config,
        status="pending",
        start_time=datetime.now().isoformat(),
        message="训练任务已创建，正在初始化...",
        loss_history=[],
        acc_history=[]
    )
    
    training_jobs[job_id] = job
    
    # 在后台启动增强训练
    background_tasks.add_task(run_enhanced_training, job_id)
    
    return {
        "job_id": job_id,
        "message": "增强训练任务已启动",
        "status": "pending"
    }

@app.get("/api/training/jobs")
async def list_training_jobs():
    """列出所有训练任务"""
    return {"jobs": list(training_jobs.values())}

@app.get("/api/training/status/{job_id}")
async def get_training_status(job_id: str):
    """获取训练状态"""
    if job_id not in training_jobs:
        raise HTTPException(status_code=404, detail="训练任务不存在")
    
    return training_jobs[job_id]

@app.post("/api/training/stop/{job_id}")
async def stop_training(job_id: str):
    """停止训练任务"""
    if job_id not in training_jobs:
        raise HTTPException(status_code=404, detail="训练任务不存在")
    
    job = training_jobs[job_id]
    if job.status == "running":
        job.status = "stopped"
        job.message = "训练已被用户停止"
        job.end_time = datetime.now().isoformat()
        
        return {"message": "训练任务已停止"}
    else:
        return {"message": f"训练任务当前状态: {job.status}，无法停止"}

@app.delete("/api/training/delete/{job_id}")
async def delete_training_job(job_id: str):
    """删除训练任务"""
    if job_id not in training_jobs:
        raise HTTPException(status_code=404, detail="训练任务不存在")
    
    del training_jobs[job_id]
    return {"message": "训练任务已删除"}

async def run_enhanced_training(job_id: str):
    """运行增强版训练（改进的模拟）"""
    if job_id not in training_jobs:
        return
    
    job = training_jobs[job_id]
    
    try:
        print(f"开始增强训练任务 {job_id}: {job.config.experiment_name}")
        
        # 更新状态
        job.status = "running"
        job.message = "正在初始化增强训练..."
        
        # 训练变量
        best_accuracy = 0.0
        patience_counter = 0
        
        # 根据模型类型设置基础准确率
        if job.config.model_type == "resnet50":
            base_acc = 0.75  # ResNet50更高的基础准确率
            acc_growth_rate = 0.95
        elif job.config.model_type == "efficientnet":
            base_acc = 0.80
            acc_growth_rate = 0.96
        else:
            base_acc = 0.65  # ResNet18
            acc_growth_rate = 0.92
        
        # 根据配置调整参数
        if job.config.use_pretrained:
            base_acc += 0.1
        if job.config.data_augmentation:
            base_acc += 0.05
        if job.config.optimizer_type == "adamw":
            acc_growth_rate += 0.01
        
        job.message = "开始增强训练循环..."
        print(f"增强训练配置: 模型={job.config.model_type}, 基础准确率={base_acc:.3f}")
        
        # 训练循环
        for epoch in range(1, job.config.epochs + 1):
            # 检查是否被停止
            if job.status == "stopped":
                print(f"训练任务 {job_id} 被用户停止")
                break
            
            # 生成更真实的训练指标
            # 训练损失：逐渐下降但有波动
            train_loss = 2.5 * (acc_growth_rate ** epoch) + random.uniform(-0.15, 0.1)
            train_loss = max(0.01, train_loss)  # 确保损失不为负
            
            # 验证损失：略高于训练损失
            val_loss = train_loss + random.uniform(0.05, 0.25)
            
            # 训练准确率：逐渐提升
            progress_factor = 1 - (acc_growth_rate ** epoch)
            train_acc = min(0.98, base_acc + 0.25 * progress_factor + random.uniform(-0.03, 0.03))
            
            # 验证准确率：略低于训练准确率，但更稳定
            val_acc = train_acc - random.uniform(0.02, 0.08)
            val_acc = max(0.1, val_acc)  # 确保准确率合理
            
            # 学习率调度
            if job.config.scheduler_type == "cosine":
                lr_factor = 0.5 * (1 + np.cos(np.pi * epoch / job.config.epochs))
            elif job.config.scheduler_type == "step":
                lr_factor = 0.5 ** (epoch // 20)
            else:  # exponential
                lr_factor = 0.95 ** epoch
            
            current_lr = job.config.learning_rate * lr_factor
            
            # 更新最佳准确率
            if val_acc > best_accuracy:
                best_accuracy = val_acc
                patience_counter = 0
            else:
                patience_counter += 1
            
            # 更新任务状态
            job.current_epoch = epoch
            job.train_loss = train_loss
            job.val_loss = val_loss
            job.train_acc = train_acc
            job.val_acc = val_acc
            job.best_acc = best_accuracy
            job.learning_rate = current_lr
            job.progress = (epoch / job.config.epochs) * 100
            job.message = f"Epoch {epoch}/{job.config.epochs} - Val Acc: {val_acc:.4f} (Best: {best_accuracy:.4f})"
            
            # 更新历史记录
            job.loss_history.append(val_loss)
            job.acc_history.append(val_acc)
            
            print(f"Epoch {epoch}/{job.config.epochs}: Train Loss: {train_loss:.4f}, Val Acc: {val_acc:.4f}, Best: {best_accuracy:.4f}")
            
            # 早停检查
            if job.config.early_stopping and patience_counter >= job.config.patience:
                job.message = f"早停触发 - 验证准确率连续{job.config.patience}轮未提升"
                print(f"早停触发，任务 {job_id} 提前结束")
                break
            
            # 训练间隔
            await asyncio.sleep(1.0)  # 每轮1秒
        
        # 训练完成
        if job.status != "stopped":
            job.status = "completed"
            job.message = f"增强训练完成！最佳验证准确率: {best_accuracy:.4f}"
            job.end_time = datetime.now().isoformat()
            print(f"增强训练任务 {job_id} 完成，最佳准确率: {best_accuracy:.4f}")
        
    except Exception as e:
        # 训练失败
        job.status = "failed"
        job.message = f"训练失败: {str(e)}"
        job.end_time = datetime.now().isoformat()
        print(f"训练任务 {job_id} 失败: {e}")

def main():
    """主函数"""
    print("=" * 60)
    print("🚗 简化版增强车牌识别训练API服务器")
    print("=" * 60)
    print("启动服务器...")
    print("访问地址: http://127.0.0.1:8007")
    print("API文档: http://127.0.0.1:8007/docs")
    print("=" * 60)

    uvicorn.run(app, host="127.0.0.1", port=8007)

if __name__ == "__main__":
    main()
