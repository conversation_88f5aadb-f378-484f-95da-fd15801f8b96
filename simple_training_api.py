#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化训练API服务器
Simple Training API Server

用于测试的简化版本

Author: License Plate Recognition Team
Date: 2025-07-30
"""

import uvicorn
import asyncio
import time
import random
from datetime import datetime
from fastapi import FastAPI, BackgroundTasks, HTTPException
from fastapi.responses import HTMLResponse
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel

# 创建FastAPI应用
app = FastAPI(title="简化训练API", version="1.0.0")

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/", response_class=HTMLResponse)
async def root():
    """根路径"""
    return """
    <!DOCTYPE html>
    <html>
    <head>
        <title>简化训练API服务器</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 40px; }
            .container { max-width: 800px; margin: 0 auto; }
            .status { background: #f0f8ff; padding: 20px; border-radius: 10px; margin: 20px 0; }
            .btn { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; }
            .btn:hover { background: #0056b3; }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🚗 CCPD训练API服务器</h1>
            <div class="status">
                <h3>✅ 服务器运行正常</h3>
                <p>API服务器已成功启动并运行</p>
                <p><strong>服务地址:</strong> http://127.0.0.1:8003</p>
                <p><strong>API文档:</strong> <a href="/docs">/docs</a></p>
            </div>
            
            <h3>🔧 功能操作</h3>
            <button class="btn" onclick="testAPI()">测试API连接</button>
            <button class="btn" onclick="startTraining()">启动训练任务</button>
            <button class="btn" onclick="listJobs()">查看训练任务</button>
            <button class="btn" onclick="getSystemInfo()">系统信息</button>

            <div id="result" style="margin-top: 20px;"></div>

            <h3>📊 训练任务列表</h3>
            <div id="jobsList" style="margin-top: 10px;"></div>
            
            <h3>📊 系统信息</h3>
            <div class="status">
                <p><strong>Python环境:</strong> pytorch_env</p>
                <p><strong>框架:</strong> FastAPI + PyTorch</p>
                <p><strong>功能:</strong> 车牌识别模型训练</p>
            </div>
        </div>
        
        <script>
            async function testAPI() {
                try {
                    const response = await fetch('/api/test');
                    const result = await response.json();
                    showResult('success', 'API测试成功: ' + result.message);
                } catch (error) {
                    showResult('error', 'API测试失败: ' + error.message);
                }
            }

            async function startTraining() {
                try {
                    const config = {
                        experiment_name: "Web界面训练-" + new Date().toLocaleTimeString(),
                        model_type: "resnet18",
                        batch_size: 32,
                        learning_rate: 0.001,
                        epochs: 5,
                        dataset_size: 500
                    };

                    const response = await fetch('/api/training/start', {
                        method: 'POST',
                        headers: {'Content-Type': 'application/json'},
                        body: JSON.stringify(config)
                    });

                    const result = await response.json();
                    showResult('success', '训练任务已启动，任务ID: ' + result.job_id);

                    // 自动刷新任务列表
                    setTimeout(listJobs, 1000);

                } catch (error) {
                    showResult('error', '启动训练失败: ' + error.message);
                }
            }

            async function listJobs() {
                try {
                    const response = await fetch('/api/training/jobs');
                    const result = await response.json();

                    let html = '';
                    if (result.jobs.length === 0) {
                        html = '<p style="color: #666;">暂无训练任务</p>';
                    } else {
                        result.jobs.forEach(job => {
                            const statusColor = getStatusColor(job.status);
                            html += `
                                <div style="border: 1px solid #ddd; padding: 15px; margin: 10px 0; border-radius: 8px; background: #f9f9f9;">
                                    <div style="display: flex; justify-content: space-between; align-items: center;">
                                        <h4 style="margin: 0; color: #333;">${job.config.experiment_name}</h4>
                                        <span style="background: ${statusColor}; color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px;">
                                            ${job.status.toUpperCase()}
                                        </span>
                                    </div>
                                    <p style="margin: 5px 0; color: #666;">任务ID: ${job.job_id}</p>
                                    <p style="margin: 5px 0; color: #666;">进度: ${job.progress}% (${job.current_epoch}/${job.config.epochs})</p>
                                    <p style="margin: 5px 0; color: #666;">验证准确率: ${job.val_acc} (最佳: ${job.best_acc})</p>
                                    <p style="margin: 5px 0; color: #666;">${job.message}</p>
                                    ${job.status === 'running' ?
                                        `<button class="btn" onclick="stopTraining('${job.job_id}')" style="background: #dc3545;">停止训练</button>` :
                                        `<button class="btn" onclick="deleteJob('${job.job_id}')" style="background: #6c757d;">删除任务</button>`
                                    }
                                </div>
                            `;
                        });
                    }

                    document.getElementById('jobsList').innerHTML = html;

                } catch (error) {
                    showResult('error', '获取任务列表失败: ' + error.message);
                }
            }

            async function getSystemInfo() {
                try {
                    const response = await fetch('/api/status');
                    const result = await response.json();

                    let info = `
                        <strong>系统信息:</strong><br>
                        PyTorch: ${result.pytorch_version}<br>
                        设备: ${result.device}<br>
                        CUDA: ${result.cuda_available ? '可用' : '不可用'}<br>
                        CPU使用率: ${result.system_info.cpu_usage}<br>
                        内存使用率: ${result.system_info.memory_usage}<br>
                        可用内存: ${result.system_info.memory_available}<br>
                        Python版本: ${result.system_info.python_version}
                    `;

                    showResult('info', info);

                } catch (error) {
                    showResult('error', '获取系统信息失败: ' + error.message);
                }
            }

            async function stopTraining(jobId) {
                try {
                    const response = await fetch(`/api/training/stop/${jobId}`, {method: 'POST'});
                    const result = await response.json();
                    showResult('success', result.message);
                    setTimeout(listJobs, 1000);
                } catch (error) {
                    showResult('error', '停止训练失败: ' + error.message);
                }
            }

            async function deleteJob(jobId) {
                if (confirm('确定要删除这个训练任务吗？')) {
                    try {
                        const response = await fetch(`/api/training/delete/${jobId}`, {method: 'DELETE'});
                        const result = await response.json();
                        showResult('success', result.message);
                        setTimeout(listJobs, 1000);
                    } catch (error) {
                        showResult('error', '删除任务失败: ' + error.message);
                    }
                }
            }

            function showResult(type, message) {
                const colors = {
                    'success': '#d4edda',
                    'error': '#f8d7da',
                    'info': '#d1ecf1'
                };
                const textColors = {
                    'success': '#155724',
                    'error': '#721c24',
                    'info': '#0c5460'
                };

                document.getElementById('result').innerHTML =
                    `<div style="background: ${colors[type]}; padding: 10px; border-radius: 5px; color: ${textColors[type]};">
                        ${message}
                    </div>`;
            }

            function getStatusColor(status) {
                const colors = {
                    'pending': '#ffc107',
                    'running': '#007bff',
                    'completed': '#28a745',
                    'failed': '#dc3545',
                    'stopped': '#6c757d'
                };
                return colors[status] || '#6c757d';
            }

            // 自动刷新任务列表
            setInterval(listJobs, 5000);

            // 页面加载时获取任务列表
            window.onload = function() {
                listJobs();
            };
        </script>
    </body>
    </html>
    """

@app.get("/api/test")
async def test_api():
    """测试API"""
    return {"message": "API服务器运行正常", "status": "success"}

@app.get("/api/status")
async def get_status():
    """获取服务器状态"""
    import torch
    import psutil
    import os

    # 获取系统信息
    memory = psutil.virtual_memory()
    cpu_percent = psutil.cpu_percent(interval=1)

    return {
        "server": "running",
        "pytorch_version": torch.__version__,
        "cuda_available": torch.cuda.is_available(),
        "device": "cuda" if torch.cuda.is_available() else "cpu",
        "system_info": {
            "cpu_usage": f"{cpu_percent}%",
            "memory_usage": f"{memory.percent}%",
            "memory_available": f"{memory.available / (1024**3):.1f}GB",
            "python_version": f"{os.sys.version.split()[0]}",
            "platform": os.name
        }
    }

# 全局变量存储训练任务
training_jobs = {}

class TrainingConfig(BaseModel):
    """训练配置"""
    experiment_name: str = "默认实验"
    model_type: str = "resnet18"
    batch_size: int = 32
    learning_rate: float = 0.001
    epochs: int = 10
    dataset_size: int = 1000

class TrainingJob(BaseModel):
    """训练任务"""
    job_id: str
    config: TrainingConfig
    status: str = "pending"  # pending, running, completed, failed, stopped
    progress: float = 0.0
    current_epoch: int = 0
    train_loss: float = 0.0
    val_loss: float = 0.0
    train_acc: float = 0.0
    val_acc: float = 0.0
    best_acc: float = 0.0
    start_time: str = ""
    end_time: str = ""
    message: str = ""

@app.post("/api/training/start")
async def start_training(config: TrainingConfig, background_tasks: BackgroundTasks):
    """启动训练任务"""
    import uuid
    from datetime import datetime

    # 生成任务ID
    job_id = str(uuid.uuid4())[:8]

    # 创建训练任务
    job = TrainingJob(
        job_id=job_id,
        config=config,
        status="pending",
        start_time=datetime.now().isoformat(),
        message="训练任务已创建，等待开始..."
    )

    training_jobs[job_id] = job

    # 在后台启动训练
    background_tasks.add_task(run_training_simulation, job_id)

    return {
        "job_id": job_id,
        "message": "训练任务已启动",
        "status": "pending"
    }

@app.get("/api/training/jobs")
async def list_training_jobs():
    """列出所有训练任务"""
    return {"jobs": list(training_jobs.values())}

@app.get("/api/training/status/{job_id}")
async def get_training_status(job_id: str):
    """获取训练状态"""
    if job_id not in training_jobs:
        raise HTTPException(status_code=404, detail="训练任务不存在")

    return training_jobs[job_id]

@app.post("/api/training/stop/{job_id}")
async def stop_training(job_id: str):
    """停止训练任务"""
    if job_id not in training_jobs:
        raise HTTPException(status_code=404, detail="训练任务不存在")

    job = training_jobs[job_id]
    if job.status == "running":
        job.status = "stopped"
        job.message = "训练已被用户停止"
        from datetime import datetime
        job.end_time = datetime.now().isoformat()

        return {"message": "训练任务已停止"}
    else:
        return {"message": f"训练任务当前状态: {job.status}，无法停止"}

@app.delete("/api/training/delete/{job_id}")
async def delete_training_job(job_id: str):
    """删除训练任务"""
    if job_id not in training_jobs:
        raise HTTPException(status_code=404, detail="训练任务不存在")

    del training_jobs[job_id]
    return {"message": "训练任务已删除"}

async def run_training_simulation(job_id: str):
    """运行训练模拟"""
    if job_id not in training_jobs:
        return

    job = training_jobs[job_id]

    try:
        # 更新状态为运行中
        job.status = "running"
        job.message = "训练正在进行中..."

        # 模拟训练过程
        for epoch in range(1, job.config.epochs + 1):
            # 检查是否被停止
            if job.status == "stopped":
                break

            # 模拟训练一个epoch
            await asyncio.sleep(2)  # 模拟训练时间

            # 生成模拟的训练指标
            train_loss = 2.0 * (0.9 ** epoch) + random.uniform(-0.1, 0.1)
            val_loss = train_loss + random.uniform(0.0, 0.2)
            train_acc = min(0.95, 0.3 + 0.7 * (1 - 0.9 ** epoch) + random.uniform(-0.05, 0.05))
            val_acc = train_acc - random.uniform(0.0, 0.1)

            # 更新任务状态
            job.current_epoch = epoch
            job.train_loss = round(train_loss, 4)
            job.val_loss = round(val_loss, 4)
            job.train_acc = round(train_acc, 4)
            job.val_acc = round(val_acc, 4)
            job.best_acc = round(max(job.best_acc, val_acc), 4)
            job.progress = round((epoch / job.config.epochs) * 100, 1)
            job.message = f"Epoch {epoch}/{job.config.epochs} - Val Acc: {val_acc:.4f}"

            print(f"Job {job_id}: Epoch {epoch}/{job.config.epochs}, Val Acc: {val_acc:.4f}")

        # 训练完成
        if job.status != "stopped":
            job.status = "completed"
            job.message = f"训练完成！最佳验证准确率: {job.best_acc:.4f}"
            job.end_time = datetime.now().isoformat()
            print(f"Job {job_id}: Training completed with best accuracy {job.best_acc:.4f}")

    except Exception as e:
        # 训练失败
        job.status = "failed"
        job.message = f"训练失败: {str(e)}"
        job.end_time = datetime.now().isoformat()
        print(f"Job {job_id}: Training failed - {e}")

def main():
    """主函数"""
    print("=" * 50)
    print("简化训练API服务器")
    print("=" * 50)
    print("启动服务器...")
    print("访问地址: http://127.0.0.1:8003")
    print("API文档: http://127.0.0.1:8003/docs")
    print("=" * 50)

    uvicorn.run(app, host="127.0.0.1", port=8003)

if __name__ == "__main__":
    main()
