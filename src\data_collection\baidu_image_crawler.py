# -*- coding: utf-8 -*-
"""
百度图片高效爬虫
Enhanced Baidu Image Crawler for License Plate Dataset

根据需求文档要求，实现高效的百度图片爬取功能
目标：获取10万张标注车牌图像，涵盖不同地区、不同类型的车牌样本
"""

import os
import re
import json
import time
import random
import hashlib
import requests
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
from urllib.parse import quote, unquote
import cv2
import numpy as np
from concurrent.futures import ThreadPoolExecutor, as_completed
from tqdm import tqdm
import threading

from ..utils.logger import LoggerMixin
from ..utils.chinese_support import setup_chinese_environment


class BaiduImageCrawler(LoggerMixin):
    """百度图片高效爬虫类"""
    
    def __init__(self, output_dir: str = "data/baidu_images", max_workers: int = 8):
        """
        初始化百度图片爬虫
        
        Args:
            output_dir (str): 输出目录
            max_workers (int): 最大并发线程数
        """
        setup_chinese_environment()
        
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        self.images_dir = self.output_dir / "images"
        self.metadata_dir = self.output_dir / "metadata"
        self.images_dir.mkdir(exist_ok=True)
        self.metadata_dir.mkdir(exist_ok=True)
        
        self.max_workers = max_workers
        self.downloaded_hashes = set()
        self.download_stats = {
            'total_attempted': 0,
            'successful_downloads': 0,
            'failed_downloads': 0,
            'duplicate_images': 0,
            'invalid_images': 0
        }

        # 线程锁
        self.stats_lock = threading.Lock()
        self.hash_lock = threading.Lock()

        # URL去重集合
        self.downloaded_urls = set()
        self.seen_urls = set()

        # 加载已下载的URL记录
        self._load_downloaded_urls()

        # 下载重试配置
        self.max_retries = 3
        self.retry_delay = 1.0

        # 动态加载配置
        self.enable_dynamic_loading = True
        self.max_pages_per_keyword = 20  # 每个关键词最多翻页数
        self.min_images_per_page = 5     # 每页最少图片数，低于此数停止翻页

        # 请求会话
        self.session = requests.Session()
        self.session.headers.update({
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1'
        })
        
        # 请求头池
        self.user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.107 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:90.0) Gecko/20100101 Firefox/90.0',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        ]
        
        # 车牌相关关键词
        self.license_plate_keywords = [
            # 中文关键词
            "车牌", "汽车牌照", "机动车牌照", "车辆牌照",
            "蓝牌车", "黄牌车", "绿牌车", "白牌车", "黑牌车",
            "新能源车牌", "电动车车牌", "混合动力车牌",
            "京牌", "沪牌", "粤牌", "川牌", "鲁牌", "苏牌", "浙牌",
            "停车场车牌", "高速公路车牌", "城市道路车牌",
            "车牌识别", "车牌检测", "车牌定位",
            
            # 英文关键词
            "license plate", "car plate", "vehicle plate", "number plate",
            "license plate recognition", "automatic number plate recognition",
            "blue license plate", "yellow license plate", "green license plate",
            "electric vehicle plate", "new energy vehicle plate",
            "parking lot license plate", "highway license plate",
            "chinese license plate", "china car plate"
        ]
        
        self.logger.info(f"百度图片爬虫初始化完成，输出目录: {self.output_dir}")
    
    def get_random_headers(self) -> Dict[str, str]:
        """获取随机请求头"""
        return {
            'User-Agent': random.choice(self.user_agents),
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Cache-Control': 'max-age=0'
        }
    
    def search_baidu_images(self, keyword: str, max_images: int = 1000) -> List[Dict[str, Any]]:
        """
        搜索百度图片获取图片URL列表

        Args:
            keyword (str): 搜索关键词
            max_images (int): 最大图片数量

        Returns:
            List[Dict[str, Any]]: 图片信息列表
        """
        self.logger.info(f"开始搜索关键词: {keyword}")

        image_urls = []
        page = 0

        consecutive_failures = 0
        max_consecutive_failures = 3

        while (len(image_urls) < max_images and
               page < self.max_pages_per_keyword and
               consecutive_failures < max_consecutive_failures):

            try:
                # 获取当前页的图片
                page_images = self._search_single_page(keyword, page)

                if not page_images:
                    consecutive_failures += 1
                    self.logger.warning(f"第 {page} 页未获取到图片，连续失败 {consecutive_failures} 次")

                    # 如果连续失败次数过多，尝试不同的搜索策略
                    if consecutive_failures >= 2:
                        # 尝试使用HTML解析
                        html_images = self._search_baidu_images_html(keyword, page)
                        if html_images:
                            page_images = html_images
                            consecutive_failures = 0
                            self.logger.info(f"HTML解析成功获取 {len(html_images)} 张图片")

                    if not page_images:
                        page += 1
                        time.sleep(random.uniform(2.0, 4.0))  # 失败时延迟更长
                        continue
                else:
                    consecutive_failures = 0

                # 去重处理
                unique_images = self._deduplicate_urls(page_images)

                if len(unique_images) < self.min_images_per_page and page > 5:
                    self.logger.info(f"第 {page} 页去重后只有 {len(unique_images)} 张图片，可能已到达搜索末尾")
                    break

                image_urls.extend(unique_images)
                self.logger.info(f"第 {page} 页: 获取 {len(page_images)} 张，去重后 {len(unique_images)} 张，累计 {len(image_urls)} 张")

                page += 1

                # 动态调整延迟时间
                if len(unique_images) > 20:
                    delay = random.uniform(0.5, 1.5)  # 获取较多时延迟较短
                elif len(unique_images) > 10:
                    delay = random.uniform(1.0, 2.5)
                else:
                    delay = random.uniform(2.0, 4.0)  # 获取较少时延迟较长

                time.sleep(delay)

            except Exception as e:
                consecutive_failures += 1
                self.logger.error(f"搜索第 {page} 页时出错: {str(e)}")

                if consecutive_failures >= max_consecutive_failures:
                    self.logger.warning(f"连续失败 {consecutive_failures} 次，停止搜索")
                    break

                page += 1
                time.sleep(random.uniform(3.0, 6.0))  # 出错时延迟更长

        self.logger.info(f"关键词 '{keyword}' 搜索完成，找到 {len(image_urls)} 张图片")
        return image_urls

    def _search_baidu_images_html(self, keyword: str, page: int = 0) -> List[Dict[str, Any]]:
        """
        通过HTML页面解析获取图片URL（备选方案）

        Args:
            keyword (str): 搜索关键词
            page (int): 页面编号

        Returns:
            List[Dict[str, Any]]: 图片信息列表
        """
        try:
            from bs4 import BeautifulSoup
            import re

            encoded_keyword = quote(keyword)
            # 使用您提供的百度图片搜索URL格式
            search_url = f"https://image.baidu.com/search/index?tn=baiduimage&ipn=r&ct=201326592&cl=2&lm=&st=-1&fm=index&fr=&hs=0&xthttps=111110&sf=1&fmq=&pv=&ic=0&nc=1&z=&se=&showtab=0&fb=0&width=&height=&face=0&istype=2&ie=utf-8&word={encoded_keyword}&pn={page * 20}"

            headers = self.get_random_headers()
            headers.update({
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                'Cache-Control': 'max-age=0'
            })

            response = requests.get(search_url, headers=headers, timeout=15)
            response.raise_for_status()

            soup = BeautifulSoup(response.text, 'html.parser')
            image_urls = []

            # 查找图片链接的多种方式（优化版）
            # 方式1: 优先查找带有data-objurl属性的img标签（基于您提供的HTML结构）
            img_tags_with_objurl = soup.find_all('img', {'data-objurl': True})
            for img in img_tags_with_objurl:
                try:
                    original_url = img.get('data-objurl', '')
                    thumb_url = img.get('src', '')
                    data_thumb_url = img.get('data-thumbnail-url', '')

                    if original_url:
                        # 清理URL中的HTML实体
                        original_url = original_url.replace('&amp;', '&')
                        thumb_url = thumb_url.replace('&amp;', '&') if thumb_url else ''
                        data_thumb_url = data_thumb_url.replace('&amp;', '&') if data_thumb_url else ''

                        image_info = {
                            'thumb_url': thumb_url or data_thumb_url,
                            'middle_url': '',
                            'original_url': original_url,
                            'title': img.get('alt', ''),
                            'source': '',
                            'width': self._parse_int(img.get('width', 0)),
                            'height': self._parse_int(img.get('height', 0)),
                            'keyword': keyword,
                            'page': page,
                            'method': 'html_objurl'
                        }
                        image_urls.append(image_info)
                except Exception as e:
                    self.logger.debug(f"解析data-objurl图片失败: {e}")
                    continue

            # 方式2: 如果方式1没找到足够图片，查找普通img标签
            if len(image_urls) < 10:
                img_tags = soup.find_all('img', {'src': True})
                for img in img_tags:
                    try:
                        src = img.get('src', '')
                        if (src and
                            any(ext in src.lower() for ext in ['.jpg', '.jpeg', '.png', '.webp', '.gif']) and
                            not src.startswith('data:') and
                            src not in [info['thumb_url'] for info in image_urls]):  # 避免重复

                            src = src.replace('&amp;', '&')
                            image_info = {
                                'thumb_url': src,
                                'middle_url': src,
                                'original_url': src,
                                'title': img.get('alt', ''),
                                'source': '',
                                'width': self._parse_int(img.get('width', 0)),
                                'height': self._parse_int(img.get('height', 0)),
                                'keyword': keyword,
                                'page': page,
                                'method': 'html_img'
                            }
                            image_urls.append(image_info)
                    except Exception as e:
                        self.logger.debug(f"解析普通img标签失败: {e}")
                        continue

            # 方式2: 从JavaScript代码中提取图片URL
            script_tags = soup.find_all('script')
            for script in script_tags:
                if script.string:
                    # 查找objURL模式
                    obj_urls = re.findall(r'"objURL":"([^"]+)"', script.string)
                    for url in obj_urls:
                        if url and ('jpg' in url or 'jpeg' in url or 'png' in url or 'webp' in url):
                            image_info = {
                                'thumb_url': '',
                                'middle_url': '',
                                'original_url': url.replace('\\/', '/'),
                                'title': '',
                                'source': '',
                                'width': 0,
                                'height': 0,
                                'keyword': keyword,
                                'page': page,
                                'method': 'html_script'
                            }
                            image_urls.append(image_info)

            self.logger.debug(f"HTML解析找到 {len(image_urls)} 张图片")
            return image_urls[:30]  # 限制每页最多30张

        except Exception as e:
            self.logger.debug(f"HTML解析失败: {str(e)}")
            return []

    def _extract_images_with_regex(self, html_content: str, keyword: str, page: int = 0) -> List[Dict[str, Any]]:
        """
        使用正则表达式从HTML内容中提取图片URL（最后的备选方案）

        Args:
            html_content (str): HTML内容
            keyword (str): 搜索关键词
            page (int): 页面编号

        Returns:
            List[Dict[str, Any]]: 图片信息列表
        """
        try:
            import re

            image_urls = []

            # 正则表达式模式列表
            patterns = [
                # 匹配objURL模式
                r'"objURL":"([^"]+)"',
                # 匹配thumbURL模式
                r'"thumbURL":"([^"]+)"',
                # 匹配middleURL模式
                r'"middleURL":"([^"]+)"',
                # 匹配src属性中的图片URL
                r'src="([^"]*(?:jpg|jpeg|png|webp|gif)[^"]*)"',
                # 匹配data-src属性
                r'data-src="([^"]*(?:jpg|jpeg|png|webp|gif)[^"]*)"',
                # 匹配百度图片特有的URL格式
                r'https?://img\d*\.baidu\.com/[^"\s]+\.(?:jpg|jpeg|png|webp|gif)',
                # 匹配其他常见图片URL
                r'https?://[^"\s]+\.(?:jpg|jpeg|png|webp|gif)'
            ]

            found_urls = set()  # 使用集合去重

            for pattern in patterns:
                matches = re.findall(pattern, html_content, re.IGNORECASE)
                for match in matches:
                    # 如果match是元组，取第一个元素
                    url = match if isinstance(match, str) else match[0] if match else ""

                    if url and url not in found_urls:
                        # 清理URL
                        url = url.replace('\\/', '/').replace('\\"', '"')

                        # 验证URL格式
                        if self._is_valid_image_url(url):
                            found_urls.add(url)

                            image_info = {
                                'thumb_url': url,
                                'middle_url': url,
                                'original_url': url,
                                'title': f'图片来自{keyword}搜索',
                                'source': 'regex_extraction',
                                'width': 0,
                                'height': 0,
                                'keyword': keyword,
                                'page': page,
                                'method': 'regex'
                            }
                            image_urls.append(image_info)

                            # 限制每页最多30张
                            if len(image_urls) >= 30:
                                break

                if len(image_urls) >= 30:
                    break

            self.logger.debug(f"正则表达式提取找到 {len(image_urls)} 张图片")
            return image_urls

        except Exception as e:
            self.logger.debug(f"正则表达式提取失败: {str(e)}")
            return []

    def _is_valid_image_url(self, url: str) -> bool:
        """
        验证图片URL是否有效

        Args:
            url (str): 图片URL

        Returns:
            bool: 是否有效
        """
        if not url or len(url) < 10:
            return False

        # 检查是否是有效的HTTP/HTTPS URL
        if not url.startswith(('http://', 'https://')):
            return False

        # 检查是否包含图片扩展名
        image_extensions = ['.jpg', '.jpeg', '.png', '.webp', '.gif']
        url_lower = url.lower()

        # 直接包含扩展名
        if any(ext in url_lower for ext in image_extensions):
            return True

        # 百度图片特殊格式
        if 'baidu.com' in url_lower and ('fm=' in url_lower or 'app=' in url_lower):
            return True

        return False
    
    def download_image(self, image_info: Dict[str, Any]) -> Optional[str]:
        """
        下载单张图片
        
        Args:
            image_info (Dict[str, Any]): 图片信息
            
        Returns:
            Optional[str]: 下载成功返回文件路径，失败返回None
        """
        with self.stats_lock:
            self.download_stats['total_attempted'] += 1
        
        # 尝试不同质量的URL
        urls_to_try = [
            image_info.get('original_url', ''),
            image_info.get('middle_url', ''),
            image_info.get('thumb_url', '')
        ]
        
        for url in urls_to_try:
            if not url:
                continue
                
            try:
                headers = self.get_random_headers()
                response = requests.get(url, headers=headers, timeout=15, stream=True)
                response.raise_for_status()
                
                # 检查内容类型
                content_type = response.headers.get('content-type', '').lower()
                if not content_type.startswith('image/'):
                    continue
                
                # 读取图片数据
                image_data = response.content
                
                # 检查图片大小
                if len(image_data) < 1024:  # 小于1KB的图片
                    continue
                
                # 计算哈希值去重
                image_hash = hashlib.md5(image_data).hexdigest()
                
                with self.hash_lock:
                    if image_hash in self.downloaded_hashes:
                        with self.stats_lock:
                            self.download_stats['duplicate_images'] += 1
                        return None
                    self.downloaded_hashes.add(image_hash)
                
                # 验证图片有效性
                if not self._validate_image_data(image_data):
                    with self.stats_lock:
                        self.download_stats['invalid_images'] += 1
                    continue
                
                # 保存图片
                file_extension = self._get_file_extension(url, content_type)
                filename = f"{image_hash}{file_extension}"
                file_path = self.images_dir / filename
                
                with open(file_path, 'wb') as f:
                    f.write(image_data)
                
                # 保存元数据
                metadata = {
                    'filename': filename,
                    'hash': image_hash,
                    'url': url,
                    'keyword': image_info.get('keyword', ''),
                    'title': image_info.get('title', ''),
                    'source': image_info.get('source', ''),
                    'width': image_info.get('width', 0),
                    'height': image_info.get('height', 0),
                    'download_time': time.time()
                }
                
                metadata_file = self.metadata_dir / f"{image_hash}.json"
                with open(metadata_file, 'w', encoding='utf-8') as f:
                    json.dump(metadata, f, ensure_ascii=False, indent=2)
                
                with self.stats_lock:
                    self.download_stats['successful_downloads'] += 1
                
                return str(file_path)
                
            except Exception as e:
                continue
        
        with self.stats_lock:
            self.download_stats['failed_downloads'] += 1
        
        return None
    
    def _validate_image_data(self, image_data: bytes) -> bool:
        """验证图片数据有效性"""
        try:
            # 使用OpenCV验证图片
            nparr = np.frombuffer(image_data, np.uint8)
            img = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
            
            if img is None:
                return False
            
            height, width = img.shape[:2]
            
            # 检查图片尺寸
            if height < 100 or width < 100:  # 太小的图片
                return False
            
            if height > 4000 or width > 4000:  # 太大的图片
                return False
            
            # 检查宽高比（车牌通常是横向的）
            aspect_ratio = width / height
            if aspect_ratio < 0.5 or aspect_ratio > 10:  # 过于极端的宽高比
                return False
            
            return True
            
        except Exception:
            return False
    
    def _get_file_extension(self, url: str, content_type: str) -> str:
        """获取文件扩展名"""
        # 从content-type获取
        if 'jpeg' in content_type or 'jpg' in content_type:
            return '.jpg'
        elif 'png' in content_type:
            return '.png'
        elif 'webp' in content_type:
            return '.webp'
        elif 'bmp' in content_type:
            return '.bmp'
        
        # 从URL获取
        if url.lower().endswith(('.jpg', '.jpeg')):
            return '.jpg'
        elif url.lower().endswith('.png'):
            return '.png'
        elif url.lower().endswith('.webp'):
            return '.webp'
        elif url.lower().endswith('.bmp'):
            return '.bmp'
        
        return '.jpg'  # 默认
    
    def crawl_license_plates(self, target_images: int = 100000, keywords: Optional[List[str]] = None) -> Dict[str, int]:
        """
        爬取车牌图片
        
        Args:
            target_images (int): 目标图片数量
            keywords (Optional[List[str]]): 自定义关键词列表
            
        Returns:
            Dict[str, int]: 爬取统计信息
        """
        if keywords is None:
            keywords = self.license_plate_keywords
        
        self.logger.info(f"开始爬取车牌图片，目标数量: {target_images}")
        
        # 重置统计信息
        self.download_stats = {
            'total_attempted': 0,
            'successful_downloads': 0,
            'failed_downloads': 0,
            'duplicate_images': 0,
            'invalid_images': 0
        }
        
        all_image_infos = []
        
        # 搜索所有关键词
        images_per_keyword = max(target_images // len(keywords), 100)
        
        for keyword in keywords:
            if self.download_stats['successful_downloads'] >= target_images:
                break
            
            image_infos = self.search_baidu_images(keyword, images_per_keyword)
            all_image_infos.extend(image_infos)
            
            # 随机打乱顺序
            random.shuffle(all_image_infos)
        
        self.logger.info(f"搜索完成，共找到 {len(all_image_infos)} 张候选图片")
        
        # 并发下载图片
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # 提交下载任务
            future_to_info = {
                executor.submit(self.download_image, info): info 
                for info in all_image_infos[:target_images * 2]  # 多搜索一些以应对失败
            }
            
            # 使用进度条显示下载进度
            with tqdm(total=min(len(future_to_info), target_images), desc="下载图片") as pbar:
                for future in as_completed(future_to_info):
                    if self.download_stats['successful_downloads'] >= target_images:
                        # 取消剩余任务
                        for f in future_to_info:
                            f.cancel()
                        break
                    
                    try:
                        result = future.result()
                        if result:
                            pbar.update(1)
                    except Exception as e:
                        self.logger.debug(f"下载任务异常: {e}")
        
        # 保存统计信息
        stats_file = self.output_dir / "download_stats.json"
        with open(stats_file, 'w', encoding='utf-8') as f:
            json.dump(self.download_stats, f, ensure_ascii=False, indent=2)
        
        self.logger.info(f"爬取完成！统计信息: {self.download_stats}")
        return self.download_stats

    def _load_downloaded_urls(self):
        """加载已下载的URL记录"""
        try:
            url_record_file = self.metadata_dir / "downloaded_urls.txt"
            if url_record_file.exists():
                with open(url_record_file, 'r', encoding='utf-8') as f:
                    for line in f:
                        url = line.strip()
                        if url:
                            self.downloaded_urls.add(url)
                self.logger.info(f"加载了 {len(self.downloaded_urls)} 个已下载URL记录")
        except Exception as e:
            self.logger.warning(f"加载URL记录失败: {str(e)}")

    def _save_downloaded_url(self, url: str):
        """保存已下载的URL"""
        try:
            if url not in self.downloaded_urls:
                self.downloaded_urls.add(url)
                url_record_file = self.metadata_dir / "downloaded_urls.txt"
                with open(url_record_file, 'a', encoding='utf-8') as f:
                    f.write(f"{url}\n")
        except Exception as e:
            self.logger.warning(f"保存URL记录失败: {str(e)}")

    def _is_url_downloaded(self, url: str) -> bool:
        """检查URL是否已下载"""
        return url in self.downloaded_urls

    def _deduplicate_urls(self, image_urls: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """去除重复的URL"""
        unique_urls = []
        seen_in_batch = set()

        for img_info in image_urls:
            # 获取所有可能的URL
            urls_to_check = [
                img_info.get('original_url', ''),
                img_info.get('middle_url', ''),
                img_info.get('thumb_url', '')
            ]

            # 检查是否有任何URL已被处理
            is_duplicate = False
            for url in urls_to_check:
                if url and (url in self.seen_urls or url in seen_in_batch or self._is_url_downloaded(url)):
                    is_duplicate = True
                    break

            if not is_duplicate:
                # 标记所有URL为已见过
                for url in urls_to_check:
                    if url:
                        self.seen_urls.add(url)
                        seen_in_batch.add(url)
                unique_urls.append(img_info)

        if len(image_urls) > len(unique_urls):
            self.logger.info(f"去重: {len(image_urls)} -> {len(unique_urls)} 张图片")

        return unique_urls

    def _search_single_page(self, keyword: str, page: int) -> List[Dict[str, Any]]:
        """
        搜索单页图片

        Args:
            keyword (str): 搜索关键词
            page (int): 页面编号

        Returns:
            List[Dict[str, Any]]: 图片信息列表
        """
        try:
            encoded_keyword = quote(keyword)

            # 使用用户提供的百度图片搜索URL格式
            search_url = (
                f"https://image.baidu.com/search/acjson?"
                f"tn=resultjson_com&logid=&ipn=rj&ct=201326592&is=&fp=result&"
                f"queryWord={encoded_keyword}&cl=2&lm=-1&ie=utf-8&oe=utf-8&"
                f"word={encoded_keyword}&pn={page * 30}&rn=30"
            )

            self.logger.debug(f"搜索URL: {search_url}")

            headers = self.get_random_headers()
            headers.update({
                'Referer': f'https://image.baidu.com/search/index?tn=baiduimage&ipn=r&ct=201326592&cl=2&lm=&st=-1&fm=index&fr=&hs=0&xthttps=111110&sf=1&fmq=&pv=&ic=0&nc=1&z=&se=&showtab=0&fb=0&width=&height=&face=0&istype=2&ie=utf-8&word={encoded_keyword}',
                'Accept': 'application/json, text/javascript, */*; q=0.01',
                'X-Requested-With': 'XMLHttpRequest'
            })

            response = self.session.get(search_url, headers=headers, timeout=15)
            response.raise_for_status()

            # 解析JSON响应
            return self._parse_json_response(response.text, keyword, page)

        except Exception as e:
            self.logger.debug(f"单页搜索失败: {str(e)}")
            return []

    def _parse_json_response(self, response_text: str, keyword: str, page: int) -> List[Dict[str, Any]]:
        """
        解析JSON响应（增强版）

        Args:
            response_text (str): 响应文本
            keyword (str): 搜索关键词
            page (int): 页面编号

        Returns:
            List[Dict[str, Any]]: 图片信息列表
        """
        try:
            import re

            # 清理响应文本
            cleaned_text = response_text

            # 移除控制字符（除了换行符、制表符、回车符）
            cleaned_text = re.sub(r'[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]', '', cleaned_text)

            # 修复常见的转义字符问题
            cleaned_text = cleaned_text.replace('\\/', '/')
            # 修复无效的转义序列
            cleaned_text = re.sub(r'\\(?!["\\/bfnrt]|u[0-9a-fA-F]{4})', r'\\\\', cleaned_text)
            # 移除可能的BOM标记
            cleaned_text = cleaned_text.lstrip('\ufeff')

            # 尝试多种方式解析JSON
            data = None

            # 方法1: 直接解析
            try:
                data = json.loads(cleaned_text)
            except json.JSONDecodeError:
                # 方法2: 使用更宽松的解析
                try:
                    # 替换可能有问题的字符
                    ultra_cleaned = re.sub(r'[\x00-\x1f\x7f-\x9f]', '', cleaned_text)
                    data = json.loads(ultra_cleaned)
                except json.JSONDecodeError:
                    # 方法3: 尝试提取JSON部分
                    json_match = re.search(r'\{.*\}', cleaned_text, re.DOTALL)
                    if json_match:
                        json_str = json_match.group()
                        data = json.loads(json_str)
                    else:
                        raise json.JSONDecodeError("无法找到有效的JSON数据", cleaned_text, 0)

            if not data or 'data' not in data or not data['data']:
                return []

            image_urls = []
            for item in data['data']:
                # 检查必要字段
                if not isinstance(item, dict):
                    continue

                thumb_url = item.get('thumbURL', '')
                middle_url = item.get('middleURL', '')
                original_url = item.get('objURL', '')

                if thumb_url or middle_url or original_url:
                    image_info = {
                        'thumb_url': thumb_url,
                        'middle_url': middle_url,
                        'original_url': original_url,
                        'title': item.get('fromPageTitle', ''),
                        'source': item.get('fromPageTitleEnc', ''),
                        'width': item.get('width', 0),
                        'height': item.get('height', 0),
                        'keyword': keyword,
                        'page': page,
                        'method': 'json'
                    }
                    image_urls.append(image_info)

            return image_urls

        except Exception as e:
            self.logger.debug(f"JSON解析失败: {str(e)}")
            return []

    def _parse_int(self, value) -> int:
        """安全解析整数"""
        try:
            return int(value) if value else 0
        except (ValueError, TypeError):
            return 0


def main():
    """主函数，用于测试爬虫"""
    crawler = BaiduImageCrawler(output_dir="data/baidu_license_plates")
    
    # 爬取1000张图片进行测试
    stats = crawler.crawl_license_plates(target_images=1000)
    
    print("爬取统计:")
    for key, value in stats.items():
        print(f"  {key}: {value}")


if __name__ == "__main__":
    main()
