# -*- coding: utf-8 -*-
"""
数据集生成脚本
Dataset Generator for License Plate Recognition

根据需求文档要求，将采集到的数据处理后生成训练和测试数据集
目标：构建包含10万张标注车牌图像的数据集，涵盖不同地区、不同类型的车牌样本
"""

import os
import json
import shutil
import random
from pathlib import Path
from typing import List, Dict, Any, Tuple, Optional
import cv2
import numpy as np
from tqdm import tqdm
import yaml

from ..utils.logger import LoggerMixin
from ..utils.chinese_support import setup_chinese_environment, validate_chinese_plate


class LicensePlateDatasetGenerator(LoggerMixin):
    """车牌数据集生成器"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化数据集生成器
        
        Args:
            config (Dict[str, Any]): 配置字典
        """
        setup_chinese_environment()
        
        self.config = config
        self.data_config = config.get('data', {})
        
        # 路径配置
        self.raw_data_path = Path(self.data_config.get('raw_data_path', 'data/raw'))
        self.processed_data_path = Path(self.data_config.get('processed_data_path', 'data/processed'))
        self.dataset_path = Path(self.data_config.get('dataset_path', 'data/datasets'))
        
        # 数据集分割比例
        self.train_ratio = self.data_config.get('train_ratio', 0.8)
        self.val_ratio = self.data_config.get('val_ratio', 0.1)
        self.test_ratio = self.data_config.get('test_ratio', 0.1)
        
        # 创建目录
        self.processed_data_path.mkdir(parents=True, exist_ok=True)
        self.dataset_path.mkdir(parents=True, exist_ok=True)
        
        # 数据集子目录
        self.train_dir = self.dataset_path / "train"
        self.val_dir = self.dataset_path / "val"
        self.test_dir = self.dataset_path / "test"
        
        for dir_path in [self.train_dir, self.val_dir, self.test_dir]:
            dir_path.mkdir(exist_ok=True)
            (dir_path / "images").mkdir(exist_ok=True)
            (dir_path / "annotations").mkdir(exist_ok=True)
        
        # 车牌类型配置
        self.plate_types = {
            'blue': {'name': '蓝牌', 'color_range': [(100, 50, 50), (130, 255, 255)]},
            'yellow': {'name': '黄牌', 'color_range': [(20, 50, 50), (30, 255, 255)]},
            'green': {'name': '绿牌', 'color_range': [(40, 50, 50), (80, 255, 255)]},
            'white': {'name': '白牌', 'color_range': [(0, 0, 200), (180, 30, 255)]},
            'black': {'name': '黑牌', 'color_range': [(0, 0, 0), (180, 255, 50)]}
        }
        
        self.logger.info("数据集生成器初始化完成")
    
    def analyze_raw_data(self) -> Dict[str, Any]:
        """
        分析原始数据
        
        Returns:
            Dict[str, Any]: 数据分析结果
        """
        self.logger.info("开始分析原始数据")
        
        analysis_result = {
            'total_images': 0,
            'valid_images': 0,
            'invalid_images': 0,
            'image_formats': {},
            'image_sizes': [],
            'data_sources': {},
            'estimated_plate_types': {}
        }
        
        # 遍历所有数据源
        data_sources = [
            self.raw_data_path,
            Path("data/baidu_license_plates/images"),
            Path("data/real_data/images"),
            Path("data/ccpd_data/images")
        ]
        
        for source_dir in data_sources:
            if not source_dir.exists():
                continue
            
            source_name = source_dir.name
            analysis_result['data_sources'][source_name] = 0
            
            # 遍历图像文件
            for image_file in source_dir.rglob("*"):
                if image_file.is_file() and image_file.suffix.lower() in ['.jpg', '.jpeg', '.png', '.bmp', '.webp']:
                    analysis_result['total_images'] += 1
                    analysis_result['data_sources'][source_name] += 1
                    
                    # 分析图像格式
                    format_key = image_file.suffix.lower()
                    analysis_result['image_formats'][format_key] = analysis_result['image_formats'].get(format_key, 0) + 1
                    
                    # 验证图像有效性
                    if self._validate_image_file(image_file):
                        analysis_result['valid_images'] += 1
                        
                        # 获取图像尺寸
                        try:
                            img = cv2.imread(str(image_file))
                            if img is not None:
                                height, width = img.shape[:2]
                                analysis_result['image_sizes'].append((width, height))
                                
                                # 估计车牌类型
                                plate_type = self._estimate_plate_type(img)
                                if plate_type:
                                    analysis_result['estimated_plate_types'][plate_type] = analysis_result['estimated_plate_types'].get(plate_type, 0) + 1
                        except Exception:
                            pass
                    else:
                        analysis_result['invalid_images'] += 1
        
        # 计算统计信息
        if analysis_result['image_sizes']:
            widths, heights = zip(*analysis_result['image_sizes'])
            analysis_result['size_stats'] = {
                'avg_width': np.mean(widths),
                'avg_height': np.mean(heights),
                'min_width': min(widths),
                'max_width': max(widths),
                'min_height': min(heights),
                'max_height': max(heights)
            }
        
        self.logger.info(f"数据分析完成: {analysis_result['total_images']} 张图像，{analysis_result['valid_images']} 张有效")
        return analysis_result
    
    def _validate_image_file(self, image_path: Path) -> bool:
        """验证图像文件有效性"""
        try:
            img = cv2.imread(str(image_path))
            if img is None:
                return False
            
            height, width = img.shape[:2]
            
            # 检查尺寸
            if height < 50 or width < 50:
                return False
            
            if height > 5000 or width > 5000:
                return False
            
            return True
            
        except Exception:
            return False
    
    def _estimate_plate_type(self, image: np.ndarray) -> Optional[str]:
        """估计车牌类型"""
        try:
            # 转换到HSV色彩空间
            hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
            
            # 检测每种车牌颜色
            max_pixels = 0
            detected_type = None
            
            for plate_type, config in self.plate_types.items():
                lower, upper = config['color_range']
                mask = cv2.inRange(hsv, np.array(lower), np.array(upper))
                pixel_count = cv2.countNonZero(mask)
                
                if pixel_count > max_pixels:
                    max_pixels = pixel_count
                    detected_type = plate_type
            
            # 如果检测到的像素数量足够多，返回类型
            if max_pixels > image.shape[0] * image.shape[1] * 0.1:  # 至少10%的像素
                return detected_type
            
            return None
            
        except Exception:
            return None
    
    def process_images(self, target_count: int = 100000) -> Dict[str, Any]:
        """
        处理图像数据
        
        Args:
            target_count (int): 目标图像数量
            
        Returns:
            Dict[str, Any]: 处理结果
        """
        self.logger.info(f"开始处理图像数据，目标数量: {target_count}")
        
        # 收集所有有效图像
        all_images = []
        
        data_sources = [
            self.raw_data_path,
            Path("data/baidu_license_plates/images"),
            Path("data/real_data/images"),
            Path("data/ccpd_data/images")
        ]
        
        for source_dir in data_sources:
            if not source_dir.exists():
                continue
            
            for image_file in source_dir.rglob("*"):
                if image_file.is_file() and image_file.suffix.lower() in ['.jpg', '.jpeg', '.png', '.bmp', '.webp']:
                    if self._validate_image_file(image_file):
                        all_images.append(image_file)
        
        self.logger.info(f"找到 {len(all_images)} 张有效图像")
        
        # 随机选择目标数量的图像
        if len(all_images) > target_count:
            selected_images = random.sample(all_images, target_count)
        else:
            selected_images = all_images
        
        # 处理选中的图像
        processed_count = 0
        processing_stats = {
            'total_processed': 0,
            'successful_processed': 0,
            'failed_processed': 0,
            'plate_types': {}
        }
        
        with tqdm(total=len(selected_images), desc="处理图像") as pbar:
            for image_path in selected_images:
                try:
                    # 处理单张图像
                    result = self._process_single_image(image_path, processed_count)
                    
                    if result:
                        processing_stats['successful_processed'] += 1
                        plate_type = result.get('plate_type', 'unknown')
                        processing_stats['plate_types'][plate_type] = processing_stats['plate_types'].get(plate_type, 0) + 1
                        processed_count += 1
                    else:
                        processing_stats['failed_processed'] += 1
                    
                    processing_stats['total_processed'] += 1
                    pbar.update(1)
                    
                except Exception as e:
                    self.logger.debug(f"处理图像失败 {image_path}: {e}")
                    processing_stats['failed_processed'] += 1
                    processing_stats['total_processed'] += 1
                    pbar.update(1)
        
        self.logger.info(f"图像处理完成: {processing_stats}")
        return processing_stats
    
    def _process_single_image(self, image_path: Path, index: int) -> Optional[Dict[str, Any]]:
        """处理单张图像"""
        try:
            # 读取图像
            img = cv2.imread(str(image_path))
            if img is None:
                return None
            
            # 图像预处理
            processed_img = self._preprocess_image(img)
            
            # 估计车牌类型
            plate_type = self._estimate_plate_type(processed_img)
            
            # 生成新文件名
            new_filename = f"plate_{index:06d}.jpg"
            output_path = self.processed_data_path / new_filename
            
            # 保存处理后的图像
            cv2.imwrite(str(output_path), processed_img)
            
            # 创建标注信息
            annotation = {
                'filename': new_filename,
                'original_path': str(image_path),
                'plate_type': plate_type or 'unknown',
                'width': processed_img.shape[1],
                'height': processed_img.shape[0],
                'processed': True
            }
            
            # 保存标注文件
            annotation_path = self.processed_data_path / f"plate_{index:06d}.json"
            with open(annotation_path, 'w', encoding='utf-8') as f:
                json.dump(annotation, f, ensure_ascii=False, indent=2)
            
            return annotation
            
        except Exception as e:
            self.logger.debug(f"处理图像失败: {e}")
            return None
    
    def _preprocess_image(self, image: np.ndarray) -> np.ndarray:
        """图像预处理"""
        # 调整图像大小
        target_height = 480
        height, width = image.shape[:2]
        target_width = int(width * target_height / height)
        
        if target_width > 640:
            target_width = 640
            target_height = int(height * target_width / width)
        
        resized = cv2.resize(image, (target_width, target_height))
        
        # 图像增强
        # 1. 对比度增强
        lab = cv2.cvtColor(resized, cv2.COLOR_BGR2LAB)
        l, a, b = cv2.split(lab)
        clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
        l = clahe.apply(l)
        enhanced = cv2.merge([l, a, b])
        enhanced = cv2.cvtColor(enhanced, cv2.COLOR_LAB2BGR)
        
        # 2. 去噪
        denoised = cv2.bilateralFilter(enhanced, 9, 75, 75)
        
        return denoised
    
    def split_dataset(self) -> Dict[str, Any]:
        """
        分割数据集为训练集、验证集和测试集
        
        Returns:
            Dict[str, Any]: 分割结果
        """
        self.logger.info("开始分割数据集")
        
        # 获取所有处理后的图像
        processed_images = list(self.processed_data_path.glob("plate_*.jpg"))
        processed_annotations = list(self.processed_data_path.glob("plate_*.json"))
        
        if len(processed_images) != len(processed_annotations):
            self.logger.warning("图像和标注文件数量不匹配")
        
        # 配对图像和标注
        image_annotation_pairs = []
        for img_path in processed_images:
            ann_path = img_path.with_suffix('.json')
            if ann_path.exists():
                image_annotation_pairs.append((img_path, ann_path))
        
        # 随机打乱
        random.shuffle(image_annotation_pairs)
        
        # 计算分割点
        total_count = len(image_annotation_pairs)
        train_count = int(total_count * self.train_ratio)
        val_count = int(total_count * self.val_ratio)
        
        # 分割数据
        train_pairs = image_annotation_pairs[:train_count]
        val_pairs = image_annotation_pairs[train_count:train_count + val_count]
        test_pairs = image_annotation_pairs[train_count + val_count:]
        
        # 复制文件到对应目录
        split_stats = {}
        
        for split_name, pairs, target_dir in [
            ('train', train_pairs, self.train_dir),
            ('val', val_pairs, self.val_dir),
            ('test', test_pairs, self.test_dir)
        ]:
            split_stats[split_name] = len(pairs)
            
            for img_path, ann_path in tqdm(pairs, desc=f"复制{split_name}数据"):
                # 复制图像
                shutil.copy2(img_path, target_dir / "images" / img_path.name)
                # 复制标注
                shutil.copy2(ann_path, target_dir / "annotations" / ann_path.name)
        
        # 生成数据集配置文件
        dataset_config = {
            'name': 'license_plate_dataset',
            'version': '1.0',
            'description': '基于深度学习的车牌识别数据集',
            'total_images': total_count,
            'splits': split_stats,
            'classes': list(self.plate_types.keys()) + ['unknown'],
            'image_format': 'jpg',
            'annotation_format': 'json'
        }
        
        config_path = self.dataset_path / "dataset_config.yaml"
        with open(config_path, 'w', encoding='utf-8') as f:
            yaml.dump(dataset_config, f, default_flow_style=False, allow_unicode=True)
        
        self.logger.info(f"数据集分割完成: {split_stats}")
        return split_stats
    
    def generate_dataset(self, target_count: int = 100000) -> Dict[str, Any]:
        """
        生成完整数据集
        
        Args:
            target_count (int): 目标图像数量
            
        Returns:
            Dict[str, Any]: 生成结果
        """
        self.logger.info("开始生成数据集")
        
        # 1. 分析原始数据
        analysis_result = self.analyze_raw_data()
        
        # 2. 处理图像
        processing_result = self.process_images(target_count)
        
        # 3. 分割数据集
        split_result = self.split_dataset()
        
        # 4. 生成最终报告
        final_result = {
            'analysis': analysis_result,
            'processing': processing_result,
            'split': split_result,
            'dataset_path': str(self.dataset_path),
            'success': True
        }
        
        # 保存生成报告
        report_path = self.dataset_path / "generation_report.json"
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(final_result, f, ensure_ascii=False, indent=2, default=str)
        
        self.logger.info("数据集生成完成")
        return final_result


def main():
    """主函数"""
    # 加载配置
    from ..utils.config_loader import load_config
    config = load_config("config/config.yaml")
    
    # 创建数据集生成器
    generator = LicensePlateDatasetGenerator(config)
    
    # 生成数据集
    result = generator.generate_dataset(target_count=10000)  # 测试用较小数量
    
    print("数据集生成结果:")
    print(f"  数据集路径: {result['dataset_path']}")
    print(f"  训练集: {result['split']['train']} 张")
    print(f"  验证集: {result['split']['val']} 张")
    print(f"  测试集: {result['split']['test']} 张")


if __name__ == "__main__":
    main()
