# -*- coding: utf-8 -*-
"""
真实车牌数据爬虫模块
从公开数据源获取真实车牌图像，支持多车牌场景
"""

import os
import sys
import requests
import json
import time
import random
from typing import List, Dict, Optional, Tuple
from urllib.parse import urljoin, urlparse
import hashlib
from pathlib import Path
import cv2
import numpy as np
from bs4 import BeautifulSoup
import logging

# 设置中文编码支持
import locale
try:
    locale.setlocale(locale.LC_ALL, 'zh_CN.UTF-8')
except:
    try:
        locale.setlocale(locale.LC_ALL, 'Chinese_China.936')
    except:
        pass

class RealDataCrawler:
    """真实车牌数据爬虫"""
    
    def __init__(self, output_dir: str = "data/real_images"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # 创建子目录
        self.images_dir = self.output_dir / "images"
        self.annotations_dir = self.output_dir / "annotations"
        self.images_dir.mkdir(exist_ok=True)
        self.annotations_dir.mkdir(exist_ok=True)
        
        # 设置请求头
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        }
        
        # 设置日志
        self.setup_logging()
        
        # 数据源配置
        self.data_sources = {
            'github_datasets': [
                'https://github.com/detectRecog/CCPD',  # CCPD数据集
                'https://github.com/zhubenfu/License-Plate-Detect-Recognition-via-Deep-Neural-Networks-accuracy-up-to-99.9',
            ],
            'kaggle_datasets': [
                'https://www.kaggle.com/datasets/andrewmvd/car-plate-detection',
                'https://www.kaggle.com/datasets/aslanahmedov/number-plate-detection',
            ],
            'roboflow_datasets': [
                'https://universe.roboflow.com/brad-dwyer/license-plate-recognition-rxg4e',
                'https://universe.roboflow.com/roboflow-universe-projects/license-plate-recognition',
            ]
        }
        
        self.session = requests.Session()
        self.session.headers.update(self.headers)
        
    def setup_logging(self):
        """设置日志系统"""
        log_file = self.output_dir / "crawler.log"
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        self.logger = logging.getLogger(__name__)
        
    def download_image(self, url: str, filename: str) -> bool:
        """下载图像文件"""
        try:
            response = self.session.get(url, timeout=30)
            response.raise_for_status()
            
            # 检查是否为图像
            content_type = response.headers.get('content-type', '')
            if not content_type.startswith('image/'):
                self.logger.warning(f"URL不是图像文件: {url}")
                return False
            
            # 保存图像
            image_path = self.images_dir / filename
            with open(image_path, 'wb') as f:
                f.write(response.content)
            
            # 验证图像是否有效
            img = cv2.imread(str(image_path))
            if img is None:
                os.remove(image_path)
                self.logger.warning(f"无效图像文件: {filename}")
                return False
            
            self.logger.info(f"成功下载图像: {filename}")
            return True
            
        except Exception as e:
            self.logger.error(f"下载图像失败 {url}: {str(e)}")
            return False
    
    def crawl_github_ccpd(self) -> List[str]:
        """爬取CCPD数据集信息"""
        self.logger.info("开始爬取CCPD数据集...")
        
        # CCPD数据集的下载链接
        ccpd_urls = [
            "https://github.com/detectRecog/CCPD/releases/download/v1.0/ccpd_base.tar.bz2",
            "https://github.com/detectRecog/CCPD/releases/download/v1.0/ccpd_challenge.tar.bz2",
            "https://github.com/detectRecog/CCPD/releases/download/v1.0/ccpd_db.tar.bz2",
            "https://github.com/detectRecog/CCPD/releases/download/v1.0/ccpd_fn.tar.bz2",
            "https://github.com/detectRecog/CCPD/releases/download/v1.0/ccpd_np.tar.bz2",
            "https://github.com/detectRecog/CCPD/releases/download/v1.0/ccpd_rotate.tar.bz2",
            "https://github.com/detectRecog/CCPD/releases/download/v1.0/ccpd_tilt.tar.bz2",
            "https://github.com/detectRecog/CCPD/releases/download/v1.0/ccpd_weather.tar.bz2"
        ]
        
        downloaded_files = []
        for url in ccpd_urls:
            filename = url.split('/')[-1]
            file_path = self.output_dir / filename
            
            if file_path.exists():
                self.logger.info(f"文件已存在: {filename}")
                downloaded_files.append(str(file_path))
                continue
            
            try:
                self.logger.info(f"下载CCPD数据集: {filename}")
                response = self.session.get(url, stream=True, timeout=300)
                response.raise_for_status()
                
                total_size = int(response.headers.get('content-length', 0))
                downloaded_size = 0
                
                with open(file_path, 'wb') as f:
                    for chunk in response.iter_content(chunk_size=8192):
                        if chunk:
                            f.write(chunk)
                            downloaded_size += len(chunk)
                            if total_size > 0:
                                progress = (downloaded_size / total_size) * 100
                                print(f"\r下载进度: {progress:.1f}%", end='', flush=True)
                
                print()  # 换行
                downloaded_files.append(str(file_path))
                self.logger.info(f"成功下载: {filename}")
                
                # 添加延迟避免被限制
                time.sleep(random.uniform(1, 3))
                
            except Exception as e:
                self.logger.error(f"下载失败 {url}: {str(e)}")
        
        return downloaded_files
    
    def extract_ccpd_dataset(self, archive_path: str) -> List[str]:
        """解压CCPD数据集"""
        import tarfile
        
        try:
            self.logger.info(f"解压数据集: {archive_path}")
            extract_dir = self.output_dir / "ccpd_extracted"
            extract_dir.mkdir(exist_ok=True)
            
            with tarfile.open(archive_path, 'r:bz2') as tar:
                tar.extractall(extract_dir)
            
            # 查找解压后的图像文件
            image_files = []
            for ext in ['*.jpg', '*.jpeg', '*.png', '*.bmp']:
                image_files.extend(extract_dir.rglob(ext))
            
            self.logger.info(f"解压完成，找到 {len(image_files)} 个图像文件")
            return [str(f) for f in image_files]
            
        except Exception as e:
            self.logger.error(f"解压失败 {archive_path}: {str(e)}")
            return []
    
    def parse_ccpd_filename(self, filename: str) -> Optional[Dict]:
        """解析CCPD文件名获取标注信息"""
        try:
            # CCPD文件名格式: 025-95_113-154&383_386&473-386&473_177&454_154&383_363&402-0_0_22_27_27_33_16-37-15.jpg
            # 格式说明: Area-AG_AG-BB&BB_BB&BB-BB&BB_BB&BB_BB&BB_BB&BB-LP-brightness-blurriness.jpg
            
            basename = os.path.splitext(filename)[0]
            parts = basename.split('-')
            
            if len(parts) < 7:
                return None
            
            # 解析边界框坐标
            bbox_str = parts[2]
            coords = bbox_str.split('_')
            if len(coords) != 4:
                return None
            
            # 解析四个角点
            corners = []
            for coord in coords:
                x, y = map(int, coord.split('&'))
                corners.append([x, y])
            
            # 解析车牌字符
            plate_str = parts[4]
            plate_chars = plate_str.split('_')
            
            # CCPD字符映射
            provinces = ["皖", "沪", "津", "渝", "冀", "晋", "蒙", "辽", "吉", "黑", "苏", "浙", "京", "闽", "赣", "鲁", "豫", "鄂", "湘", "粤", "桂", "琼", "川", "贵", "云", "藏", "陕", "甘", "青", "宁", "新", "警", "学", "O"]
            alphabets = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'J', 'K', 'L', 'M', 'N', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z', 'O']
            ads = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'J', 'K', 'L', 'M', 'N', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z', '0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'O']
            
            plate_text = ""
            if len(plate_chars) >= 7:
                plate_text += provinces[int(plate_chars[0])]  # 省份
                plate_text += alphabets[int(plate_chars[1])]  # 字母
                for i in range(2, 7):
                    plate_text += ads[int(plate_chars[i])]  # 数字/字母
            
            return {
                'filename': filename,
                'bbox': corners,
                'plate_text': plate_text,
                'brightness': int(parts[5]) if len(parts) > 5 else 0,
                'blurriness': int(parts[6]) if len(parts) > 6 else 0
            }
            
        except Exception as e:
            self.logger.warning(f"解析文件名失败 {filename}: {str(e)}")
            return None
    
    def create_annotation_file(self, image_info: Dict, image_path: str):
        """创建标注文件"""
        try:
            # 读取图像获取尺寸
            img = cv2.imread(image_path)
            if img is None:
                return
            
            height, width = img.shape[:2]
            
            # 创建YOLO格式标注
            annotation = {
                'image_path': image_path,
                'image_size': [width, height],
                'objects': [{
                    'class': 'license_plate',
                    'bbox': image_info['bbox'],
                    'plate_text': image_info['plate_text']
                }]
            }
            
            # 保存标注文件
            annotation_file = self.annotations_dir / f"{Path(image_path).stem}.json"
            with open(annotation_file, 'w', encoding='utf-8') as f:
                json.dump(annotation, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            self.logger.error(f"创建标注文件失败: {str(e)}")
    
    def crawl_real_data(self, max_images: int = 1000) -> Dict[str, int]:
        """爬取真实车牌数据"""
        self.logger.info(f"开始爬取真实车牌数据，目标数量: {max_images}")
        
        stats = {
            'downloaded': 0,
            'processed': 0,
            'failed': 0
        }
        
        # 1. 下载CCPD数据集
        ccpd_files = self.crawl_github_ccpd()
        
        # 2. 处理下载的数据集
        for archive_file in ccpd_files:
            if stats['processed'] >= max_images:
                break
                
            # 解压数据集
            image_files = self.extract_ccpd_dataset(archive_file)
            
            # 处理图像文件
            for image_file in image_files:
                if stats['processed'] >= max_images:
                    break
                
                # 解析文件名获取标注
                filename = os.path.basename(image_file)
                image_info = self.parse_ccpd_filename(filename)
                
                if image_info is None:
                    stats['failed'] += 1
                    continue
                
                # 复制图像到目标目录
                target_path = self.images_dir / filename
                try:
                    import shutil
                    shutil.copy2(image_file, target_path)
                    
                    # 创建标注文件
                    self.create_annotation_file(image_info, str(target_path))
                    
                    stats['processed'] += 1
                    stats['downloaded'] += 1
                    
                    if stats['processed'] % 100 == 0:
                        self.logger.info(f"已处理 {stats['processed']} 个图像")
                        
                except Exception as e:
                    self.logger.error(f"处理图像失败 {filename}: {str(e)}")
                    stats['failed'] += 1
        
        self.logger.info(f"数据爬取完成: {stats}")
        return stats

def main():
    """主函数"""
    print("=== 真实车牌数据爬虫 ===")
    
    crawler = RealDataCrawler()
    
    # 爬取数据
    stats = crawler.crawl_real_data(max_images=500)
    
    print(f"\n爬取结果:")
    print(f"- 成功下载: {stats['downloaded']} 个图像")
    print(f"- 处理完成: {stats['processed']} 个图像")
    print(f"- 失败数量: {stats['failed']} 个图像")
    
    print(f"\n数据保存位置:")
    print(f"- 图像目录: {crawler.images_dir}")
    print(f"- 标注目录: {crawler.annotations_dir}")

if __name__ == "__main__":
    main()
