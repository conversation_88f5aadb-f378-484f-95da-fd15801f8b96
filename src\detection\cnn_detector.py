#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于CNN的车牌检测器
CNN-based License Plate Detector

Author: License Plate Recognition Team
Date: 2025-07-30
"""

import cv2
import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from pathlib import Path
from typing import Dict, Any, List, Tuple, Optional
import torchvision.transforms as transforms

from .plate_detector import PlateDetector
from ..utils.logger import LoggerMixin


class PlateDetectionCNN(nn.Module):
    """车牌检测CNN网络"""
    
    def __init__(self, input_channels: int = 3, num_classes: int = 2):
        """
        初始化CNN网络
        
        Args:
            input_channels (int): 输入通道数
            num_classes (int): 类别数 (背景 + 车牌)
        """
        super(PlateDetectionCNN, self).__init__()
        
        # 特征提取层
        self.features = nn.Sequential(
            # 第一个卷积块
            nn.Conv2d(input_channels, 32, kernel_size=3, padding=1),
            nn.BatchNorm2d(32),
            nn.ReLU(inplace=True),
            nn.Conv2d(32, 32, kernel_size=3, padding=1),
            nn.BatchNorm2d(32),
            nn.ReLU(inplace=True),
            nn.MaxPool2d(kernel_size=2, stride=2),
            
            # 第二个卷积块
            nn.Conv2d(32, 64, kernel_size=3, padding=1),
            nn.BatchNorm2d(64),
            nn.ReLU(inplace=True),
            nn.Conv2d(64, 64, kernel_size=3, padding=1),
            nn.BatchNorm2d(64),
            nn.ReLU(inplace=True),
            nn.MaxPool2d(kernel_size=2, stride=2),
            
            # 第三个卷积块
            nn.Conv2d(64, 128, kernel_size=3, padding=1),
            nn.BatchNorm2d(128),
            nn.ReLU(inplace=True),
            nn.Conv2d(128, 128, kernel_size=3, padding=1),
            nn.BatchNorm2d(128),
            nn.ReLU(inplace=True),
            nn.MaxPool2d(kernel_size=2, stride=2),
            
            # 第四个卷积块
            nn.Conv2d(128, 256, kernel_size=3, padding=1),
            nn.BatchNorm2d(256),
            nn.ReLU(inplace=True),
            nn.Conv2d(256, 256, kernel_size=3, padding=1),
            nn.BatchNorm2d(256),
            nn.ReLU(inplace=True),
        )
        
        # 分类头
        self.classifier = nn.Sequential(
            nn.Conv2d(256, 128, kernel_size=3, padding=1),
            nn.BatchNorm2d(128),
            nn.ReLU(inplace=True),
            nn.Conv2d(128, num_classes, kernel_size=1),
        )
        
        # 边界框回归头
        self.bbox_regressor = nn.Sequential(
            nn.Conv2d(256, 128, kernel_size=3, padding=1),
            nn.BatchNorm2d(128),
            nn.ReLU(inplace=True),
            nn.Conv2d(128, 4, kernel_size=1),  # 4个坐标值
        )
    
    def forward(self, x):
        """
        前向传播
        
        Args:
            x (torch.Tensor): 输入张量
            
        Returns:
            Tuple[torch.Tensor, torch.Tensor]: 分类结果和边界框回归结果
        """
        # 特征提取
        features = self.features(x)
        
        # 分类
        cls_output = self.classifier(features)
        
        # 边界框回归
        bbox_output = self.bbox_regressor(features)
        
        return cls_output, bbox_output


class CNNDetector(PlateDetector):
    """基于CNN的车牌检测器"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化CNN检测器
        
        Args:
            config (Dict[str, Any]): 配置字典
        """
        super().__init__(config)
        
        # 创建CNN模型
        self.cnn_model = PlateDetectionCNN()
        self.cnn_model.to(self.device)
        
        # 滑动窗口参数
        self.window_size = (128, 64)  # 车牌检测窗口大小
        self.stride = 32  # 滑动步长
        
        self.logger.info("CNN车牌检测器初始化完成")
    
    def load_model(self, model_path: str) -> bool:
        """
        加载预训练的CNN模型
        
        Args:
            model_path (str): 模型文件路径
            
        Returns:
            bool: 是否加载成功
        """
        try:
            if not Path(model_path).exists():
                self.logger.warning(f"模型文件不存在: {model_path}，使用未训练的模型")
                return False
            
            checkpoint = torch.load(model_path, map_location=self.device)
            self.cnn_model.load_state_dict(checkpoint['model_state_dict'])
            self.cnn_model.eval()
            self.logger.info(f"成功加载CNN模型: {model_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"加载CNN模型失败: {str(e)}")
            return False
    
    def detect(self, image: np.ndarray) -> List[Dict[str, Any]]:
        """
        使用CNN检测车牌
        
        Args:
            image (np.ndarray): 输入图像
            
        Returns:
            List[Dict[str, Any]]: 检测结果列表
        """
        try:
            # 预处理图像
            processed_image = self._preprocess_image(image)
            
            # 使用滑动窗口进行检测
            detections = self._sliding_window_detection(processed_image, image.shape)
            
            # 非极大值抑制
            detections = self._apply_nms(detections)
            
            return detections
            
        except Exception as e:
            import traceback
            self.logger.error(f"CNN检测失败: {str(e)}")
            self.logger.error(f"详细错误信息: {traceback.format_exc()}")
            return []
    
    def _sliding_window_detection(self, image_tensor: torch.Tensor, 
                                 original_shape: Tuple[int, int, int]) -> List[Dict[str, Any]]:
        """
        滑动窗口检测
        
        Args:
            image_tensor (torch.Tensor): 预处理后的图像张量
            original_shape (Tuple[int, int, int]): 原始图像形状
            
        Returns:
            List[Dict[str, Any]]: 检测结果
        """
        detections = []

        # 确保图像张量有正确的维度
        if len(image_tensor.shape) == 3:
            image_tensor = image_tensor.unsqueeze(0)  # 添加batch维度

        h, w = image_tensor.shape[-2:]
        window_h, window_w = self.window_size

        # 计算缩放比例
        scale_y = original_shape[0] / h
        scale_x = original_shape[1] / w

        with torch.no_grad():
            for y in range(0, h - window_h + 1, self.stride):
                for x in range(0, w - window_w + 1, self.stride):
                    # 提取窗口
                    window = image_tensor[:, :, y:y+window_h, x:x+window_w]

                    # 确保窗口在正确的设备上
                    window = window.to(self.device)

                    # CNN推理
                    cls_output, bbox_output = self.cnn_model(window)
                    
                    # 获取分类概率
                    cls_probs = F.softmax(cls_output, dim=1)
                    # 对空间维度取平均，然后获取车牌类别的概率
                    plate_prob = cls_probs[0, 1].mean().item()  # 车牌类别的概率
                    
                    # 如果置信度足够高
                    if plate_prob > self.confidence_threshold:
                        # 计算在原始图像中的坐标（简化版本，不使用边界框回归）
                        bbox_x1 = x * scale_x
                        bbox_y1 = y * scale_y
                        bbox_x2 = (x + window_w) * scale_x
                        bbox_y2 = (y + window_h) * scale_y

                        # 确保边界框在图像范围内
                        bbox_x1 = max(0, min(bbox_x1, original_shape[1]))
                        bbox_y1 = max(0, min(bbox_y1, original_shape[0]))
                        bbox_x2 = max(0, min(bbox_x2, original_shape[1]))
                        bbox_y2 = max(0, min(bbox_y2, original_shape[0]))

                        detection = {
                            'bbox': [bbox_x1, bbox_y1, bbox_x2, bbox_y2],
                            'confidence': plate_prob,
                            'class': 'license_plate'
                        }
                        detections.append(detection)
        
        return detections
    
    def _apply_nms(self, detections: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        应用非极大值抑制
        
        Args:
            detections (List[Dict[str, Any]]): 检测结果
            
        Returns:
            List[Dict[str, Any]]: NMS后的检测结果
        """
        if not detections:
            return []
        
        # 转换为torch张量
        boxes = torch.tensor([det['bbox'] for det in detections], dtype=torch.float32)
        scores = torch.tensor([det['confidence'] for det in detections], dtype=torch.float32)
        
        # 应用NMS
        try:
            keep_indices = torch.ops.torchvision.nms(boxes, scores, self.nms_threshold)
        except:
            # 如果torchvision.nms不可用，使用简单的NMS实现
            keep_indices = self._simple_nms(boxes, scores, self.nms_threshold)
        
        # 返回保留的检测结果
        return [detections[i] for i in keep_indices.tolist()]

    def _simple_nms(self, boxes: torch.Tensor, scores: torch.Tensor, iou_threshold: float) -> torch.Tensor:
        """
        简单的NMS实现

        Args:
            boxes (torch.Tensor): 边界框
            scores (torch.Tensor): 置信度分数
            iou_threshold (float): IoU阈值

        Returns:
            torch.Tensor: 保留的索引
        """
        # 按分数排序
        sorted_indices = torch.argsort(scores, descending=True)
        keep = []

        while len(sorted_indices) > 0:
            # 选择分数最高的框
            current = sorted_indices[0]
            keep.append(current.item())

            if len(sorted_indices) == 1:
                break

            # 计算IoU
            current_box = boxes[current]
            remaining_boxes = boxes[sorted_indices[1:]]

            ious = self._calculate_iou(current_box.unsqueeze(0), remaining_boxes)

            # 保留IoU小于阈值的框
            mask = ious < iou_threshold
            sorted_indices = sorted_indices[1:][mask]

        return torch.tensor(keep, dtype=torch.long)

    def _calculate_iou(self, box1: torch.Tensor, box2: torch.Tensor) -> torch.Tensor:
        """
        计算IoU

        Args:
            box1 (torch.Tensor): 第一个边界框
            box2 (torch.Tensor): 第二个边界框

        Returns:
            torch.Tensor: IoU值
        """
        # 计算交集
        x1 = torch.max(box1[:, 0], box2[:, 0])
        y1 = torch.max(box1[:, 1], box2[:, 1])
        x2 = torch.min(box1[:, 2], box2[:, 2])
        y2 = torch.min(box1[:, 3], box2[:, 3])

        intersection = torch.clamp(x2 - x1, min=0) * torch.clamp(y2 - y1, min=0)

        # 计算并集
        area1 = (box1[:, 2] - box1[:, 0]) * (box1[:, 3] - box1[:, 1])
        area2 = (box2[:, 2] - box2[:, 0]) * (box2[:, 3] - box2[:, 1])
        union = area1 + area2 - intersection

        # 计算IoU
        iou = intersection / (union + 1e-6)
        return iou
    
    def train_model(self, train_loader, val_loader, num_epochs: int = 50):
        """
        训练CNN模型
        
        Args:
            train_loader: 训练数据加载器
            val_loader: 验证数据加载器
            num_epochs (int): 训练轮数
        """
        # 定义损失函数和优化器
        criterion_cls = nn.CrossEntropyLoss()
        criterion_bbox = nn.SmoothL1Loss()
        optimizer = torch.optim.Adam(self.cnn_model.parameters(), lr=0.001)
        scheduler = torch.optim.lr_scheduler.StepLR(optimizer, step_size=20, gamma=0.1)
        
        self.cnn_model.train()
        
        for epoch in range(num_epochs):
            total_loss = 0
            for batch_idx, (data, cls_targets, bbox_targets) in enumerate(train_loader):
                data = data.to(self.device)
                cls_targets = cls_targets.to(self.device)
                bbox_targets = bbox_targets.to(self.device)
                
                optimizer.zero_grad()
                
                # 前向传播
                cls_output, bbox_output = self.cnn_model(data)
                
                # 计算损失
                cls_loss = criterion_cls(cls_output, cls_targets)
                bbox_loss = criterion_bbox(bbox_output, bbox_targets)
                total_loss_batch = cls_loss + bbox_loss
                
                # 反向传播
                total_loss_batch.backward()
                optimizer.step()
                
                total_loss += total_loss_batch.item()
            
            scheduler.step()
            
            avg_loss = total_loss / len(train_loader)
            self.logger.info(f'Epoch [{epoch+1}/{num_epochs}], Loss: {avg_loss:.4f}')
            
            # 验证
            if val_loader and (epoch + 1) % 5 == 0:
                self._validate_model(val_loader, criterion_cls, criterion_bbox)
    
    def _validate_model(self, val_loader, criterion_cls, criterion_bbox):
        """验证模型"""
        self.cnn_model.eval()
        total_loss = 0
        
        with torch.no_grad():
            for data, cls_targets, bbox_targets in val_loader:
                data = data.to(self.device)
                cls_targets = cls_targets.to(self.device)
                bbox_targets = bbox_targets.to(self.device)
                
                cls_output, bbox_output = self.cnn_model(data)
                
                cls_loss = criterion_cls(cls_output, cls_targets)
                bbox_loss = criterion_bbox(bbox_output, bbox_targets)
                total_loss += (cls_loss + bbox_loss).item()
        
        avg_val_loss = total_loss / len(val_loader)
        self.logger.info(f'Validation Loss: {avg_val_loss:.4f}')
        
        self.cnn_model.train()


def create_cnn_detector(config: Dict[str, Any]) -> CNNDetector:
    """
    创建CNN车牌检测器
    
    Args:
        config (Dict[str, Any]): 配置字典
        
    Returns:
        CNNDetector: 检测器实例
    """
    return CNNDetector(config)
