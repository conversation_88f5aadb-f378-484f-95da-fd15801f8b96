# -*- coding: utf-8 -*-
"""
改进的YOLO车牌检测器
使用预训练的YOLOv8模型进行车牌检测，支持多车牌场景，解决密集预测框问题
"""

import os
import sys
import cv2
import numpy as np
import torch
import torch.nn as nn
from typing import List, Dict, Tuple, Optional
from pathlib import Path
import logging
import json

# 设置中文编码支持
import locale
try:
    locale.setlocale(locale.LC_ALL, 'zh_CN.UTF-8')
except:
    try:
        locale.setlocale(locale.LC_ALL, 'Chinese_China.936')
    except:
        pass

class ImprovedYOLODetector:
    """改进的YOLO车牌检测器 - 解决密集预测框问题"""
    
    def __init__(self, model_path: Optional[str] = None, confidence_threshold: float = 0.7):
        self.confidence_threshold = confidence_threshold  # 提高置信度阈值
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.model = None
        self.input_size = (640, 640)
        self.iou_threshold = 0.3  # NMS IoU阈值
        
        # 设置日志
        self.logger = logging.getLogger(__name__)
        
        # 尝试加载模型
        self.load_model(model_path)
        
    def load_model(self, model_path: Optional[str] = None):
        """加载YOLO模型"""
        try:
            # 尝试使用ultralytics YOLOv8
            try:
                from ultralytics import YOLO
                
                if model_path and os.path.exists(model_path):
                    self.model = YOLO(model_path)
                    self.logger.info(f"加载自定义模型: {model_path}")
                else:
                    # 使用预训练模型
                    self.model = YOLO('yolov8n.pt')
                    self.logger.info("加载YOLOv8预训练模型")
                
                self.model_type = 'ultralytics'
                return
                
            except ImportError:
                self.logger.warning("ultralytics未安装，尝试使用torch hub")
            
            # 尝试使用torch hub YOLOv5
            try:
                self.model = torch.hub.load('ultralytics/yolov5', 'yolov5s', pretrained=True)
                self.model.to(self.device)
                self.model_type = 'torch_hub'
                self.logger.info("加载YOLOv5模型")
                return
                
            except Exception as e:
                self.logger.warning(f"torch hub加载失败: {e}")
            
            # 使用自定义轻量级检测器
            self.model = self.create_lightweight_detector()
            self.model_type = 'custom'
            self.logger.info("使用自定义轻量级检测器")
            
        except Exception as e:
            self.logger.error(f"模型加载失败: {e}")
            raise
    
    def create_lightweight_detector(self):
        """创建轻量级检测器"""
        class LightweightDetector(nn.Module):
            def __init__(self, num_classes=1):
                super().__init__()
                
                # 使用更深的网络结构
                self.backbone = nn.Sequential(
                    # 第一层
                    nn.Conv2d(3, 32, 3, 2, 1),
                    nn.BatchNorm2d(32),
                    nn.ReLU(inplace=True),
                    
                    # 第二层
                    nn.Conv2d(32, 64, 3, 2, 1),
                    nn.BatchNorm2d(64),
                    nn.ReLU(inplace=True),
                    
                    # 第三层
                    nn.Conv2d(64, 128, 3, 2, 1),
                    nn.BatchNorm2d(128),
                    nn.ReLU(inplace=True),
                    
                    # 第四层
                    nn.Conv2d(128, 256, 3, 2, 1),
                    nn.BatchNorm2d(256),
                    nn.ReLU(inplace=True),
                    
                    # 第五层
                    nn.Conv2d(256, 512, 3, 2, 1),
                    nn.BatchNorm2d(512),
                    nn.ReLU(inplace=True),
                )
                
                # 全局平均池化
                self.global_pool = nn.AdaptiveAvgPool2d((1, 1))
                
                # 分类头
                self.classifier = nn.Sequential(
                    nn.Linear(512, 256),
                    nn.ReLU(inplace=True),
                    nn.Dropout(0.5),
                    nn.Linear(256, 1),  # 二分类：是否为车牌
                    nn.Sigmoid()
                )
                
                # 回归头
                self.regressor = nn.Sequential(
                    nn.Linear(512, 256),
                    nn.ReLU(inplace=True),
                    nn.Dropout(0.5),
                    nn.Linear(256, 4),  # [x, y, w, h]
                    nn.Sigmoid()
                )
                
            def forward(self, x):
                features = self.backbone(x)
                pooled = self.global_pool(features).flatten(1)
                
                confidence = self.classifier(pooled)
                bbox = self.regressor(pooled)
                
                return confidence, bbox
        
        model = LightweightDetector()
        model.to(self.device)
        return model
    
    def preprocess_image(self, image: np.ndarray) -> torch.Tensor:
        """预处理图像"""
        self.original_shape = image.shape[:2]
        
        # 调整尺寸并保持宽高比
        h, w = image.shape[:2]
        scale = min(self.input_size[0] / w, self.input_size[1] / h)
        new_w, new_h = int(w * scale), int(h * scale)
        
        resized = cv2.resize(image, (new_w, new_h))
        
        # 创建填充图像
        padded = np.full((self.input_size[1], self.input_size[0], 3), 114, dtype=np.uint8)
        
        # 计算填充位置
        dx = (self.input_size[0] - new_w) // 2
        dy = (self.input_size[1] - new_h) // 2
        
        padded[dy:dy+new_h, dx:dx+new_w] = resized
        
        # 归一化
        normalized = padded.astype(np.float32) / 255.0
        
        # 转换为tensor
        tensor = torch.from_numpy(normalized).permute(2, 0, 1).unsqueeze(0)
        return tensor.to(self.device)
    
    def sliding_window_detect(self, image: np.ndarray) -> List[Dict]:
        """滑动窗口检测 - 用于自定义模型"""
        detections = []
        
        # 滑动窗口参数
        window_sizes = [(128, 64), (160, 80), (192, 96)]  # 不同尺寸的窗口
        stride = 32
        
        h, w = image.shape[:2]
        
        for window_w, window_h in window_sizes:
            for y in range(0, h - window_h + 1, stride):
                for x in range(0, w - window_w + 1, stride):
                    # 提取窗口
                    window = image[y:y+window_h, x:x+window_w]
                    
                    # 预处理窗口
                    window_tensor = self.preprocess_image(window)
                    
                    # 推理
                    with torch.no_grad():
                        confidence, bbox = self.model(window_tensor)
                    
                    conf_score = confidence.item()
                    
                    if conf_score > self.confidence_threshold:
                        # 转换边界框坐标
                        bbox_np = bbox.cpu().numpy()[0]
                        
                        # 相对坐标转绝对坐标
                        cx = x + bbox_np[0] * window_w
                        cy = y + bbox_np[1] * window_h
                        w_box = bbox_np[2] * window_w
                        h_box = bbox_np[3] * window_h
                        
                        x1 = int(cx - w_box/2)
                        y1 = int(cy - h_box/2)
                        x2 = int(cx + w_box/2)
                        y2 = int(cy + h_box/2)
                        
                        detections.append({
                            'bbox': [x1, y1, x2, y2],
                            'confidence': conf_score,
                            'class': 'license_plate'
                        })
        
        return detections
    
    def postprocess_detections(self, predictions, original_shape: Tuple[int, int]) -> List[Dict]:
        """后处理检测结果"""
        detections = []
        
        if self.model_type == 'ultralytics':
            # ultralytics YOLO格式
            for result in predictions:
                boxes = result.boxes
                if boxes is not None:
                    for i in range(len(boxes)):
                        conf = float(boxes.conf[i])
                        if conf >= self.confidence_threshold:
                            # 获取边界框坐标
                            x1, y1, x2, y2 = boxes.xyxy[i].cpu().numpy()
                            
                            detections.append({
                                'bbox': [int(x1), int(y1), int(x2), int(y2)],
                                'confidence': conf,
                                'class': 'license_plate'
                            })
        
        elif self.model_type == 'torch_hub':
            # torch hub YOLOv5格式
            results = predictions.pandas().xyxy[0]
            for _, row in results.iterrows():
                if row['confidence'] >= self.confidence_threshold:
                    x1, y1, x2, y2 = int(row['xmin']), int(row['ymin']), int(row['xmax']), int(row['ymax'])
                    
                    detections.append({
                        'bbox': [x1, y1, x2, y2],
                        'confidence': row['confidence'],
                        'class': 'license_plate'
                    })
        
        return detections
    
    def apply_advanced_nms(self, detections: List[Dict]) -> List[Dict]:
        """应用高级非极大值抑制"""
        if len(detections) <= 1:
            return detections
        
        # 按置信度排序
        detections.sort(key=lambda x: x['confidence'], reverse=True)
        
        # 转换为numpy数组便于计算
        boxes = np.array([det['bbox'] for det in detections])
        scores = np.array([det['confidence'] for det in detections])
        
        # 计算面积
        areas = (boxes[:, 2] - boxes[:, 0]) * (boxes[:, 3] - boxes[:, 1])
        
        # NMS
        keep = []
        indices = np.arange(len(detections))
        
        while len(indices) > 0:
            # 选择置信度最高的
            current = indices[0]
            keep.append(current)
            
            if len(indices) == 1:
                break
            
            # 计算IoU
            current_box = boxes[current]
            other_boxes = boxes[indices[1:]]
            
            # 计算交集
            x1 = np.maximum(current_box[0], other_boxes[:, 0])
            y1 = np.maximum(current_box[1], other_boxes[:, 1])
            x2 = np.minimum(current_box[2], other_boxes[:, 2])
            y2 = np.minimum(current_box[3], other_boxes[:, 3])
            
            intersection = np.maximum(0, x2 - x1) * np.maximum(0, y2 - y1)
            
            # 计算并集
            current_area = areas[current]
            other_areas = areas[indices[1:]]
            union = current_area + other_areas - intersection
            
            # 计算IoU
            iou = intersection / union
            
            # 保留IoU小于阈值的框
            indices = indices[1:][iou <= self.iou_threshold]
        
        return [detections[i] for i in keep]
    
    def detect(self, image: np.ndarray) -> List[Dict]:
        """检测车牌"""
        try:
            if self.model_type == 'custom':
                # 使用滑动窗口检测
                detections = self.sliding_window_detect(image)
            else:
                # 使用YOLO模型
                input_tensor = self.preprocess_image(image)
                
                with torch.no_grad():
                    if self.model_type == 'ultralytics':
                        predictions = self.model(image)  # 直接传入原图
                    else:
                        predictions = self.model(input_tensor)
                
                detections = self.postprocess_detections(predictions, self.original_shape)
            
            # 应用高级NMS
            filtered_detections = self.apply_advanced_nms(detections)
            
            self.logger.info(f"检测到 {len(filtered_detections)} 个车牌 (原始: {len(detections)})")
            return filtered_detections
            
        except Exception as e:
            self.logger.error(f"检测失败: {e}")
            return []
    
    def visualize_detections(self, image: np.ndarray, detections: List[Dict]) -> np.ndarray:
        """可视化检测结果"""
        result_image = image.copy()
        
        for i, detection in enumerate(detections):
            bbox = detection['bbox']
            confidence = detection['confidence']
            
            # 使用不同颜色区分不同的检测框
            colors = [(0, 255, 0), (255, 0, 0), (0, 0, 255), (255, 255, 0), (255, 0, 255)]
            color = colors[i % len(colors)]
            
            # 绘制边界框
            cv2.rectangle(result_image, (bbox[0], bbox[1]), (bbox[2], bbox[3]), color, 3)
            
            # 绘制置信度标签
            label = f"车牌{i+1}: {confidence:.3f}"
            
            # 使用中文字体（如果可用）
            try:
                # 尝试使用PIL绘制中文
                from PIL import Image, ImageDraw, ImageFont
                
                pil_image = Image.fromarray(cv2.cvtColor(result_image, cv2.COLOR_BGR2RGB))
                draw = ImageDraw.Draw(pil_image)
                
                # 尝试加载中文字体
                try:
                    font = ImageFont.truetype("simhei.ttf", 20)
                except:
                    font = ImageFont.load_default()
                
                draw.text((bbox[0], bbox[1] - 25), label, fill=color, font=font)
                result_image = cv2.cvtColor(np.array(pil_image), cv2.COLOR_RGB2BGR)
                
            except ImportError:
                # 使用OpenCV绘制
                cv2.putText(result_image, label,
                           (bbox[0], bbox[1] - 10),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.7, color, 2)
        
        return result_image

def create_improved_yolo_detector(config: Dict) -> ImprovedYOLODetector:
    """创建改进的YOLO检测器"""
    model_path = config.get('model_path')
    confidence_threshold = config.get('confidence_threshold', 0.7)
    
    return ImprovedYOLODetector(model_path, confidence_threshold)
