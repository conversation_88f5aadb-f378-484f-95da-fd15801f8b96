#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
车牌检测主程序
License Plate Detection Main Program

Author: License Plate Recognition Team
Date: 2025-07-30
"""

import argparse
import cv2
import numpy as np
from pathlib import Path
from typing import Dict, Any, List, Optional
import os
import sys
import time

from .plate_detector import create_detector
from .yolo_detector import create_yolo_detector
from .cnn_detector import create_cnn_detector
from ..utils.logger import LoggerMixin
from ..utils.config_loader import load_config
from ..utils.chinese_support import setup_chinese_environment

# 尝试导入改进的YOLO检测器
try:
    from .improved_yolo_detector import create_improved_yolo_detector
    IMPROVED_YOLO_AVAILABLE = True
except ImportError:
    IMPROVED_YOLO_AVAILABLE = False


class DetectionManager(LoggerMixin):
    """车牌检测管理器"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化管理器

        Args:
            config (Dict[str, Any]): 配置字典
        """
        # 设置中文环境
        setup_chinese_environment()

        self.config = config
        self.detection_config = config.get('model', {}).get('detection', {})

        # 创建检测器
        detector_type = self.detection_config.get('model_name', 'improved_yolo')

        # 优先使用改进的YOLO检测器
        if detector_type == 'improved_yolo' and IMPROVED_YOLO_AVAILABLE:
            try:
                self.detector = create_improved_yolo_detector(self.detection_config)
                self.logger.info("使用改进的YOLO检测器")
            except Exception as e:
                self.logger.warning(f"改进YOLO检测器创建失败: {e}")
                self.logger.info("回退到原YOLO检测器")
                detector_type = 'yolo_v8'

        if detector_type.startswith('yolo') and detector_type != 'improved_yolo':
            version = detector_type.split('_')[-1] if '_' in detector_type else 'v5'
            self.detector = create_yolo_detector(config, version)
            self.logger.info(f"使用YOLO{version}检测器")
        elif detector_type == 'cnn':
            self.detector = create_cnn_detector(config)
            self.logger.info("使用CNN检测器")
        elif not hasattr(self, 'detector'):
            # 如果还没有创建检测器，使用默认检测器
            self.detector = create_detector(config, detector_type)
            self.logger.info(f"使用{detector_type}检测器")

        # 加载模型
        model_path = self._get_model_path()
        if model_path and hasattr(self.detector, 'load_model'):
            try:
                self.detector.load_model(model_path)
                self.logger.info(f"加载模型: {model_path}")
            except Exception as e:
                self.logger.warning(f"模型加载失败: {e}")

        self.logger.info("车牌检测管理器初始化完成")
    
    def _get_model_path(self) -> Optional[str]:
        """
        获取模型路径
        
        Returns:
            Optional[str]: 模型路径
        """
        model_save_path = Path(self.config.get('output', {}).get('model_save_path', 'models/saved_models'))
        detector_type = self.detection_config.get('model_name', 'simple')
        
        # 查找模型文件
        model_files = list(model_save_path.glob(f"{detector_type}*.pt"))
        model_files.extend(list(model_save_path.glob(f"{detector_type}*.pth")))
        
        if model_files:
            # 按修改时间排序，选择最新的模型
            model_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)
            return str(model_files[0])
        
        return None
    
    def detect_from_image(self, image_path: str, output_path: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        从图像文件中检测车牌
        
        Args:
            image_path (str): 图像文件路径
            output_path (Optional[str]): 输出图像路径
            
        Returns:
            List[Dict[str, Any]]: 检测结果
        """
        try:
            # 读取图像
            image = cv2.imread(image_path)
            if image is None:
                self.logger.error(f"无法读取图像: {image_path}")
                return []
            
            # 检测车牌
            start_time = time.time()
            detections = self.detector.detect(image)
            end_time = time.time()
            
            # 计算处理时间
            process_time = end_time - start_time
            self.logger.info(f"检测完成，耗时: {process_time:.3f}秒")
            self.logger.info(f"检测到 {len(detections)} 个车牌")
            
            # 可视化结果
            if output_path:
                result_image = self.detector.visualize_detections(image, detections)
                cv2.imwrite(output_path, result_image)
                self.logger.info(f"结果已保存到: {output_path}")
            
            return detections
            
        except Exception as e:
            self.logger.error(f"检测失败: {str(e)}")
            return []
    
    def detect_from_video(self, video_path: str, output_path: Optional[str] = None) -> Dict[str, Any]:
        """
        从视频文件中检测车牌
        
        Args:
            video_path (str): 视频文件路径
            output_path (Optional[str]): 输出视频路径
            
        Returns:
            Dict[str, Any]: 检测结果统计
        """
        try:
            # 打开视频文件
            cap = cv2.VideoCapture(video_path)
            if not cap.isOpened():
                self.logger.error(f"无法打开视频: {video_path}")
                return {'error': '无法打开视频'}
            
            # 获取视频信息
            fps = cap.get(cv2.CAP_PROP_FPS)
            width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            
            # 创建输出视频
            writer = None
            if output_path:
                fourcc = cv2.VideoWriter_fourcc(*'mp4v')
                writer = cv2.VideoWriter(output_path, fourcc, fps, (width, height))
            
            # 处理视频
            frame_count = 0
            detection_count = 0
            total_time = 0
            
            while True:
                ret, frame = cap.read()
                if not ret:
                    break
                
                # 每隔几帧处理一次
                if frame_count % 5 == 0:
                    # 检测车牌
                    start_time = time.time()
                    detections = self.detector.detect(frame)
                    end_time = time.time()
                    
                    process_time = end_time - start_time
                    total_time += process_time
                    detection_count += len(detections)
                    
                    # 可视化结果
                    if detections:
                        frame = self.detector.visualize_detections(frame, detections)
                
                # 写入输出视频
                if writer:
                    writer.write(frame)
                
                frame_count += 1
                if frame_count % 100 == 0:
                    self.logger.info(f"已处理 {frame_count}/{total_frames} 帧")
            
            # 释放资源
            cap.release()
            if writer:
                writer.release()
            
            # 计算统计信息
            avg_time = total_time / (frame_count / 5) if frame_count > 0 else 0
            
            stats = {
                'total_frames': frame_count,
                'total_detections': detection_count,
                'avg_process_time': avg_time,
                'fps': 1 / avg_time if avg_time > 0 else 0
            }
            
            self.logger.info(f"视频处理完成，检测到 {detection_count} 个车牌")
            self.logger.info(f"平均处理时间: {avg_time:.3f}秒/帧")
            
            return stats
            
        except Exception as e:
            self.logger.error(f"视频处理失败: {str(e)}")
            return {'error': str(e)}
    
    def extract_plate_regions(self, image_path: str, output_dir: Optional[str] = None) -> List[np.ndarray]:
        """
        提取车牌区域
        
        Args:
            image_path (str): 图像文件路径
            output_dir (Optional[str]): 输出目录
            
        Returns:
            List[np.ndarray]: 车牌区域图像列表
        """
        try:
            # 读取图像
            image = cv2.imread(image_path)
            if image is None:
                self.logger.error(f"无法读取图像: {image_path}")
                return []
            
            # 检测车牌
            detections = self.detector.detect(image)
            
            # 提取车牌区域
            plate_regions = self.detector.extract_plate_regions(image, detections)
            
            # 保存车牌区域
            if output_dir and plate_regions:
                output_path = Path(output_dir)
                output_path.mkdir(parents=True, exist_ok=True)
                
                for i, plate in enumerate(plate_regions):
                    plate_path = output_path / f"plate_{i}.jpg"
                    cv2.imwrite(str(plate_path), plate)
                
                self.logger.info(f"已保存 {len(plate_regions)} 个车牌区域到 {output_dir}")
            
            return plate_regions
            
        except Exception as e:
            self.logger.error(f"提取车牌区域失败: {str(e)}")
            return []


def main():
    """主程序入口"""
    parser = argparse.ArgumentParser(description='车牌检测程序')
    parser.add_argument('--config', type=str, default='config/config.yaml', help='配置文件路径')
    parser.add_argument('--mode', type=str, choices=['image', 'video', 'extract'], default='image', help='检测模式')
    parser.add_argument('--input', type=str, required=True, help='输入文件路径')
    parser.add_argument('--output', type=str, help='输出文件路径')
    
    args = parser.parse_args()
    
    # 加载配置
    config = load_config(args.config)
    
    # 创建检测管理器
    manager = DetectionManager(config)
    
    if args.mode == 'image':
        manager.detect_from_image(args.input, args.output)
    elif args.mode == 'video':
        manager.detect_from_video(args.input, args.output)
    elif args.mode == 'extract':
        manager.extract_plate_regions(args.input, args.output)


if __name__ == "__main__":
    main()
