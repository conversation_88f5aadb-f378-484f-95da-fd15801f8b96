#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高级骨干网络实现
Advanced Backbone Networks

Author: License Plate Recognition Team
Date: 2025-08-01
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import math
from typing import Dict, Any, List, Tuple, Optional
from torchvision import models
import timm

from .base_model import BaseModel
from ..utils.logger import LoggerMixin


class MBConvBlock(nn.Module):
    """MobileNet V3 Inverted Residual Block with Squeeze-and-Excitation"""
    
    def __init__(self, in_channels: int, out_channels: int, kernel_size: int = 3,
                 stride: int = 1, expand_ratio: int = 6, se_ratio: float = 0.25,
                 drop_rate: float = 0.0):
        super(MBConvBlock, self).__init__()
        
        self.stride = stride
        self.drop_rate = drop_rate
        self.use_residual = stride == 1 and in_channels == out_channels
        
        # Expansion phase
        expanded_channels = in_channels * expand_ratio
        self.expand_conv = nn.Conv2d(in_channels, expanded_channels, 1, bias=False) if expand_ratio != 1 else None
        self.expand_bn = nn.BatchNorm2d(expanded_channels) if expand_ratio != 1 else None
        
        # Depthwise convolution
        self.depthwise_conv = nn.Conv2d(
            expanded_channels, expanded_channels, kernel_size,
            stride=stride, padding=kernel_size//2, groups=expanded_channels, bias=False
        )
        self.depthwise_bn = nn.BatchNorm2d(expanded_channels)
        
        # Squeeze-and-Excitation
        se_channels = max(1, int(in_channels * se_ratio))
        self.se_reduce = nn.Conv2d(expanded_channels, se_channels, 1)
        self.se_expand = nn.Conv2d(se_channels, expanded_channels, 1)
        
        # Pointwise convolution
        self.pointwise_conv = nn.Conv2d(expanded_channels, out_channels, 1, bias=False)
        self.pointwise_bn = nn.BatchNorm2d(out_channels)
        
        # Dropout
        self.dropout = nn.Dropout2d(drop_rate) if drop_rate > 0 else None
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        identity = x
        
        # Expansion
        if self.expand_conv is not None:
            x = F.silu(self.expand_bn(self.expand_conv(x)))
        
        # Depthwise
        x = F.silu(self.depthwise_bn(self.depthwise_conv(x)))
        
        # Squeeze-and-Excitation
        se_weight = F.adaptive_avg_pool2d(x, 1)
        se_weight = F.silu(self.se_reduce(se_weight))
        se_weight = torch.sigmoid(self.se_expand(se_weight))
        x = x * se_weight
        
        # Pointwise
        x = self.pointwise_bn(self.pointwise_conv(x))
        
        # Dropout and residual
        if self.dropout is not None:
            x = self.dropout(x)
        
        if self.use_residual:
            x = x + identity
        
        return x


class EfficientNet(BaseModel):
    """EfficientNet骨干网络实现"""
    
    def __init__(self, config: Dict[str, Any], model_name: str = 'efficientnet-b0'):
        """
        初始化EfficientNet
        
        Args:
            config (Dict[str, Any]): 配置字典
            model_name (str): 模型名称
        """
        super(EfficientNet, self).__init__(config)
        
        self.model_name = model_name
        self.num_classes = config.get('num_classes', 1000)
        
        # 构建模型
        self.build_model()
        
        # 移动到设备
        self.to_device()
        
        self.logger.info(f"EfficientNet {model_name} 初始化完成，参数数量: {self.count_parameters():,}")
    
    def build_model(self) -> None:
        """构建EfficientNet模型"""
        # 使用timm库的预训练EfficientNet
        try:
            self.backbone = timm.create_model(
                self.model_name, 
                pretrained=True, 
                features_only=True,
                out_indices=[1, 2, 3, 4]  # 输出多个特征层
            )
        except:
            # 如果timm不可用，使用torchvision的实现
            if 'b0' in self.model_name:
                self.backbone = models.efficientnet_b0(pretrained=True).features
            elif 'b1' in self.model_name:
                self.backbone = models.efficientnet_b1(pretrained=True).features
            elif 'b2' in self.model_name:
                self.backbone = models.efficientnet_b2(pretrained=True).features
            else:
                self.backbone = models.efficientnet_b0(pretrained=True).features
    
    def forward(self, x: torch.Tensor) -> Dict[str, torch.Tensor]:
        """
        前向传播
        
        Args:
            x (torch.Tensor): 输入张量 [B, C, H, W]
            
        Returns:
            Dict[str, torch.Tensor]: 多尺度特征图
        """
        features = {}
        
        if hasattr(self.backbone, 'forward_features'):
            # timm模型
            feature_list = self.backbone(x)
            for i, feat in enumerate(feature_list):
                features[f'C{i+2}'] = feat
        else:
            # torchvision模型
            x = self.backbone(x)
            features['C5'] = x
        
        return features


class RegNet(BaseModel):
    """RegNet骨干网络实现"""
    
    def __init__(self, config: Dict[str, Any], model_name: str = 'regnet_y_400mf'):
        """
        初始化RegNet
        
        Args:
            config (Dict[str, Any]): 配置字典
            model_name (str): 模型名称
        """
        super(RegNet, self).__init__(config)
        
        self.model_name = model_name
        
        # 构建模型
        self.build_model()
        
        # 移动到设备
        self.to_device()
        
        self.logger.info(f"RegNet {model_name} 初始化完成，参数数量: {self.count_parameters():,}")
    
    def build_model(self) -> None:
        """构建RegNet模型"""
        if 'y_400mf' in self.model_name:
            backbone = models.regnet_y_400mf(pretrained=True)
        elif 'y_800mf' in self.model_name:
            backbone = models.regnet_y_800mf(pretrained=True)
        elif 'y_1_6gf' in self.model_name:
            backbone = models.regnet_y_1_6gf(pretrained=True)
        else:
            backbone = models.regnet_y_400mf(pretrained=True)
        
        # 移除分类头，保留特征提取部分
        self.backbone = nn.Sequential(*list(backbone.children())[:-1])
    
    def forward(self, x: torch.Tensor) -> Dict[str, torch.Tensor]:
        """
        前向传播
        
        Args:
            x (torch.Tensor): 输入张量 [B, C, H, W]
            
        Returns:
            Dict[str, torch.Tensor]: 特征图
        """
        features = self.backbone(x)
        return {'C5': features}


class ConvNeXt(BaseModel):
    """ConvNeXt骨干网络实现"""
    
    def __init__(self, config: Dict[str, Any], model_name: str = 'convnext_tiny'):
        """
        初始化ConvNeXt
        
        Args:
            config (Dict[str, Any]): 配置字典
            model_name (str): 模型名称
        """
        super(ConvNeXt, self).__init__(config)
        
        self.model_name = model_name
        
        # 构建模型
        self.build_model()
        
        # 移动到设备
        self.to_device()
        
        self.logger.info(f"ConvNeXt {model_name} 初始化完成，参数数量: {self.count_parameters():,}")
    
    def build_model(self) -> None:
        """构建ConvNeXt模型"""
        try:
            # 使用timm库的ConvNeXt
            self.backbone = timm.create_model(
                self.model_name, 
                pretrained=True, 
                features_only=True,
                out_indices=[1, 2, 3, 4]
            )
        except:
            # 如果timm不可用，使用torchvision的实现
            if 'tiny' in self.model_name:
                backbone = models.convnext_tiny(pretrained=True)
            elif 'small' in self.model_name:
                backbone = models.convnext_small(pretrained=True)
            elif 'base' in self.model_name:
                backbone = models.convnext_base(pretrained=True)
            else:
                backbone = models.convnext_tiny(pretrained=True)
            
            self.backbone = backbone.features
    
    def forward(self, x: torch.Tensor) -> Dict[str, torch.Tensor]:
        """
        前向传播
        
        Args:
            x (torch.Tensor): 输入张量 [B, C, H, W]
            
        Returns:
            Dict[str, torch.Tensor]: 多尺度特征图
        """
        features = {}
        
        if hasattr(self.backbone, 'forward_features'):
            # timm模型
            feature_list = self.backbone(x)
            for i, feat in enumerate(feature_list):
                features[f'C{i+2}'] = feat
        else:
            # torchvision模型
            x = self.backbone(x)
            features['C5'] = x
        
        return features


def create_backbone(backbone_name: str, config: Dict[str, Any]) -> BaseModel:
    """
    创建骨干网络
    
    Args:
        backbone_name (str): 骨干网络名称
        config (Dict[str, Any]): 配置字典
        
    Returns:
        BaseModel: 骨干网络实例
    """
    if 'efficientnet' in backbone_name.lower():
        return EfficientNet(config, backbone_name)
    elif 'regnet' in backbone_name.lower():
        return RegNet(config, backbone_name)
    elif 'convnext' in backbone_name.lower():
        return ConvNeXt(config, backbone_name)
    else:
        raise ValueError(f"不支持的骨干网络: {backbone_name}")
