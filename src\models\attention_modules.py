#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
注意力机制模块
Attention Mechanisms

Author: License Plate Recognition Team
Date: 2025-08-01
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import math
from typing import Dict, Any, List, Tuple, Optional


class SEModule(nn.Module):
    """Squeeze-and-Excitation模块"""
    
    def __init__(self, channels: int, reduction: int = 16):
        """
        初始化SE模块
        
        Args:
            channels (int): 输入通道数
            reduction (int): 降维比例
        """
        super(SEModule, self).__init__()
        
        self.avg_pool = nn.AdaptiveAvgPool2d(1)
        self.fc = nn.Sequential(
            nn.Linear(channels, channels // reduction, bias=False),
            nn.ReLU(inplace=True),
            nn.Linear(channels // reduction, channels, bias=False),
            nn.Sigmoid()
        )
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        b, c, _, _ = x.size()
        y = self.avg_pool(x).view(b, c)
        y = self.fc(y).view(b, c, 1, 1)
        return x * y.expand_as(x)


class ECAModule(nn.Module):
    """Efficient Channel Attention模块"""
    
    def __init__(self, channels: int, gamma: int = 2, b: int = 1):
        """
        初始化ECA模块
        
        Args:
            channels (int): 输入通道数
            gamma (int): 参数gamma
            b (int): 参数b
        """
        super(ECAModule, self).__init__()
        
        # 计算卷积核大小
        t = int(abs((math.log(channels, 2) + b) / gamma))
        k = t if t % 2 else t + 1
        
        self.avg_pool = nn.AdaptiveAvgPool2d(1)
        self.conv = nn.Conv1d(1, 1, kernel_size=k, padding=(k - 1) // 2, bias=False)
        self.sigmoid = nn.Sigmoid()
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        # 全局平均池化
        y = self.avg_pool(x)
        
        # 1D卷积
        y = self.conv(y.squeeze(-1).transpose(-1, -2)).transpose(-1, -2).unsqueeze(-1)
        
        # Sigmoid激活
        y = self.sigmoid(y)
        
        return x * y.expand_as(x)


class CBAMModule(nn.Module):
    """Convolutional Block Attention Module"""
    
    def __init__(self, channels: int, reduction: int = 16, kernel_size: int = 7):
        """
        初始化CBAM模块
        
        Args:
            channels (int): 输入通道数
            reduction (int): 通道注意力降维比例
            kernel_size (int): 空间注意力卷积核大小
        """
        super(CBAMModule, self).__init__()
        
        # 通道注意力
        self.channel_attention = ChannelAttention(channels, reduction)
        
        # 空间注意力
        self.spatial_attention = SpatialAttention(kernel_size)
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        # 通道注意力
        x = self.channel_attention(x) * x
        
        # 空间注意力
        x = self.spatial_attention(x) * x
        
        return x


class ChannelAttention(nn.Module):
    """通道注意力模块"""
    
    def __init__(self, channels: int, reduction: int = 16):
        super(ChannelAttention, self).__init__()
        
        self.avg_pool = nn.AdaptiveAvgPool2d(1)
        self.max_pool = nn.AdaptiveMaxPool2d(1)
        
        self.fc = nn.Sequential(
            nn.Conv2d(channels, channels // reduction, 1, bias=False),
            nn.ReLU(inplace=True),
            nn.Conv2d(channels // reduction, channels, 1, bias=False)
        )
        
        self.sigmoid = nn.Sigmoid()
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        avg_out = self.fc(self.avg_pool(x))
        max_out = self.fc(self.max_pool(x))
        out = avg_out + max_out
        return self.sigmoid(out)


class SpatialAttention(nn.Module):
    """空间注意力模块"""
    
    def __init__(self, kernel_size: int = 7):
        super(SpatialAttention, self).__init__()
        
        self.conv = nn.Conv2d(2, 1, kernel_size, padding=kernel_size // 2, bias=False)
        self.sigmoid = nn.Sigmoid()
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        avg_out = torch.mean(x, dim=1, keepdim=True)
        max_out, _ = torch.max(x, dim=1, keepdim=True)
        x = torch.cat([avg_out, max_out], dim=1)
        x = self.conv(x)
        return self.sigmoid(x)


class CoordinateAttention(nn.Module):
    """坐标注意力模块"""
    
    def __init__(self, channels: int, reduction: int = 32):
        """
        初始化坐标注意力模块
        
        Args:
            channels (int): 输入通道数
            reduction (int): 降维比例
        """
        super(CoordinateAttention, self).__init__()
        
        self.pool_h = nn.AdaptiveAvgPool2d((None, 1))
        self.pool_w = nn.AdaptiveAvgPool2d((1, None))
        
        mip = max(8, channels // reduction)
        
        self.conv1 = nn.Conv2d(channels, mip, kernel_size=1, stride=1, padding=0)
        self.bn1 = nn.BatchNorm2d(mip)
        self.act = nn.SiLU()
        
        self.conv_h = nn.Conv2d(mip, channels, kernel_size=1, stride=1, padding=0)
        self.conv_w = nn.Conv2d(mip, channels, kernel_size=1, stride=1, padding=0)
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        identity = x
        
        n, c, h, w = x.size()
        
        # 水平和垂直池化
        x_h = self.pool_h(x)
        x_w = self.pool_w(x).permute(0, 1, 3, 2)
        
        # 拼接
        y = torch.cat([x_h, x_w], dim=2)
        y = self.conv1(y)
        y = self.bn1(y)
        y = self.act(y)
        
        # 分离
        x_h, x_w = torch.split(y, [h, w], dim=2)
        x_w = x_w.permute(0, 1, 3, 2)
        
        # 生成注意力权重
        a_h = self.conv_h(x_h).sigmoid()
        a_w = self.conv_w(x_w).sigmoid()
        
        # 应用注意力
        out = identity * a_w * a_h
        
        return out


class MultiHeadSelfAttention(nn.Module):
    """多头自注意力模块"""
    
    def __init__(self, dim: int, num_heads: int = 8, qkv_bias: bool = False, 
                 attn_drop: float = 0., proj_drop: float = 0.):
        """
        初始化多头自注意力
        
        Args:
            dim (int): 输入维度
            num_heads (int): 注意力头数
            qkv_bias (bool): 是否使用QKV偏置
            attn_drop (float): 注意力dropout率
            proj_drop (float): 投影dropout率
        """
        super(MultiHeadSelfAttention, self).__init__()
        
        self.num_heads = num_heads
        head_dim = dim // num_heads
        self.scale = head_dim ** -0.5
        
        self.qkv = nn.Linear(dim, dim * 3, bias=qkv_bias)
        self.attn_drop = nn.Dropout(attn_drop)
        self.proj = nn.Linear(dim, dim)
        self.proj_drop = nn.Dropout(proj_drop)
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        B, N, C = x.shape
        qkv = self.qkv(x).reshape(B, N, 3, self.num_heads, C // self.num_heads).permute(2, 0, 3, 1, 4)
        q, k, v = qkv[0], qkv[1], qkv[2]
        
        attn = (q @ k.transpose(-2, -1)) * self.scale
        attn = attn.softmax(dim=-1)
        attn = self.attn_drop(attn)
        
        x = (attn @ v).transpose(1, 2).reshape(B, N, C)
        x = self.proj(x)
        x = self.proj_drop(x)
        
        return x


class CrossAttention(nn.Module):
    """交叉注意力模块"""
    
    def __init__(self, dim: int, num_heads: int = 8, qkv_bias: bool = False,
                 attn_drop: float = 0., proj_drop: float = 0.):
        """
        初始化交叉注意力
        
        Args:
            dim (int): 输入维度
            num_heads (int): 注意力头数
            qkv_bias (bool): 是否使用QKV偏置
            attn_drop (float): 注意力dropout率
            proj_drop (float): 投影dropout率
        """
        super(CrossAttention, self).__init__()
        
        self.num_heads = num_heads
        head_dim = dim // num_heads
        self.scale = head_dim ** -0.5
        
        self.q = nn.Linear(dim, dim, bias=qkv_bias)
        self.kv = nn.Linear(dim, dim * 2, bias=qkv_bias)
        self.attn_drop = nn.Dropout(attn_drop)
        self.proj = nn.Linear(dim, dim)
        self.proj_drop = nn.Dropout(proj_drop)
    
    def forward(self, x: torch.Tensor, context: torch.Tensor) -> torch.Tensor:
        B, N, C = x.shape
        _, M, _ = context.shape
        
        q = self.q(x).reshape(B, N, self.num_heads, C // self.num_heads).permute(0, 2, 1, 3)
        kv = self.kv(context).reshape(B, M, 2, self.num_heads, C // self.num_heads).permute(2, 0, 3, 1, 4)
        k, v = kv[0], kv[1]
        
        attn = (q @ k.transpose(-2, -1)) * self.scale
        attn = attn.softmax(dim=-1)
        attn = self.attn_drop(attn)
        
        x = (attn @ v).transpose(1, 2).reshape(B, N, C)
        x = self.proj(x)
        x = self.proj_drop(x)
        
        return x


def add_attention_to_conv(conv_layer: nn.Module, attention_type: str = 'se', 
                         **kwargs) -> nn.Module:
    """
    为卷积层添加注意力机制
    
    Args:
        conv_layer (nn.Module): 卷积层
        attention_type (str): 注意力类型
        **kwargs: 注意力模块参数
        
    Returns:
        nn.Module: 带注意力的卷积层
    """
    if hasattr(conv_layer, 'out_channels'):
        channels = conv_layer.out_channels
    else:
        raise ValueError("无法确定卷积层的输出通道数")
    
    if attention_type.lower() == 'se':
        attention = SEModule(channels, **kwargs)
    elif attention_type.lower() == 'eca':
        attention = ECAModule(channels, **kwargs)
    elif attention_type.lower() == 'cbam':
        attention = CBAMModule(channels, **kwargs)
    elif attention_type.lower() == 'ca':
        attention = CoordinateAttention(channels, **kwargs)
    else:
        raise ValueError(f"不支持的注意力类型: {attention_type}")
    
    return nn.Sequential(conv_layer, attention)
