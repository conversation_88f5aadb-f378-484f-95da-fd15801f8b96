#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
集成车牌识别器
Integrated License Plate Recognizer

结合CCPD训练的字符识别模型和传统计算机视觉检测方法

Author: License Plate Recognition Team
Date: 2025-07-30
"""

import os
import sys
import cv2
import numpy as np
import torch
import torch.nn as nn
import torchvision.transforms as transforms
from typing import List, Tuple, Dict, Any, Optional
from pathlib import Path
import json
from PIL import Image, ImageDraw, ImageFont

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent))

from utils.logger import LoggerMixin


class IntegratedPlateRecognizer(LoggerMixin):
    """集成车牌识别器"""
    
    def __init__(self, model_path: Optional[str] = None):
        """
        初始化集成识别器
        
        Args:
            model_path (str, optional): 训练好的模型路径
        """
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.model = None
        self.char_mapping = {}
        self.reverse_char_mapping = {}
        
        # 加载字符映射
        self._load_character_mappings()
        
        # 加载模型
        if model_path and os.path.exists(model_path):
            self.load_model(model_path)
        
        # 图像预处理
        self.transform = transforms.Compose([
            transforms.Resize((64, 64)),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], 
                               std=[0.229, 0.224, 0.225])
        ])
        
        self.logger.info("集成车牌识别器初始化完成")
    
    def _load_character_mappings(self) -> None:
        """加载字符映射"""
        # CCPD标准字符集
        provinces = ["皖", "沪", "津", "渝", "冀", "晋", "蒙", "辽", "吉", "黑", "苏", "浙", 
                    "京", "闽", "赣", "鲁", "豫", "鄂", "湘", "粤", "桂", "琼", "川", "贵", 
                    "云", "藏", "陕", "甘", "青", "宁", "新"]
        
        alphabets = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'J', 'K', 'L', 'M', 'N', 
                    'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z']
        
        ads = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'J', 'K', 'L', 'M', 'N', 'P', 
               'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z', '0', '1', '2', '3', 
               '4', '5', '6', '7', '8', '9']
        
        # 添加标点符号
        punctuation = ['·', '-', '.']
        
        # 创建完整字符集
        all_chars = provinces + alphabets + ads + punctuation
        self.char_mapping = {char: i for i, char in enumerate(set(all_chars))}
        self.reverse_char_mapping = {v: k for k, v in self.char_mapping.items()}
        
        # 保存CCPD格式的字符数组
        self.ccpd_chars = {
            "provinces": provinces,
            "alphabets": alphabets,
            "ads": ads
        }
    
    def load_model(self, model_path: str) -> None:
        """加载训练好的模型"""
        try:
            checkpoint = torch.load(model_path, map_location=self.device)
            
            # 创建模型
            import torchvision.models as models
            self.model = models.resnet18(pretrained=False)
            self.model.fc = nn.Linear(self.model.fc.in_features, len(self.char_mapping))
            
            # 加载权重
            self.model.load_state_dict(checkpoint['model_state_dict'])
            self.model.to(self.device)
            self.model.eval()
            
            self.logger.info(f"模型已从 {model_path} 加载")
            
        except Exception as e:
            self.logger.error(f"加载模型失败: {e}")
            self.model = None
    
    def detect_license_plates(self, image: np.ndarray) -> List[Tuple[int, int, int, int]]:
        """
        检测车牌区域
        
        Args:
            image (np.ndarray): 输入图像
            
        Returns:
            List[Tuple[int, int, int, int]]: 检测到的车牌边界框列表 (x1, y1, x2, y2)
        """
        # 转换为灰度图
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        
        # 高斯模糊
        blurred = cv2.GaussianBlur(gray, (5, 5), 0)
        
        # 边缘检测
        edges = cv2.Canny(blurred, 50, 150)
        
        # 形态学操作
        kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (17, 3))
        closed = cv2.morphologyEx(edges, cv2.MORPH_CLOSE, kernel)
        
        # 查找轮廓
        contours, _ = cv2.findContours(closed, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        plate_candidates = []
        
        for contour in contours:
            # 计算轮廓的边界框
            x, y, w, h = cv2.boundingRect(contour)
            
            # 车牌比例过滤
            aspect_ratio = w / h
            area = w * h
            
            # 车牌的典型比例约为3:1到5:1，面积不能太小
            if 2.0 <= aspect_ratio <= 6.0 and area > 1000:
                plate_candidates.append((x, y, x + w, y + h))
        
        # 按面积排序，返回最大的几个候选区域
        plate_candidates.sort(key=lambda box: (box[2] - box[0]) * (box[3] - box[1]), reverse=True)
        
        return plate_candidates[:3]  # 返回最多3个候选区域
    
    def segment_characters(self, plate_image: np.ndarray) -> List[np.ndarray]:
        """
        分割车牌字符
        
        Args:
            plate_image (np.ndarray): 车牌图像
            
        Returns:
            List[np.ndarray]: 分割出的字符图像列表
        """
        # 转换为灰度图
        gray = cv2.cvtColor(plate_image, cv2.COLOR_BGR2GRAY)
        
        # 二值化
        _, binary = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        
        # 如果背景是黑色，反转图像
        if np.mean(binary) < 127:
            binary = cv2.bitwise_not(binary)
        
        # 垂直投影
        vertical_projection = np.sum(binary, axis=0)
        
        # 找到字符分割点
        char_positions = []
        in_char = False
        start_pos = 0
        
        for i, projection in enumerate(vertical_projection):
            if projection > 0 and not in_char:
                # 字符开始
                start_pos = i
                in_char = True
            elif projection == 0 and in_char:
                # 字符结束
                if i - start_pos > 5:  # 最小字符宽度
                    char_positions.append((start_pos, i))
                in_char = False
        
        # 处理最后一个字符
        if in_char and len(vertical_projection) - start_pos > 5:
            char_positions.append((start_pos, len(vertical_projection)))
        
        # 提取字符图像
        char_images = []
        for start, end in char_positions:
            char_img = plate_image[:, start:end]
            if char_img.shape[1] > 0:
                char_images.append(char_img)
        
        return char_images
    
    def recognize_character(self, char_image: np.ndarray) -> Tuple[str, float]:
        """
        识别单个字符
        
        Args:
            char_image (np.ndarray): 字符图像
            
        Returns:
            Tuple[str, float]: 识别的字符和置信度
        """
        if self.model is None:
            return "?", 0.0
        
        try:
            # 预处理字符图像
            if len(char_image.shape) == 3:
                char_image = cv2.cvtColor(char_image, cv2.COLOR_BGR2RGB)
            else:
                char_image = cv2.cvtColor(char_image, cv2.COLOR_GRAY2RGB)
            
            # 转换为PIL图像
            pil_image = Image.fromarray(char_image)
            
            # 应用变换
            input_tensor = self.transform(pil_image).unsqueeze(0).to(self.device)
            
            # 预测
            with torch.no_grad():
                output = self.model(input_tensor)
                probabilities = torch.softmax(output, dim=1)
                
                predicted_class = output.argmax(dim=1).item()
                confidence = probabilities.max(dim=1)[0].item()
            
            # 转换为字符
            predicted_char = self.reverse_char_mapping.get(predicted_class, "?")
            
            return predicted_char, confidence
            
        except Exception as e:
            self.logger.error(f"字符识别失败: {e}")
            return "?", 0.0
    
    def recognize_plate(self, image: np.ndarray) -> List[Dict[str, Any]]:
        """
        完整的车牌识别流程
        
        Args:
            image (np.ndarray): 输入图像
            
        Returns:
            List[Dict[str, Any]]: 识别结果列表
        """
        results = []
        
        # 检测车牌区域
        plate_boxes = self.detect_license_plates(image)
        
        for i, (x1, y1, x2, y2) in enumerate(plate_boxes):
            # 提取车牌区域
            plate_image = image[y1:y2, x1:x2]
            
            if plate_image.size == 0:
                continue
            
            # 分割字符
            char_images = self.segment_characters(plate_image)
            
            # 识别每个字符
            plate_text = ""
            char_confidences = []
            
            for char_img in char_images:
                char, confidence = self.recognize_character(char_img)
                plate_text += char
                char_confidences.append(confidence)
            
            # 计算平均置信度
            avg_confidence = np.mean(char_confidences) if char_confidences else 0.0
            
            # 验证车牌格式
            is_valid = self._validate_plate_format(plate_text)
            
            result = {
                "bbox": [x1, y1, x2, y2],
                "plate_text": plate_text,
                "confidence": avg_confidence,
                "is_valid": is_valid,
                "char_count": len(char_images)
            }
            
            results.append(result)
        
        # 按置信度排序
        results.sort(key=lambda x: x["confidence"], reverse=True)
        
        return results
    
    def _validate_plate_format(self, plate_text: str) -> bool:
        """
        验证车牌格式
        
        Args:
            plate_text (str): 车牌文本
            
        Returns:
            bool: 是否为有效格式
        """
        if len(plate_text) < 7:
            return False
        
        # 检查第一个字符是否为省份
        if plate_text[0] not in self.ccpd_chars["provinces"]:
            return False
        
        # 检查第二个字符是否为字母
        if len(plate_text) > 1 and plate_text[1] not in self.ccpd_chars["alphabets"]:
            return False
        
        return True
    
    def draw_results(self, image: np.ndarray, results: List[Dict[str, Any]]) -> np.ndarray:
        """
        在图像上绘制识别结果
        
        Args:
            image (np.ndarray): 原始图像
            results (List[Dict[str, Any]]): 识别结果
            
        Returns:
            np.ndarray: 绘制结果的图像
        """
        result_image = image.copy()
        
        for result in results:
            x1, y1, x2, y2 = result["bbox"]
            plate_text = result["plate_text"]
            confidence = result["confidence"]
            is_valid = result["is_valid"]
            
            # 选择颜色
            color = (0, 255, 0) if is_valid else (0, 0, 255)  # 绿色表示有效，红色表示无效
            
            # 绘制边界框
            cv2.rectangle(result_image, (x1, y1), (x2, y2), color, 2)
            
            # 绘制文本
            label = f"{plate_text} ({confidence:.2f})"
            label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.7, 2)[0]
            
            # 绘制文本背景
            cv2.rectangle(result_image, 
                         (x1, y1 - label_size[1] - 10), 
                         (x1 + label_size[0], y1), 
                         color, -1)
            
            # 绘制文本
            cv2.putText(result_image, label, (x1, y1 - 5), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
        
        return result_image
