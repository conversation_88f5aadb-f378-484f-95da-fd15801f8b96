#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CCPD数据集训练器
CCPD Dataset Trainer

基于CCPD (Chinese City Parking Dataset) 训练高精度车牌识别模型

Author: License Plate Recognition Team
Date: 2025-07-30
"""

import os
import sys
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
import torchvision.transforms as transforms
import cv2
import numpy as np
from typing import Dict, Any, List, Tuple, Optional
from pathlib import Path
import json
import time
import requests
import zipfile
import tarfile
from datetime import datetime
from PIL import Image
import random
import shutil
from tqdm import tqdm
import signal
import threading

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent))

from utils.logger import LoggerMixin
from training.training_visualizer import TrainingVisualizer


class CCPDDatasetDownloader(LoggerMixin):
    """CCPD数据集下载器"""
    
    def __init__(self, download_dir: str = "data/ccpd"):
        """
        初始化下载器
        
        Args:
            download_dir (str): 下载目录
        """
        self.download_dir = Path(download_dir)
        self.download_dir.mkdir(parents=True, exist_ok=True)
        
        # CCPD数据集下载链接（使用百度云备用链接）
        self.dataset_urls = {
            "ccpd_2019": {
                "url": "https://pan.baidu.com/s/1i5AOjAbtkwb17Zy-NQGqkw",
                "code": "hm0u",
                "description": "CCPD 2019主数据集 (300k+ 图片)"
            },
            "ccpd_green": {
                "url": "https://pan.baidu.com/s/1JSpc9BZXFlPkXxRK4qUCyw", 
                "code": "ol3j",
                "description": "CCPD Green新能源车牌数据集"
            }
        }
        
        self.logger.info("CCPD数据集下载器初始化完成")
    
    def download_sample_dataset(self) -> str:
        """
        下载示例数据集（用于快速测试）
        
        Returns:
            str: 数据集路径
        """
        sample_dir = self.download_dir / "sample"
        sample_dir.mkdir(exist_ok=True)
        
        # 创建示例数据集结构
        self._create_sample_dataset(sample_dir)
        
        self.logger.info(f"示例数据集创建完成: {sample_dir}")
        return str(sample_dir)
    
    def _create_sample_dataset(self, sample_dir: Path) -> None:
        """创建示例数据集"""
        # 创建目录结构
        train_dir = sample_dir / "train"
        val_dir = sample_dir / "val"
        test_dir = sample_dir / "test"
        
        for dir_path in [train_dir, val_dir, test_dir]:
            dir_path.mkdir(exist_ok=True)
        
        # 生成示例图片（模拟CCPD格式）
        self._generate_sample_images(train_dir, 1000)
        self._generate_sample_images(val_dir, 200)
        self._generate_sample_images(test_dir, 200)
        
        # 创建字符映射文件
        self._create_character_mapping(sample_dir)
    
    def _generate_sample_images(self, output_dir: Path, num_images: int) -> None:
        """生成示例图片"""
        # 中国省份
        provinces = ["皖", "沪", "津", "渝", "冀", "晋", "蒙", "辽", "吉", "黑", "苏", "浙", 
                    "京", "闽", "赣", "鲁", "豫", "鄂", "湘", "粤", "桂", "琼", "川", "贵", 
                    "云", "藏", "陕", "甘", "青", "宁", "新"]
        
        # 字母（不包含I和O）
        alphabets = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'J', 'K', 'L', 'M', 'N', 
                    'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z']
        
        # 字母+数字
        ads = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'J', 'K', 'L', 'M', 'N', 'P', 
               'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z', '0', '1', '2', '3', 
               '4', '5', '6', '7', '8', '9']
        
        for i in range(num_images):
            # 生成随机车牌号
            province_idx = random.randint(0, len(provinces) - 1)
            alphabet_idx = random.randint(0, len(alphabets) - 1)
            ad_indices = [random.randint(0, len(ads) - 1) for _ in range(5)]
            
            # 创建CCPD格式的文件名
            # 格式: Area-TiltDegree-BBox-FourVertices-LPNumber-Brightness-Blurriness.jpg
            area = random.randint(20, 95)
            tilt = f"{random.randint(80, 120)}_{random.randint(80, 120)}"

            # 边界框坐标
            x1, y1 = random.randint(50, 200), random.randint(100, 300)
            x2, y2 = x1 + random.randint(120, 200), y1 + random.randint(40, 80)
            bbox = f"{x1}&{y1}_{x2}&{y2}"

            # 四个顶点坐标（从右下角开始）
            vertices = f"{x2}&{y2}_{x1}&{y2}_{x1}&{y1}_{x2}&{y1}"

            # 车牌号码索引
            lp_indices = f"{province_idx}_{alphabet_idx}_{'_'.join(map(str, ad_indices))}"

            # 亮度和模糊度
            brightness = random.randint(20, 50)
            blurriness = random.randint(10, 30)

            filename = f"{area:03d}-{tilt}-{bbox}-{vertices}-{lp_indices}-{brightness}-{blurriness}.jpg"
            
            # 生成图片
            self._create_sample_image(output_dir / filename, 
                                    provinces[province_idx], 
                                    alphabets[alphabet_idx],
                                    [ads[idx] for idx in ad_indices])
    
    def _create_sample_image(self, filepath: Path, province: str, alphabet: str, 
                           digits: List[str]) -> None:
        """创建示例车牌图片"""
        # 创建640x480的图片
        img = np.random.randint(100, 200, (480, 640, 3), dtype=np.uint8)
        
        # 在图片上绘制车牌区域（简化版）
        plate_x, plate_y = random.randint(100, 400), random.randint(200, 350)
        plate_w, plate_h = 140, 40
        
        # 绘制白色车牌背景
        cv2.rectangle(img, (plate_x, plate_y), (plate_x + plate_w, plate_y + plate_h), 
                     (255, 255, 255), -1)
        
        # 绘制黑色边框
        cv2.rectangle(img, (plate_x, plate_y), (plate_x + plate_w, plate_y + plate_h), 
                     (0, 0, 0), 2)
        
        # 保存图片
        cv2.imwrite(str(filepath), img)
    
    def _create_character_mapping(self, dataset_dir: Path) -> None:
        """创建字符映射文件"""
        provinces = ["皖", "沪", "津", "渝", "冀", "晋", "蒙", "辽", "吉", "黑", "苏", "浙", 
                    "京", "闽", "赣", "鲁", "豫", "鄂", "湘", "粤", "桂", "琼", "川", "贵", 
                    "云", "藏", "陕", "甘", "青", "宁", "新"]
        
        alphabets = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'J', 'K', 'L', 'M', 'N', 
                    'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z']
        
        ads = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'J', 'K', 'L', 'M', 'N', 'P', 
               'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z', '0', '1', '2', '3', 
               '4', '5', '6', '7', '8', '9']
        
        # 创建完整字符映射
        all_chars = provinces + alphabets + ads + ['·', '-', '.']  # 添加标点符号
        char_mapping = {char: i for i, char in enumerate(set(all_chars))}
        
        # 保存映射文件
        mapping_file = dataset_dir / "char_mapping.json"
        with open(mapping_file, 'w', encoding='utf-8') as f:
            json.dump(char_mapping, f, ensure_ascii=False, indent=2)
        
        # 保存CCPD格式的字符数组
        ccpd_chars = {
            "provinces": provinces,
            "alphabets": alphabets,
            "ads": ads
        }
        
        ccpd_file = dataset_dir / "ccpd_chars.json"
        with open(ccpd_file, 'w', encoding='utf-8') as f:
            json.dump(ccpd_chars, f, ensure_ascii=False, indent=2)
        
        self.logger.info(f"字符映射文件已创建: {mapping_file}")


class CCPDDataset(Dataset, LoggerMixin):
    """CCPD数据集类"""
    
    def __init__(self, data_dir: str, split: str = "train", 
                 transform: Optional[transforms.Compose] = None,
                 task: str = "recognition"):
        """
        初始化CCPD数据集
        
        Args:
            data_dir (str): 数据目录
            split (str): 数据分割 ("train", "val", "test")
            transform: 数据变换
            task (str): 任务类型 ("detection", "recognition", "both")
        """
        self.data_dir = Path(data_dir)
        self.split = split
        self.transform = transform
        self.task = task
        
        # 加载字符映射
        self.char_mapping = self._load_character_mapping()
        self.ccpd_chars = self._load_ccpd_characters()
        
        # 加载数据
        self.samples = self._load_samples()
        
        self.logger.info(f"CCPD数据集加载完成: {split} split, {len(self.samples)} 样本")
    
    def _load_character_mapping(self) -> Dict[str, int]:
        """加载字符映射"""
        mapping_file = self.data_dir / "char_mapping.json"
        if mapping_file.exists():
            with open(mapping_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        else:
            # 创建默认映射
            return self._create_default_mapping()
    
    def _load_ccpd_characters(self) -> Dict[str, List[str]]:
        """加载CCPD字符数组"""
        ccpd_file = self.data_dir / "ccpd_chars.json"
        if ccpd_file.exists():
            with open(ccpd_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        else:
            return self._create_default_ccpd_chars()
    
    def _create_default_mapping(self) -> Dict[str, int]:
        """创建默认字符映射"""
        provinces = ["皖", "沪", "津", "渝", "冀", "晋", "蒙", "辽", "吉", "黑", "苏", "浙", 
                    "京", "闽", "赣", "鲁", "豫", "鄂", "湘", "粤", "桂", "琼", "川", "贵", 
                    "云", "藏", "陕", "甘", "青", "宁", "新"]
        
        alphabets = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'J', 'K', 'L', 'M', 'N', 
                    'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z']
        
        ads = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'J', 'K', 'L', 'M', 'N', 'P', 
               'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z', '0', '1', '2', '3', 
               '4', '5', '6', '7', '8', '9']
        
        all_chars = provinces + alphabets + ads + ['·', '-', '.']
        return {char: i for i, char in enumerate(set(all_chars))}
    
    def _create_default_ccpd_chars(self) -> Dict[str, List[str]]:
        """创建默认CCPD字符数组"""
        return {
            "provinces": ["皖", "沪", "津", "渝", "冀", "晋", "蒙", "辽", "吉", "黑", "苏", "浙", 
                         "京", "闽", "赣", "鲁", "豫", "鄂", "湘", "粤", "桂", "琼", "川", "贵", 
                         "云", "藏", "陕", "甘", "青", "宁", "新"],
            "alphabets": ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'J', 'K', 'L', 'M', 'N', 
                         'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z'],
            "ads": ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'J', 'K', 'L', 'M', 'N', 'P', 
                   'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z', '0', '1', '2', '3', 
                   '4', '5', '6', '7', '8', '9']
        }
    
    def _load_samples(self) -> List[Tuple[str, Dict[str, Any]]]:
        """加载样本数据"""
        split_dir = self.data_dir / self.split
        if not split_dir.exists():
            raise FileNotFoundError(f"数据分割目录不存在: {split_dir}")
        
        samples = []
        for img_file in split_dir.glob("*.jpg"):
            # 解析CCPD文件名
            annotation = self._parse_ccpd_filename(img_file.name)
            if annotation:
                samples.append((str(img_file), annotation))
        
        return samples
    
    def _parse_ccpd_filename(self, filename: str) -> Optional[Dict[str, Any]]:
        """
        解析CCPD格式的文件名
        
        格式: Area-TiltDegree-BBox-FourVertices-LPNumber-Brightness-Blurriness.jpg
        """
        try:
            # 移除扩展名
            name = filename.replace('.jpg', '')
            parts = name.split('-')
            
            if len(parts) != 7:
                return None
            
            area, tilt, bbox, vertices, lp_number, brightness, blurriness = parts
            
            # 解析边界框
            bbox_coords = bbox.split('_')
            x1, y1 = map(int, bbox_coords[0].split('&'))
            x2, y2 = map(int, bbox_coords[1].split('&'))
            
            # 解析车牌号码索引
            lp_indices = list(map(int, lp_number.split('_')))
            
            # 转换为实际字符
            if len(lp_indices) >= 7:
                province = self.ccpd_chars["provinces"][lp_indices[0]]
                alphabet = self.ccpd_chars["alphabets"][lp_indices[1]]
                digits = [self.ccpd_chars["ads"][idx] for idx in lp_indices[2:7]]
                
                plate_text = province + alphabet + ''.join(digits)
            else:
                plate_text = ""
            
            return {
                "bbox": [x1, y1, x2, y2],
                "plate_text": plate_text,
                "area_ratio": float(area) / 100.0,
                "brightness": int(brightness),
                "blurriness": int(blurriness)
            }
            
        except Exception as e:
            self.logger.warning(f"解析文件名失败: {filename}, 错误: {e}")
            return None
    
    def __len__(self) -> int:
        return len(self.samples)
    
    def __getitem__(self, idx: int) -> Tuple[torch.Tensor, torch.Tensor]:
        """获取单个样本"""
        img_path, annotation = self.samples[idx]
        
        # 加载图像
        image = cv2.imread(img_path)
        image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        
        if self.task == "recognition":
            # 字符识别任务：提取车牌区域
            bbox = annotation["bbox"]
            plate_img = image[bbox[1]:bbox[3], bbox[0]:bbox[2]]
            
            if plate_img.size == 0:
                plate_img = np.zeros((40, 140, 3), dtype=np.uint8)
            
            # 调整尺寸
            plate_img = cv2.resize(plate_img, (140, 40))
            
            # 转换为PIL图像
            plate_img = Image.fromarray(plate_img)
            
            if self.transform:
                plate_img = self.transform(plate_img)
            
            # 创建标签（车牌文本的字符索引序列）
            plate_text = annotation["plate_text"]
            label = self._text_to_indices(plate_text)
            
            return plate_img, torch.tensor(label, dtype=torch.long)
        
        elif self.task == "detection":
            # 检测任务：返回完整图像和边界框
            if self.transform:
                image = self.transform(Image.fromarray(image))
            
            bbox = annotation["bbox"]
            # 归一化边界框坐标
            h, w = image.shape[1], image.shape[2]
            normalized_bbox = [bbox[0]/w, bbox[1]/h, bbox[2]/w, bbox[3]/h]
            
            return image, torch.tensor(normalized_bbox, dtype=torch.float32)
        
        else:  # both
            # 返回图像和完整标注
            if self.transform:
                image = self.transform(Image.fromarray(image))
            
            return image, annotation
    
    def _text_to_indices(self, text: str) -> List[int]:
        """将文本转换为字符索引序列"""
        indices = []
        for char in text:
            if char in self.char_mapping:
                indices.append(self.char_mapping[char])
            else:
                indices.append(0)  # 未知字符
        
        # 填充到固定长度（7个字符）
        while len(indices) < 7:
            indices.append(0)
        
        return indices[:7]


class CCPDTrainer(LoggerMixin):
    """CCPD训练器 - 支持断点续训和实时进度显示"""

    def __init__(self, config: Dict[str, Any]):
        """
        初始化训练器

        Args:
            config (Dict[str, Any]): 配置字典
        """
        self.config = config
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

        # 模型和训练组件
        self.model = None
        self.optimizer = None
        self.criterion = None
        self.scheduler = None

        # 训练状态
        self.current_epoch = 0
        self.best_accuracy = 0.0
        self.training_history = []
        self.start_epoch = 0

        # 中断处理
        self.interrupted = False
        self.save_dir = None

        # 注册信号处理器
        signal.signal(signal.SIGINT, self._signal_handler)

        self.logger.info(f"CCPD训练器初始化完成，使用设备: {self.device}")

    def _signal_handler(self, signum, frame):
        """处理中断信号"""
        print("\n收到中断信号，正在保存模型...")
        self.interrupted = True
        if self.save_dir and self.model:
            self._save_checkpoint("interrupted_checkpoint.pth")
            print(f"模型已保存到: {self.save_dir}/interrupted_checkpoint.pth")
        print("可以使用 resume_training() 方法继续训练")

    def create_model(self, num_classes: int, model_type: str = "resnet18") -> nn.Module:
        """
        创建字符识别模型

        Args:
            num_classes (int): 类别数量
            model_type (str): 模型类型

        Returns:
            nn.Module: 创建的模型
        """
        if model_type == "resnet18":
            import torchvision.models as models

            # 使用预训练的ResNet18
            backbone = models.resnet18(pretrained=True)

            # 修改最后的分类层
            backbone.fc = nn.Linear(backbone.fc.in_features, num_classes)

            self.model = backbone.to(self.device)

        elif model_type == "efficientnet":
            try:
                import timm
                self.model = timm.create_model('efficientnet_b0',
                                             pretrained=True,
                                             num_classes=num_classes).to(self.device)
            except ImportError:
                self.logger.warning("timm库未安装，使用ResNet18")
                return self.create_model(num_classes, "resnet18")

        else:
            raise ValueError(f"不支持的模型类型: {model_type}")

        param_count = sum(p.numel() for p in self.model.parameters() if p.requires_grad)
        self.logger.info(f"创建了 {model_type} 模型，参数数量: {param_count:,}")

        return self.model

    def setup_training(self, learning_rate: float = 0.001,
                      weight_decay: float = 1e-4) -> None:
        """设置训练组件"""
        if self.model is None:
            raise ValueError("请先创建模型")

        # 优化器
        self.optimizer = optim.AdamW(
            self.model.parameters(),
            lr=learning_rate,
            weight_decay=weight_decay
        )

        # 损失函数
        self.criterion = nn.CrossEntropyLoss()

        # 学习率调度器
        self.scheduler = optim.lr_scheduler.CosineAnnealingWarmRestarts(
            self.optimizer, T_0=10, T_mult=2
        )

        self.logger.info("训练组件设置完成")

    def train_model(self, train_loader: DataLoader, val_loader: DataLoader,
                   epochs: int = 50, save_dir: str = "models/ccpd") -> Dict[str, Any]:
        """
        训练模型

        Args:
            train_loader: 训练数据加载器
            val_loader: 验证数据加载器
            epochs: 训练轮数
            save_dir: 模型保存目录

        Returns:
            Dict[str, Any]: 训练结果
        """
        save_path = Path(save_dir)
        save_path.mkdir(parents=True, exist_ok=True)

        self.logger.info(f"开始训练CCPD模型，共 {epochs} 轮")

        training_start_time = time.time()

        for epoch in range(epochs):
            epoch_start_time = time.time()

            # 训练阶段
            train_loss, train_acc = self._train_epoch(train_loader)

            # 验证阶段
            val_loss, val_acc = self._validate_epoch(val_loader)

            # 学习率调度
            self.scheduler.step()

            # 记录训练历史
            epoch_info = {
                'epoch': epoch + 1,
                'train_loss': train_loss,
                'train_acc': train_acc,
                'val_loss': val_loss,
                'val_acc': val_acc,
                'lr': self.optimizer.param_groups[0]['lr'],
                'time': time.time() - epoch_start_time
            }
            self.training_history.append(epoch_info)

            # 保存最佳模型
            if val_acc > self.best_accuracy:
                self.best_accuracy = val_acc
                best_model_path = save_path / 'best_ccpd_model.pth'
                torch.save({
                    'model_state_dict': self.model.state_dict(),
                    'optimizer_state_dict': self.optimizer.state_dict(),
                    'epoch': epoch + 1,
                    'accuracy': val_acc,
                    'config': self.config
                }, best_model_path)
                self.logger.info(f"保存最佳模型，验证准确率: {val_acc:.4f}")

            # 打印进度
            self.logger.info(
                f"Epoch {epoch+1}/{epochs} - "
                f"Train Loss: {train_loss:.4f}, Train Acc: {train_acc:.4f} - "
                f"Val Loss: {val_loss:.4f}, Val Acc: {val_acc:.4f} - "
                f"LR: {self.optimizer.param_groups[0]['lr']:.6f} - "
                f"Time: {epoch_info['time']:.2f}s"
            )

        training_time = time.time() - training_start_time

        result = {
            'best_accuracy': self.best_accuracy,
            'total_epochs': epochs,
            'training_time': training_time,
            'final_train_acc': train_acc,
            'final_val_acc': val_acc,
            'model_path': str(save_path / 'best_ccpd_model.pth')
        }

        self.logger.info(f"CCPD模型训练完成！最佳验证准确率: {self.best_accuracy:.4f}")
        return result

    def _train_epoch(self, train_loader: DataLoader) -> Tuple[float, float]:
        """训练一个epoch"""
        self.model.train()
        total_loss = 0.0
        correct = 0
        total = 0

        for batch_idx, (data, target) in enumerate(train_loader):
            data, target = data.to(self.device), target.to(self.device)

            # 如果target是序列，只使用第一个字符进行训练
            if len(target.shape) > 1:
                target = target[:, 0]  # 使用省份字符

            self.optimizer.zero_grad()
            output = self.model(data)
            loss = self.criterion(output, target)
            loss.backward()
            self.optimizer.step()

            total_loss += loss.item()
            pred = output.argmax(dim=1, keepdim=True)
            correct += pred.eq(target.view_as(pred)).sum().item()
            total += target.size(0)

        avg_loss = total_loss / len(train_loader)
        accuracy = correct / total

        return avg_loss, accuracy

    def _validate_epoch(self, val_loader: DataLoader) -> Tuple[float, float]:
        """验证一个epoch"""
        self.model.eval()
        total_loss = 0.0
        correct = 0
        total = 0

        with torch.no_grad():
            for data, target in val_loader:
                data, target = data.to(self.device), target.to(self.device)

                # 如果target是序列，只使用第一个字符进行验证
                if len(target.shape) > 1:
                    target = target[:, 0]

                output = self.model(data)
                loss = self.criterion(output, target)

                total_loss += loss.item()
                pred = output.argmax(dim=1, keepdim=True)
                correct += pred.eq(target.view_as(pred)).sum().item()
                total += target.size(0)

        avg_loss = total_loss / len(val_loader)
        accuracy = correct / total

        return avg_loss, accuracy

    def predict_character(self, image: np.ndarray) -> Tuple[str, float]:
        """
        预测单个字符

        Args:
            image (np.ndarray): 输入图像

        Returns:
            Tuple[str, float]: 预测字符和置信度
        """
        if self.model is None:
            raise ValueError("请先创建并加载模型")

        self.model.eval()

        # 预处理图像
        if len(image.shape) == 3:
            image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)

        # 转换为PIL图像并调整尺寸
        pil_image = Image.fromarray(image)
        pil_image = pil_image.resize((64, 64))

        # 转换为张量
        transform = transforms.Compose([
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406],
                               std=[0.229, 0.224, 0.225])
        ])

        input_tensor = transform(pil_image).unsqueeze(0).to(self.device)

        # 预测
        with torch.no_grad():
            output = self.model(input_tensor)
            probabilities = torch.softmax(output, dim=1)

            predicted_class = output.argmax(dim=1).item()
            confidence = probabilities.max(dim=1)[0].item()

        return predicted_class, confidence

    def load_model(self, model_path: str) -> None:
        """加载训练好的模型"""
        checkpoint = torch.load(model_path, map_location=self.device)

        if self.model is None:
            # 如果模型未创建，需要先创建
            config = checkpoint.get('config', {})
            num_classes = len(checkpoint.get('char_mapping', {}))
            if num_classes == 0:
                num_classes = 100  # 默认值
            self.create_model(num_classes)

        self.model.load_state_dict(checkpoint['model_state_dict'])
        self.logger.info(f"模型已从 {model_path} 加载")


def main():
    """主函数"""
    print("=" * 60)
    print("CCPD车牌数据集训练器")
    print("Chinese City Parking Dataset Trainer")
    print("=" * 60)

    # 配置
    config = {
        "data_dir": "data/ccpd/sample",
        "batch_size": 32,
        "learning_rate": 0.001,
        "epochs": 30,
        "model_type": "resnet18",
        "num_workers": 2
    }

    try:
        # 创建下载器并准备数据
        print("正在准备CCPD数据集...")
        downloader = CCPDDatasetDownloader()
        dataset_path = downloader.download_sample_dataset()
        print(f"数据集准备完成: {dataset_path}")

        # 数据变换
        train_transform = transforms.Compose([
            transforms.Resize((64, 64)),
            transforms.RandomRotation(10),
            transforms.ColorJitter(brightness=0.2, contrast=0.2),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406],
                               std=[0.229, 0.224, 0.225])
        ])

        val_transform = transforms.Compose([
            transforms.Resize((64, 64)),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406],
                               std=[0.229, 0.224, 0.225])
        ])

        # 创建数据集
        print("正在加载数据集...")
        train_dataset = CCPDDataset(dataset_path, "train", train_transform, "recognition")
        val_dataset = CCPDDataset(dataset_path, "val", val_transform, "recognition")

        print(f"训练集样本数: {len(train_dataset)}")
        print(f"验证集样本数: {len(val_dataset)}")
        print(f"字符类别数: {len(train_dataset.char_mapping)}")

        # 创建数据加载器
        train_loader = DataLoader(train_dataset, batch_size=config["batch_size"],
                                 shuffle=True, num_workers=config["num_workers"])
        val_loader = DataLoader(val_dataset, batch_size=config["batch_size"],
                               shuffle=False, num_workers=config["num_workers"])

        # 创建训练器
        trainer = CCPDTrainer(config)

        # 获取字符数量
        num_classes = len(train_dataset.char_mapping)

        # 创建模型
        print(f"正在创建 {config['model_type']} 模型...")
        trainer.create_model(num_classes, config["model_type"])

        # 设置训练
        trainer.setup_training(config["learning_rate"])

        # 开始训练
        print("开始训练...")
        result = trainer.train_model(train_loader, val_loader, config["epochs"])

        print("\n" + "=" * 60)
        print("训练完成！")
        print(f"最佳验证准确率: {result['best_accuracy']:.4f}")
        print(f"训练时间: {result['training_time']:.2f} 秒")
        print(f"模型保存路径: {result['model_path']}")
        print("=" * 60)

    except Exception as e:
        print(f"训练过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()


class CCPDTrainer(LoggerMixin):
    """CCPD训练器 - 支持断点续训、实时可视化和详细数据记录"""

    def __init__(self, config: Dict[str, Any], experiment_name: str = None):
        """
        初始化训练器

        Args:
            config (Dict[str, Any]): 配置字典
            experiment_name (str): 实验名称
        """
        self.config = config
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

        # 模型和训练组件
        self.model = None
        self.optimizer = None
        self.criterion = None
        self.scheduler = None

        # 训练状态
        self.current_epoch = 0
        self.best_accuracy = 0.0
        self.training_history = []
        self.start_epoch = 0

        # 中断处理
        self.interrupted = False
        self.save_dir = None

        # 可视化器
        self.visualizer = TrainingVisualizer(experiment_name)
        self.visualizer.save_config(config)

        # 注册信号处理器
        signal.signal(signal.SIGINT, self._signal_handler)

        self.logger.info(f"CCPD训练器初始化完成，使用设备: {self.device}")
        self.logger.info(f"实验名称: {self.visualizer.experiment_name}")

    def _signal_handler(self, signum, frame):
        """处理中断信号"""
        print("\n收到中断信号，正在保存模型和数据...")
        self.interrupted = True
        if self.save_dir and self.model:
            self._save_checkpoint("interrupted_checkpoint.pth")
            print(f"模型已保存到: {self.save_dir}/interrupted_checkpoint.pth")

        # 保存当前训练数据和图表
        if self.training_history:
            self.visualizer.plot_training_curves()
            self.visualizer.generate_training_report()
            print("训练数据和图表已保存")

        print("可以使用 resume_training() 方法继续训练")
        sys.exit(0)

    def create_model(self, num_classes: int, model_type: str = "resnet18") -> nn.Module:
        """
        创建字符识别模型

        Args:
            num_classes (int): 类别数量
            model_type (str): 模型类型

        Returns:
            nn.Module: 创建的模型
        """
        if model_type == "resnet18":
            import torchvision.models as models

            # 使用预训练的ResNet18
            backbone = models.resnet18(pretrained=True)

            # 修改最后的分类层
            backbone.fc = nn.Linear(backbone.fc.in_features, num_classes)

            self.model = backbone.to(self.device)

        elif model_type == "efficientnet":
            try:
                import timm
                self.model = timm.create_model('efficientnet_b0',
                                             pretrained=True,
                                             num_classes=num_classes).to(self.device)
            except ImportError:
                self.logger.warning("timm库未安装，使用ResNet18")
                return self.create_model(num_classes, "resnet18")

        else:
            raise ValueError(f"不支持的模型类型: {model_type}")

        param_count = sum(p.numel() for p in self.model.parameters() if p.requires_grad)
        self.logger.info(f"创建了 {model_type} 模型，参数数量: {param_count:,}")

        return self.model

    def setup_training(self, learning_rate: float = 0.001,
                      weight_decay: float = 1e-4) -> None:
        """设置训练组件"""
        if self.model is None:
            raise ValueError("请先创建模型")

        # 优化器
        self.optimizer = optim.AdamW(
            self.model.parameters(),
            lr=learning_rate,
            weight_decay=weight_decay
        )

        # 损失函数
        self.criterion = nn.CrossEntropyLoss()

        # 学习率调度器
        self.scheduler = optim.lr_scheduler.CosineAnnealingWarmRestarts(
            self.optimizer, T_0=10, T_mult=2
        )

        self.logger.info("训练组件设置完成")

    def train_model(self, train_loader: DataLoader, val_loader: DataLoader,
                   epochs: int = 50, save_dir: str = "models/ccpd") -> Dict[str, Any]:
        """
        训练模型

        Args:
            train_loader: 训练数据加载器
            val_loader: 验证数据加载器
            epochs: 训练轮数
            save_dir: 模型保存目录

        Returns:
            Dict[str, Any]: 训练结果
        """
        save_path = Path(save_dir)
        save_path.mkdir(parents=True, exist_ok=True)

        self.logger.info(f"开始训练CCPD模型，共 {epochs} 轮")

        training_start_time = time.time()

        for epoch in range(epochs):
            epoch_start_time = time.time()

            # 训练阶段
            train_loss, train_acc = self._train_epoch(train_loader)

            # 验证阶段
            val_loss, val_acc = self._validate_epoch(val_loader)

            # 学习率调度
            self.scheduler.step()

            # 记录训练历史
            epoch_info = {
                'epoch': epoch + 1,
                'train_loss': train_loss,
                'train_acc': train_acc,
                'val_loss': val_loss,
                'val_acc': val_acc,
                'lr': self.optimizer.param_groups[0]['lr'],
                'time': time.time() - epoch_start_time
            }
            self.training_history.append(epoch_info)

            # 保存最佳模型
            if val_acc > self.best_accuracy:
                self.best_accuracy = val_acc
                best_model_path = save_path / 'best_ccpd_model.pth'
                torch.save({
                    'model_state_dict': self.model.state_dict(),
                    'optimizer_state_dict': self.optimizer.state_dict(),
                    'epoch': epoch + 1,
                    'accuracy': val_acc,
                    'config': self.config
                }, best_model_path)
                self.logger.info(f"保存最佳模型，验证准确率: {val_acc:.4f}")

            # 打印进度
            self.logger.info(
                f"Epoch {epoch+1}/{epochs} - "
                f"Train Loss: {train_loss:.4f}, Train Acc: {train_acc:.4f} - "
                f"Val Loss: {val_loss:.4f}, Val Acc: {val_acc:.4f} - "
                f"LR: {self.optimizer.param_groups[0]['lr']:.6f} - "
                f"Time: {epoch_info['time']:.2f}s"
            )

        training_time = time.time() - training_start_time

        result = {
            'best_accuracy': self.best_accuracy,
            'total_epochs': epochs,
            'training_time': training_time,
            'final_train_acc': train_acc,
            'final_val_acc': val_acc,
            'model_path': str(save_path / 'best_ccpd_model.pth')
        }

        self.logger.info(f"CCPD模型训练完成！最佳验证准确率: {self.best_accuracy:.4f}")
        return result

    def _train_epoch(self, train_loader: DataLoader) -> Tuple[float, float]:
        """训练一个epoch"""
        self.model.train()
        total_loss = 0.0
        correct = 0
        total = 0

        for batch_idx, (data, target) in enumerate(train_loader):
            data, target = data.to(self.device), target.to(self.device)

            # 如果target是序列，只使用第一个字符进行训练
            if len(target.shape) > 1:
                target = target[:, 0]  # 使用省份字符

            self.optimizer.zero_grad()
            output = self.model(data)
            loss = self.criterion(output, target)
            loss.backward()
            self.optimizer.step()

            total_loss += loss.item()
            pred = output.argmax(dim=1, keepdim=True)
            correct += pred.eq(target.view_as(pred)).sum().item()
            total += target.size(0)

        avg_loss = total_loss / len(train_loader)
        accuracy = correct / total

        return avg_loss, accuracy

    def _validate_epoch(self, val_loader: DataLoader) -> Tuple[float, float]:
        """验证一个epoch"""
        self.model.eval()
        total_loss = 0.0
        correct = 0
        total = 0

        with torch.no_grad():
            for data, target in val_loader:
                data, target = data.to(self.device), target.to(self.device)

                # 如果target是序列，只使用第一个字符进行验证
                if len(target.shape) > 1:
                    target = target[:, 0]

                output = self.model(data)
                loss = self.criterion(output, target)

                total_loss += loss.item()
                pred = output.argmax(dim=1, keepdim=True)
                correct += pred.eq(target.view_as(pred)).sum().item()
                total += target.size(0)

        avg_loss = total_loss / len(val_loader)
        accuracy = correct / total

        return avg_loss, accuracy


def main():
    """主函数"""
    # 配置
    config = {
        "data_dir": "data/ccpd/sample",
        "batch_size": 32,
        "learning_rate": 0.001,
        "epochs": 50,
        "model_type": "resnet18",
        "num_workers": 4
    }

    # 创建下载器并准备数据
    downloader = CCPDDatasetDownloader()
    dataset_path = downloader.download_sample_dataset()

    # 数据变换
    train_transform = transforms.Compose([
        transforms.Resize((64, 64)),
        transforms.RandomRotation(10),
        transforms.ColorJitter(brightness=0.2, contrast=0.2),
        transforms.ToTensor(),
        transforms.Normalize(mean=[0.485, 0.456, 0.406],
                           std=[0.229, 0.224, 0.225])
    ])

    val_transform = transforms.Compose([
        transforms.Resize((64, 64)),
        transforms.ToTensor(),
        transforms.Normalize(mean=[0.485, 0.456, 0.406],
                           std=[0.229, 0.224, 0.225])
    ])

    # 创建数据集
    train_dataset = CCPDDataset(dataset_path, "train", train_transform, "recognition")
    val_dataset = CCPDDataset(dataset_path, "val", val_transform, "recognition")

    # 创建数据加载器
    train_loader = DataLoader(train_dataset, batch_size=config["batch_size"],
                             shuffle=True, num_workers=config["num_workers"])
    val_loader = DataLoader(val_dataset, batch_size=config["batch_size"],
                           shuffle=False, num_workers=config["num_workers"])

    # 创建训练器
    trainer = CCPDTrainer(config)

    # 获取字符数量
    num_classes = len(train_dataset.char_mapping)

    # 创建模型
    trainer.create_model(num_classes, config["model_type"])

    # 设置训练
    trainer.setup_training(config["learning_rate"])

    # 开始训练
    result = trainer.train_model(train_loader, val_loader, config["epochs"])

    print(f"训练完成！最佳准确率: {result['best_accuracy']:.4f}")
    print(f"模型保存路径: {result['model_path']}")


if __name__ == "__main__":
    main()
