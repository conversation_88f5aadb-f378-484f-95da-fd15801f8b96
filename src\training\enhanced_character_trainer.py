#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强型字符识别训练器
Enhanced Character Recognition Trainer

专门用于训练识别中英文和车牌标点符号的模型

Author: License Plate Recognition Team
Date: 2025-07-30
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, Dataset
import torchvision.transforms as transforms
import numpy as np
from typing import Dict, Any, List, Tuple, Optional
from pathlib import Path
import json
import cv2
from PIL import Image, ImageDraw, ImageFont
import random
import time
from datetime import datetime

from ..utils.logger import LoggerMixin


class EnhancedCharacterTrainer(LoggerMixin):
    """增强型字符识别训练器"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化训练器
        
        Args:
            config (Dict[str, Any]): 配置字典
        """
        self.config = config
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # 完整的字符映射（包含中文、英文、数字、标点符号）
        self.char_mapping = self._create_complete_character_mapping()
        self.reverse_char_mapping = {v: k for k, v in self.char_mapping.items()}
        self.num_classes = len(self.char_mapping)
        
        # 模型和训练组件
        self.model = None
        self.optimizer = None
        self.criterion = None
        self.scheduler = None
        
        # 训练状态
        self.current_epoch = 0
        self.best_accuracy = 0.0
        self.training_history = []
        
        self.logger.info(f"增强型字符识别训练器初始化完成")
        self.logger.info(f"支持字符类别数: {self.num_classes}")
        self.logger.info(f"支持的字符: {list(self.char_mapping.keys())}")
    
    def _create_complete_character_mapping(self) -> Dict[str, int]:
        """创建完整的字符映射，包含所有车牌可能出现的字符"""
        
        # 中国省份简称（31个）
        provinces = [
            '京', '津', '沪', '渝',  # 直辖市
            '冀', '豫', '云', '辽', '黑', '湘', '皖', '鲁', '新', '苏', '浙', '赣',
            '鄂', '桂', '甘', '晋', '蒙', '陕', '吉', '闽', '贵', '粤', '青', '藏',
            '川', '宁', '琼'  # 其他省份
        ]
        
        # 英文字母（24个，不包含I和O）
        letters = [
            'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'J', 'K', 'L', 'M',
            'N', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z'
        ]
        
        # 数字（10个）
        digits = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9']
        
        # 车牌标点符号
        punctuation = [
            '·',  # 中点符号（用于某些特殊车牌）
            '-',  # 连字符
            '.',  # 点号
        ]
        
        # 特殊字符
        special_chars = [
            '<UNK>',  # 未知字符
            '<PAD>',  # 填充字符
            '<BLANK>'  # CTC空白字符
        ]

        # 合并所有字符
        all_chars = provinces + letters + digits + punctuation + special_chars

        # 创建映射（从1开始，0保留给背景）
        char_mapping = {char: i + 1 for i, char in enumerate(all_chars)}
        char_mapping['<BACKGROUND>'] = 0  # 背景类别

        return char_mapping

    def create_model(self, model_type: str = 'resnet') -> nn.Module:
        """
        创建字符识别模型

        Args:
            model_type (str): 模型类型 ('resnet', 'efficientnet', 'vit')

        Returns:
            nn.Module: 字符识别模型
        """
        if model_type == 'resnet':
            model = self._create_resnet_model()
        elif model_type == 'efficientnet':
            model = self._create_efficientnet_model()
        elif model_type == 'vit':
            model = self._create_vit_model()
        else:
            raise ValueError(f"不支持的模型类型: {model_type}")

        self.model = model.to(self.device)
        self.logger.info(f"创建了 {model_type} 模型，参数数量: {self._count_parameters():,}")

        return self.model

    def _create_resnet_model(self) -> nn.Module:
        """创建基于ResNet的字符识别模型"""
        import torchvision.models as models

        # 使用预训练的ResNet18
        backbone = models.resnet18(pretrained=True)

        # 修改输入层以适应字符图像
        backbone.conv1 = nn.Conv2d(3, 64, kernel_size=7, stride=2, padding=3, bias=False)

        # 移除最后的分类层
        backbone = nn.Sequential(*list(backbone.children())[:-1])

        # 创建完整模型
        model = nn.Sequential(
            backbone,
            nn.AdaptiveAvgPool2d((1, 1)),
            nn.Flatten(),
            nn.Dropout(0.5),
            nn.Linear(512, 256),
            nn.ReLU(inplace=True),
            nn.Dropout(0.3),
            nn.Linear(256, 128),
            nn.ReLU(inplace=True),
            nn.Linear(128, self.num_classes)
        )

        return model

    def _create_efficientnet_model(self) -> nn.Module:
        """创建基于EfficientNet的字符识别模型"""
        try:
            import timm

            # 使用EfficientNet-B0
            backbone = timm.create_model('efficientnet_b0', pretrained=True, num_classes=0)

            model = nn.Sequential(
                backbone,
                nn.Dropout(0.4),
                nn.Linear(1280, 256),
                nn.ReLU(inplace=True),
                nn.Dropout(0.2),
                nn.Linear(256, self.num_classes)
            )

            return model

        except ImportError:
            self.logger.warning("timm库未安装，回退到ResNet模型")
            return self._create_resnet_model()

    def _create_vit_model(self) -> nn.Module:
        """创建基于Vision Transformer的字符识别模型"""
        try:
            import timm

            # 使用ViT-Tiny
            backbone = timm.create_model('vit_tiny_patch16_224', pretrained=True, num_classes=0)

            model = nn.Sequential(
                backbone,
                nn.Dropout(0.3),
                nn.Linear(192, 128),
                nn.ReLU(inplace=True),
                nn.Linear(128, self.num_classes)
            )

            return model

        except ImportError:
            self.logger.warning("timm库未安装，回退到ResNet模型")
            return self._create_resnet_model()

    def _count_parameters(self) -> int:
        """计算模型参数数量"""
        return sum(p.numel() for p in self.model.parameters() if p.requires_grad)

    def setup_training(self, learning_rate: float = 0.001,
                      weight_decay: float = 1e-4) -> None:
        """
        设置训练组件

        Args:
            learning_rate (float): 学习率
            weight_decay (float): 权重衰减
        """
        if self.model is None:
            raise ValueError("请先创建模型")

        # 优化器
        self.optimizer = optim.AdamW(
            self.model.parameters(),
            lr=learning_rate,
            weight_decay=weight_decay
        )

        # 损失函数（使用标签平滑）
        self.criterion = nn.CrossEntropyLoss(label_smoothing=0.1)

        # 学习率调度器
        self.scheduler = optim.lr_scheduler.CosineAnnealingWarmRestarts(
            self.optimizer, T_0=10, T_mult=2
        )

        self.logger.info("训练组件设置完成")

    def create_synthetic_dataset(self, output_dir: str,
                               samples_per_char: int = 1000) -> None:
        """
        创建合成字符数据集

        Args:
            output_dir (str): 输出目录
            samples_per_char (int): 每个字符的样本数量
        """
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)

        self.logger.info(f"开始创建合成数据集，每个字符 {samples_per_char} 个样本")

        # 为每个字符创建目录和样本
        for char, char_id in self.char_mapping.items():
            if char in ['<UNK>', '<PAD>', '<BLANK>', '<BACKGROUND>']:
                continue  # 跳过特殊字符

            char_dir = output_path / str(char_id) / char
            char_dir.mkdir(parents=True, exist_ok=True)

            # 生成样本
            for i in range(samples_per_char):
                image = self._generate_character_image(char)
                image_path = char_dir / f"{char}_{i:04d}.png"
                image.save(image_path)

            self.logger.info(f"为字符 '{char}' 生成了 {samples_per_char} 个样本")

        # 保存字符映射
        mapping_file = output_path / 'char_mapping.json'
        with open(mapping_file, 'w', encoding='utf-8') as f:
            json.dump(self.char_mapping, f, ensure_ascii=False, indent=2)

        self.logger.info(f"合成数据集创建完成，保存在: {output_path}")

    def _generate_character_image(self, char: str,
                                 image_size: Tuple[int, int] = (64, 64)) -> Image.Image:
        """
        生成单个字符的图像

        Args:
            char (str): 要生成的字符
            image_size (Tuple[int, int]): 图像尺寸

        Returns:
            Image.Image: 生成的字符图像
        """
        # 创建图像
        img = Image.new('RGB', image_size, color='white')
        draw = ImageDraw.Draw(img)

        # 尝试加载字体
        try:
            # Windows系统字体
            if char in '京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼':
                font_size = random.randint(36, 48)
                font = ImageFont.truetype("C:/Windows/Fonts/simsun.ttc", font_size)
            else:
                font_size = random.randint(32, 44)
                font = ImageFont.truetype("C:/Windows/Fonts/arial.ttf", font_size)
        except:
            # 使用默认字体
            font = ImageFont.load_default()

        # 计算文字位置（居中）
        bbox = draw.textbbox((0, 0), char, font=font)
        text_width = bbox[2] - bbox[0]
        text_height = bbox[3] - bbox[1]

        x = (image_size[0] - text_width) // 2
        y = (image_size[1] - text_height) // 2

        # 随机颜色和背景
        text_color = (random.randint(0, 100), random.randint(0, 100), random.randint(0, 100))
        bg_color = (random.randint(200, 255), random.randint(200, 255), random.randint(200, 255))

        # 重新创建图像
        img = Image.new('RGB', image_size, color=bg_color)
        draw = ImageDraw.Draw(img)

        # 绘制字符
        draw.text((x, y), char, fill=text_color, font=font)

        # 添加噪声和变换
        img = self._add_augmentation(img)

        return img

    def _add_augmentation(self, img: Image.Image) -> Image.Image:
        """为图像添加数据增强"""
        # 转换为numpy数组
        img_array = np.array(img)

        # 随机旋转
        if random.random() < 0.3:
            angle = random.uniform(-15, 15)
            img = img.rotate(angle, fillcolor='white')

        # 随机缩放
        if random.random() < 0.3:
            scale = random.uniform(0.8, 1.2)
            new_size = (int(img.size[0] * scale), int(img.size[1] * scale))
            img = img.resize(new_size, Image.Resampling.LANCZOS)

            # 裁剪或填充到原始尺寸
            if img.size[0] > 64 or img.size[1] > 64:
                img = img.crop((0, 0, 64, 64))
            else:
                new_img = Image.new('RGB', (64, 64), color='white')
                new_img.paste(img, ((64 - img.size[0]) // 2, (64 - img.size[1]) // 2))
                img = new_img

        # 添加高斯噪声
        if random.random() < 0.2:
            img_array = np.array(img)
            noise = np.random.normal(0, 10, img_array.shape)
            img_array = np.clip(img_array + noise, 0, 255).astype(np.uint8)
            img = Image.fromarray(img_array)

        return img

    def train_model(self, train_loader: DataLoader, val_loader: DataLoader,
                   epochs: int = 100, save_dir: str = 'models') -> Dict[str, Any]:
        """
        训练模型

        Args:
            train_loader (DataLoader): 训练数据加载器
            val_loader (DataLoader): 验证数据加载器
            epochs (int): 训练轮数
            save_dir (str): 模型保存目录

        Returns:
            Dict[str, Any]: 训练结果
        """
        if self.model is None or self.optimizer is None:
            raise ValueError("请先创建模型和设置训练组件")

        save_path = Path(save_dir)
        save_path.mkdir(parents=True, exist_ok=True)

        self.logger.info(f"开始训练，共 {epochs} 轮")

        training_start_time = time.time()

        for epoch in range(epochs):
            epoch_start_time = time.time()

            # 训练阶段
            train_loss, train_acc = self._train_epoch(train_loader)

            # 验证阶段
            val_loss, val_acc = self._validate_epoch(val_loader)

            # 学习率调度
            self.scheduler.step()

            # 记录训练历史
            epoch_info = {
                'epoch': epoch + 1,
                'train_loss': train_loss,
                'train_acc': train_acc,
                'val_loss': val_loss,
                'val_acc': val_acc,
                'lr': self.optimizer.param_groups[0]['lr'],
                'time': time.time() - epoch_start_time
            }
            self.training_history.append(epoch_info)

            # 保存最佳模型
            if val_acc > self.best_accuracy:
                self.best_accuracy = val_acc
                best_model_path = save_path / 'best_model.pth'
                torch.save({
                    'model_state_dict': self.model.state_dict(),
                    'optimizer_state_dict': self.optimizer.state_dict(),
                    'epoch': epoch + 1,
                    'accuracy': val_acc,
                    'char_mapping': self.char_mapping
                }, best_model_path)
                self.logger.info(f"保存最佳模型，验证准确率: {val_acc:.4f}")

            # 定期保存检查点
            if (epoch + 1) % 10 == 0:
                checkpoint_path = save_path / f'checkpoint_epoch_{epoch+1}.pth'
                torch.save({
                    'model_state_dict': self.model.state_dict(),
                    'optimizer_state_dict': self.optimizer.state_dict(),
                    'epoch': epoch + 1,
                    'accuracy': val_acc,
                    'char_mapping': self.char_mapping
                }, checkpoint_path)

            # 打印进度
            self.logger.info(
                f"Epoch {epoch+1}/{epochs} - "
                f"Train Loss: {train_loss:.4f}, Train Acc: {train_acc:.4f} - "
                f"Val Loss: {val_loss:.4f}, Val Acc: {val_acc:.4f} - "
                f"LR: {self.optimizer.param_groups[0]['lr']:.6f} - "
                f"Time: {epoch_info['time']:.2f}s"
            )

        training_time = time.time() - training_start_time

        # 保存训练历史
        history_path = save_path / 'training_history.json'
        with open(history_path, 'w', encoding='utf-8') as f:
            json.dump(self.training_history, f, indent=2)

        result = {
            'best_accuracy': self.best_accuracy,
            'total_epochs': epochs,
            'training_time': training_time,
            'final_train_acc': train_acc,
            'final_val_acc': val_acc,
            'model_path': str(save_path / 'best_model.pth')
        }

        self.logger.info(f"训练完成！最佳验证准确率: {self.best_accuracy:.4f}")
        return result

    def _train_epoch(self, train_loader: DataLoader) -> Tuple[float, float]:
        """训练一个epoch"""
        self.model.train()
        total_loss = 0.0
        correct = 0
        total = 0

        for batch_idx, (data, target) in enumerate(train_loader):
            data, target = data.to(self.device), target.to(self.device)

            # 前向传播
            self.optimizer.zero_grad()
            output = self.model(data)
            loss = self.criterion(output, target)

            # 反向传播
            loss.backward()
            self.optimizer.step()

            # 统计
            total_loss += loss.item()
            pred = output.argmax(dim=1, keepdim=True)
            correct += pred.eq(target.view_as(pred)).sum().item()
            total += target.size(0)

        avg_loss = total_loss / len(train_loader)
        accuracy = correct / total

        return avg_loss, accuracy

    def _validate_epoch(self, val_loader: DataLoader) -> Tuple[float, float]:
        """验证一个epoch"""
        self.model.eval()
        total_loss = 0.0
        correct = 0
        total = 0

        with torch.no_grad():
            for data, target in val_loader:
                data, target = data.to(self.device), target.to(self.device)

                output = self.model(data)
                loss = self.criterion(output, target)

                total_loss += loss.item()
                pred = output.argmax(dim=1, keepdim=True)
                correct += pred.eq(target.view_as(pred)).sum().item()
                total += target.size(0)

        avg_loss = total_loss / len(val_loader)
        accuracy = correct / total

        return avg_loss, accuracy

    def evaluate_model(self, test_loader: DataLoader) -> Dict[str, Any]:
        """
        评估模型性能

        Args:
            test_loader (DataLoader): 测试数据加载器

        Returns:
            Dict[str, Any]: 评估结果
        """
        if self.model is None:
            raise ValueError("请先创建模型")

        self.model.eval()

        all_predictions = []
        all_targets = []
        all_confidences = []

        with torch.no_grad():
            for data, target in test_loader:
                data, target = data.to(self.device), target.to(self.device)

                output = self.model(data)
                probabilities = torch.softmax(output, dim=1)

                pred = output.argmax(dim=1)
                confidence = probabilities.max(dim=1)[0]

                all_predictions.extend(pred.cpu().numpy())
                all_targets.extend(target.cpu().numpy())
                all_confidences.extend(confidence.cpu().numpy())

        # 计算各种指标
        accuracy = sum(p == t for p, t in zip(all_predictions, all_targets)) / len(all_targets)

        # 按字符类别统计准确率
        char_accuracy = {}
        for char, char_id in self.char_mapping.items():
            if char in ['<UNK>', '<PAD>', '<BLANK>', '<BACKGROUND>']:
                continue

            char_correct = sum(1 for p, t in zip(all_predictions, all_targets)
                             if t == char_id and p == char_id)
            char_total = sum(1 for t in all_targets if t == char_id)

            if char_total > 0:
                char_accuracy[char] = char_correct / char_total

        result = {
            'overall_accuracy': accuracy,
            'character_accuracy': char_accuracy,
            'average_confidence': np.mean(all_confidences),
            'total_samples': len(all_targets)
        }

        self.logger.info(f"模型评估完成，总体准确率: {accuracy:.4f}")
        return result

    def predict_character(self, image: np.ndarray) -> Tuple[str, float]:
        """
        预测单个字符

        Args:
            image (np.ndarray): 输入图像

        Returns:
            Tuple[str, float]: 预测字符和置信度
        """
        if self.model is None:
            raise ValueError("请先创建模型")

        self.model.eval()

        # 预处理图像
        if len(image.shape) == 3:
            image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)

        # 转换为PIL图像并调整尺寸
        pil_image = Image.fromarray(image)
        pil_image = pil_image.resize((64, 64))

        # 转换为张量
        transform = transforms.Compose([
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406],
                               std=[0.229, 0.224, 0.225])
        ])

        input_tensor = transform(pil_image).unsqueeze(0).to(self.device)

        # 预测
        with torch.no_grad():
            output = self.model(input_tensor)
            probabilities = torch.softmax(output, dim=1)

            predicted_class = output.argmax(dim=1).item()
            confidence = probabilities.max(dim=1)[0].item()

        # 转换为字符
        predicted_char = self.reverse_char_mapping.get(predicted_class, '<UNK>')

        return predicted_char, confidence

    def save_char_mapping(self, filepath: str) -> None:
        """保存字符映射"""
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(self.char_mapping, f, ensure_ascii=False, indent=2)
        self.logger.info(f"字符映射已保存到: {filepath}")

    def load_model(self, model_path: str) -> None:
        """加载训练好的模型"""
        checkpoint = torch.load(model_path, map_location=self.device)

        if self.model is None:
            # 如果模型未创建，需要先创建
            self.create_model()

        self.model.load_state_dict(checkpoint['model_state_dict'])

        if 'char_mapping' in checkpoint:
            self.char_mapping = checkpoint['char_mapping']
            self.reverse_char_mapping = {v: k for k, v in self.char_mapping.items()}

        self.logger.info(f"模型已从 {model_path} 加载")
            '
