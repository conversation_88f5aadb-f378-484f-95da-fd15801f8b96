#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
训练API服务器
Training API Server

通过FastAPI提供训练服务接口，避免程序间相互影响

Author: License Plate Recognition Team
Date: 2025-07-30
"""

import os
import sys
import asyncio
import uvicorn
from fastapi import FastAPI, BackgroundTasks, HTTPException
from fastapi.responses import JSONResponse, FileResponse, HTMLResponse
from fastapi.staticfiles import StaticFiles
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import Dict, Any, List, Optional
import json
import threading
import time
from pathlib import Path
import uuid
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent))

from utils.logger import LoggerMixin


class TrainingConfig(BaseModel):
    """训练配置模型"""
    experiment_name: Optional[str] = None
    model_type: str = "resnet18"
    batch_size: int = 32
    learning_rate: float = 0.001
    epochs: int = 30
    num_workers: int = 2
    data_dir: str = "data/ccpd/sample"
    save_dir: str = "models/ccpd"
    resume_from: Optional[str] = None


class TrainingStatus(BaseModel):
    """训练状态模型"""
    job_id: str
    status: str  # "pending", "running", "completed", "failed", "interrupted"
    current_epoch: int
    total_epochs: int
    train_loss: float
    val_loss: float
    train_acc: float
    val_acc: float
    best_acc: float
    progress: float
    message: str
    start_time: Optional[str] = None
    end_time: Optional[str] = None


class TrainingAPIServer(LoggerMixin):
    """训练API服务器"""
    
    def __init__(self):
        """初始化API服务器"""
        self.app = FastAPI(title="CCPD训练API", version="1.0.0")
        self.training_jobs = {}  # 存储训练任务

        # 添加CORS中间件
        self.app.add_middleware(
            CORSMiddleware,
            allow_origins=["*"],
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )

        self.setup_routes()

        self.logger.info("训练API服务器初始化完成")
    
    def setup_routes(self):
        """设置API路由"""

        @self.app.get("/", response_class=HTMLResponse)
        async def root():
            """根路径 - 返回监控面板"""
            monitor_file = Path(__file__).parent / "training_monitor.html"
            if monitor_file.exists():
                with open(monitor_file, 'r', encoding='utf-8') as f:
                    return f.read()
            else:
                return "<h1>CCPD训练API服务器</h1><p>监控面板文件不存在</p>"

        @self.app.post("/api/training/start")
        async def start_training(config: TrainingConfig, background_tasks: BackgroundTasks):
            """启动训练任务"""
            try:
                # 生成任务ID
                job_id = str(uuid.uuid4())
                
                # 创建训练状态
                status = TrainingStatus(
                    job_id=job_id,
                    status="pending",
                    current_epoch=0,
                    total_epochs=config.epochs,
                    train_loss=0.0,
                    val_loss=0.0,
                    train_acc=0.0,
                    val_acc=0.0,
                    best_acc=0.0,
                    progress=0.0,
                    message="训练任务已创建，等待开始",
                    start_time=datetime.now().isoformat()
                )
                
                self.training_jobs[job_id] = status
                
                # 在后台启动训练
                background_tasks.add_task(self._run_training, job_id, config.dict())
                
                return {"job_id": job_id, "message": "训练任务已启动", "status": "pending"}
                
            except Exception as e:
                self.logger.error(f"启动训练失败: {e}")
                raise HTTPException(status_code=500, detail=str(e))
        
        @self.app.get("/api/training/status/{job_id}")
        async def get_training_status(job_id: str):
            """获取训练状态"""
            if job_id not in self.training_jobs:
                raise HTTPException(status_code=404, detail="训练任务不存在")
            
            return self.training_jobs[job_id]
        
        @self.app.get("/api/training/jobs")
        async def list_training_jobs():
            """列出所有训练任务"""
            return {"jobs": list(self.training_jobs.values())}
        
        @self.app.post("/api/training/stop/{job_id}")
        async def stop_training(job_id: str):
            """停止训练任务"""
            if job_id not in self.training_jobs:
                raise HTTPException(status_code=404, detail="训练任务不存在")
            
            status = self.training_jobs[job_id]
            if status.status == "running":
                status.status = "interrupted"
                status.message = "训练已被用户中断"
                status.end_time = datetime.now().isoformat()
                
                return {"message": "训练任务已停止"}
            else:
                return {"message": "训练任务未在运行"}
        
        @self.app.get("/api/training/history/{job_id}")
        async def get_training_history(job_id: str):
            """获取训练历史数据"""
            if job_id not in self.training_jobs:
                raise HTTPException(status_code=404, detail="训练任务不存在")
            
            # 查找训练历史文件
            history_file = Path(f"experiments/{job_id}/data/training_history.json")
            if history_file.exists():
                with open(history_file, 'r', encoding='utf-8') as f:
                    history = json.load(f)
                return {"history": history}
            else:
                return {"history": []}
        
        @self.app.get("/api/training/plots/{job_id}/{plot_name}")
        async def get_training_plot(job_id: str, plot_name: str):
            """获取训练图表"""
            if job_id not in self.training_jobs:
                raise HTTPException(status_code=404, detail="训练任务不存在")
            
            plot_file = Path(f"experiments/{job_id}/plots/{plot_name}")
            if plot_file.exists():
                return FileResponse(plot_file)
            else:
                raise HTTPException(status_code=404, detail="图表文件不存在")
        
        @self.app.get("/api/training/report/{job_id}")
        async def get_training_report(job_id: str):
            """获取训练报告"""
            if job_id not in self.training_jobs:
                raise HTTPException(status_code=404, detail="训练任务不存在")
            
            report_file = Path(f"experiments/{job_id}/training_report.json")
            if report_file.exists():
                with open(report_file, 'r', encoding='utf-8') as f:
                    report = json.load(f)
                return report
            else:
                raise HTTPException(status_code=404, detail="训练报告不存在")
        
        @self.app.delete("/api/training/delete/{job_id}")
        async def delete_training_job(job_id: str):
            """删除训练任务"""
            if job_id not in self.training_jobs:
                raise HTTPException(status_code=404, detail="训练任务不存在")
            
            # 删除训练任务记录
            del self.training_jobs[job_id]
            
            # 可选：删除相关文件
            # experiment_dir = Path(f"experiments/{job_id}")
            # if experiment_dir.exists():
            #     shutil.rmtree(experiment_dir)
            
            return {"message": "训练任务已删除"}
    
    async def _run_training(self, job_id: str, config: Dict[str, Any]):
        """在后台运行训练"""
        try:
            # 更新状态为运行中
            status = self.training_jobs[job_id]
            status.status = "running"
            status.message = "训练正在进行中"
            
            # 导入训练模块（在这里导入避免启动时的依赖问题）
            from training.ccpd_dataset_trainer import CCPDDatasetDownloader, CCPDDataset
            from training.training_visualizer import TrainingVisualizer
            import torch
            import torch.nn as nn
            import torch.optim as optim
            from torch.utils.data import DataLoader
            import torchvision.transforms as transforms
            import torchvision.models as models
            
            # 设置实验名称
            if not config.get('experiment_name'):
                config['experiment_name'] = job_id
            
            # 创建可视化器
            visualizer = TrainingVisualizer(config['experiment_name'])
            visualizer.save_config(config)
            
            # 准备数据
            downloader = CCPDDatasetDownloader()
            dataset_path = downloader.download_sample_dataset()
            
            # 数据变换
            train_transform = transforms.Compose([
                transforms.Resize((64, 64)),
                transforms.RandomRotation(10),
                transforms.ColorJitter(brightness=0.2, contrast=0.2),
                transforms.ToTensor(),
                transforms.Normalize(mean=[0.485, 0.456, 0.406], 
                                   std=[0.229, 0.224, 0.225])
            ])
            
            val_transform = transforms.Compose([
                transforms.Resize((64, 64)),
                transforms.ToTensor(),
                transforms.Normalize(mean=[0.485, 0.456, 0.406], 
                                   std=[0.229, 0.224, 0.225])
            ])
            
            # 创建数据集
            train_dataset = CCPDDataset(dataset_path, "train", train_transform, "recognition")
            val_dataset = CCPDDataset(dataset_path, "val", val_transform, "recognition")
            
            # 创建数据加载器
            train_loader = DataLoader(train_dataset, batch_size=config["batch_size"], 
                                     shuffle=True, num_workers=config["num_workers"])
            val_loader = DataLoader(val_dataset, batch_size=config["batch_size"], 
                                   shuffle=False, num_workers=config["num_workers"])
            
            # 获取字符数量
            num_classes = len(train_dataset.char_mapping)

            # 创建模型
            device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
            model = models.resnet18(pretrained=True)
            model.fc = nn.Linear(model.fc.in_features, num_classes)
            model = model.to(device)

            # 创建优化器和损失函数
            optimizer = optim.AdamW(model.parameters(), lr=config["learning_rate"], weight_decay=1e-4)
            criterion = nn.CrossEntropyLoss()
            scheduler = optim.lr_scheduler.CosineAnnealingWarmRestarts(optimizer, T_0=10, T_mult=2)

            best_accuracy = 0.0
            
            # 开始训练
            for epoch in range(config["epochs"]):
                # 检查是否被中断
                if status.status == "interrupted":
                    break

                # 训练一个epoch
                train_loss, train_acc = self._train_epoch(model, train_loader, optimizer, criterion, device)
                val_loss, val_acc = self._validate_epoch(model, val_loader, criterion, device)

                # 更新最佳准确率
                if val_acc > best_accuracy:
                    best_accuracy = val_acc
                
                # 记录训练数据
                epoch_data = {
                    'epoch': epoch + 1,
                    'train_loss': train_loss,
                    'train_acc': train_acc,
                    'val_loss': val_loss,
                    'val_acc': val_acc,
                    'lr': optimizer.param_groups[0]['lr'],
                    'time': time.time()
                }

                visualizer.log_epoch(epoch_data)

                # 更新API状态
                status.current_epoch = epoch + 1
                status.train_loss = train_loss
                status.val_loss = val_loss
                status.train_acc = train_acc
                status.val_acc = val_acc
                status.best_acc = best_accuracy
                status.progress = (epoch + 1) / config["epochs"] * 100
                status.message = f"Epoch {epoch + 1}/{config['epochs']} - Val Acc: {val_acc:.4f}"

                # 学习率调度
                scheduler.step()
                
                # 每5个epoch生成一次图表
                if (epoch + 1) % 5 == 0:
                    visualizer.plot_training_curves(save_plots=True)
            
            # 训练完成
            if status.status != "interrupted":
                status.status = "completed"
                status.message = f"训练完成！最佳验证准确率: {best_accuracy:.4f}"
                status.end_time = datetime.now().isoformat()
                
                # 生成最终报告和图表
                visualizer.plot_training_curves(save_plots=True)
                visualizer.plot_loss_analysis(save_plots=True)
                visualizer.plot_accuracy_analysis(save_plots=True)
                visualizer.create_comprehensive_dashboard(save_plots=True)
                visualizer.generate_training_report()
                visualizer.save_training_data_csv()
            
        except Exception as e:
            # 训练失败
            status.status = "failed"
            status.message = f"训练失败: {str(e)}"
            status.end_time = datetime.now().isoformat()
            self.logger.error(f"训练任务 {job_id} 失败: {e}")

    def _train_epoch(self, model, train_loader, optimizer, criterion, device):
        """训练一个epoch"""
        model.train()
        total_loss = 0.0
        correct = 0
        total = 0

        for batch_idx, (data, target) in enumerate(train_loader):
            data, target = data.to(device), target.to(device)

            # 如果target是序列，只使用第一个字符进行训练
            if len(target.shape) > 1:
                target = target[:, 0]  # 使用省份字符

            optimizer.zero_grad()
            output = model(data)
            loss = criterion(output, target)
            loss.backward()
            optimizer.step()

            total_loss += loss.item()
            pred = output.argmax(dim=1, keepdim=True)
            correct += pred.eq(target.view_as(pred)).sum().item()
            total += target.size(0)

        avg_loss = total_loss / len(train_loader)
        accuracy = correct / total

        return avg_loss, accuracy

    def _validate_epoch(self, model, val_loader, criterion, device):
        """验证一个epoch"""
        model.eval()
        total_loss = 0.0
        correct = 0
        total = 0

        with torch.no_grad():
            for data, target in val_loader:
                data, target = data.to(device), target.to(device)

                # 如果target是序列，只使用第一个字符进行验证
                if len(target.shape) > 1:
                    target = target[:, 0]

                output = model(data)
                loss = criterion(output, target)

                total_loss += loss.item()
                pred = output.argmax(dim=1, keepdim=True)
                correct += pred.eq(target.view_as(pred)).sum().item()
                total += target.size(0)

        avg_loss = total_loss / len(val_loader)
        accuracy = correct / total

        return avg_loss, accuracy

    def run(self, host: str = "127.0.0.1", port: int = 8001):
        """运行API服务器"""
        self.logger.info(f"启动训练API服务器: http://{host}:{port}")
        uvicorn.run(self.app, host=host, port=port)


def main():
    """主函数"""
    server = TrainingAPIServer()
    server.run()


if __name__ == "__main__":
    main()
