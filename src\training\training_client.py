#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
训练客户端
Training Client

通过API接口调用训练服务，避免程序间相互影响

Author: License Plate Recognition Team
Date: 2025-07-30
"""

import requests
import time
import json
from typing import Dict, Any, Optional
from pathlib import Path
import sys

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent))

from utils.logger import LoggerMixin


class TrainingClient(LoggerMixin):
    """训练客户端"""
    
    def __init__(self, api_base_url: str = "http://127.0.0.1:8001"):
        """
        初始化训练客户端
        
        Args:
            api_base_url (str): API服务器地址
        """
        self.api_base_url = api_base_url.rstrip('/')
        self.session = requests.Session()
        
        self.logger.info(f"训练客户端初始化完成，API地址: {api_base_url}")
    
    def start_training(self, config: Dict[str, Any]) -> str:
        """
        启动训练任务
        
        Args:
            config (Dict[str, Any]): 训练配置
            
        Returns:
            str: 任务ID
        """
        try:
            response = self.session.post(
                f"{self.api_base_url}/api/training/start",
                json=config
            )
            response.raise_for_status()
            
            result = response.json()
            job_id = result["job_id"]
            
            self.logger.info(f"训练任务已启动，任务ID: {job_id}")
            return job_id
            
        except requests.exceptions.RequestException as e:
            self.logger.error(f"启动训练失败: {e}")
            raise
    
    def get_status(self, job_id: str) -> Dict[str, Any]:
        """
        获取训练状态
        
        Args:
            job_id (str): 任务ID
            
        Returns:
            Dict[str, Any]: 训练状态
        """
        try:
            response = self.session.get(
                f"{self.api_base_url}/api/training/status/{job_id}"
            )
            response.raise_for_status()
            
            return response.json()
            
        except requests.exceptions.RequestException as e:
            self.logger.error(f"获取训练状态失败: {e}")
            raise
    
    def monitor_training(self, job_id: str, update_interval: int = 5) -> None:
        """
        监控训练进度
        
        Args:
            job_id (str): 任务ID
            update_interval (int): 更新间隔（秒）
        """
        print(f"开始监控训练任务: {job_id}")
        print("=" * 80)
        
        last_epoch = 0
        
        while True:
            try:
                status = self.get_status(job_id)
                
                # 显示进度
                current_epoch = status["current_epoch"]
                total_epochs = status["total_epochs"]
                progress = status["progress"]
                train_loss = status["train_loss"]
                val_loss = status["val_loss"]
                train_acc = status["train_acc"]
                val_acc = status["val_acc"]
                best_acc = status["best_acc"]
                message = status["message"]
                
                # 只在epoch更新时显示详细信息
                if current_epoch > last_epoch:
                    print(f"Epoch {current_epoch}/{total_epochs} ({progress:.1f}%)")
                    print(f"  训练损失: {train_loss:.4f} | 验证损失: {val_loss:.4f}")
                    print(f"  训练准确率: {train_acc:.4f} | 验证准确率: {val_acc:.4f}")
                    print(f"  最佳准确率: {best_acc:.4f}")
                    print(f"  状态: {message}")
                    print("-" * 80)
                    last_epoch = current_epoch
                
                # 检查训练是否完成
                if status["status"] in ["completed", "failed", "interrupted"]:
                    print(f"\n训练结束，最终状态: {status['status']}")
                    print(f"最终消息: {message}")
                    break
                
                time.sleep(update_interval)
                
            except KeyboardInterrupt:
                print("\n用户中断监控")
                break
            except Exception as e:
                self.logger.error(f"监控训练失败: {e}")
                time.sleep(update_interval)
    
    def stop_training(self, job_id: str) -> bool:
        """
        停止训练任务
        
        Args:
            job_id (str): 任务ID
            
        Returns:
            bool: 是否成功停止
        """
        try:
            response = self.session.post(
                f"{self.api_base_url}/api/training/stop/{job_id}"
            )
            response.raise_for_status()
            
            result = response.json()
            self.logger.info(f"训练任务已停止: {result['message']}")
            return True
            
        except requests.exceptions.RequestException as e:
            self.logger.error(f"停止训练失败: {e}")
            return False
    
    def get_training_history(self, job_id: str) -> list:
        """
        获取训练历史
        
        Args:
            job_id (str): 任务ID
            
        Returns:
            list: 训练历史数据
        """
        try:
            response = self.session.get(
                f"{self.api_base_url}/api/training/history/{job_id}"
            )
            response.raise_for_status()
            
            result = response.json()
            return result["history"]
            
        except requests.exceptions.RequestException as e:
            self.logger.error(f"获取训练历史失败: {e}")
            return []
    
    def get_training_report(self, job_id: str) -> Dict[str, Any]:
        """
        获取训练报告
        
        Args:
            job_id (str): 任务ID
            
        Returns:
            Dict[str, Any]: 训练报告
        """
        try:
            response = self.session.get(
                f"{self.api_base_url}/api/training/report/{job_id}"
            )
            response.raise_for_status()
            
            return response.json()
            
        except requests.exceptions.RequestException as e:
            self.logger.error(f"获取训练报告失败: {e}")
            return {}
    
    def download_plot(self, job_id: str, plot_name: str, save_path: str) -> bool:
        """
        下载训练图表
        
        Args:
            job_id (str): 任务ID
            plot_name (str): 图表名称
            save_path (str): 保存路径
            
        Returns:
            bool: 是否成功下载
        """
        try:
            response = self.session.get(
                f"{self.api_base_url}/api/training/plots/{job_id}/{plot_name}"
            )
            response.raise_for_status()
            
            with open(save_path, 'wb') as f:
                f.write(response.content)
            
            self.logger.info(f"图表已下载: {save_path}")
            return True
            
        except requests.exceptions.RequestException as e:
            self.logger.error(f"下载图表失败: {e}")
            return False
    
    def list_jobs(self) -> list:
        """
        列出所有训练任务
        
        Returns:
            list: 训练任务列表
        """
        try:
            response = self.session.get(
                f"{self.api_base_url}/api/training/jobs"
            )
            response.raise_for_status()
            
            result = response.json()
            return result["jobs"]
            
        except requests.exceptions.RequestException as e:
            self.logger.error(f"获取任务列表失败: {e}")
            return []
    
    def delete_job(self, job_id: str) -> bool:
        """
        删除训练任务
        
        Args:
            job_id (str): 任务ID
            
        Returns:
            bool: 是否成功删除
        """
        try:
            response = self.session.delete(
                f"{self.api_base_url}/api/training/delete/{job_id}"
            )
            response.raise_for_status()
            
            result = response.json()
            self.logger.info(f"训练任务已删除: {result['message']}")
            return True
            
        except requests.exceptions.RequestException as e:
            self.logger.error(f"删除训练任务失败: {e}")
            return False


def main():
    """主函数 - 演示客户端使用"""
    print("=" * 60)
    print("CCPD训练客户端")
    print("=" * 60)
    
    # 创建客户端
    client = TrainingClient()
    
    # 训练配置
    config = {
        "experiment_name": "ccpd_demo_training",
        "model_type": "resnet18",
        "batch_size": 32,
        "learning_rate": 0.001,
        "epochs": 20,
        "num_workers": 2
    }
    
    try:
        # 启动训练
        print("启动训练任务...")
        job_id = client.start_training(config)
        
        # 监控训练进度
        client.monitor_training(job_id)
        
        # 获取最终报告
        print("\n获取训练报告...")
        report = client.get_training_report(job_id)
        if report:
            print(f"最佳验证准确率: {report.get('accuracy_metrics', {}).get('best_val_acc', 'N/A')}")
            print(f"训练总时间: {report.get('time_metrics', {}).get('total_training_time', 'N/A')} 秒")
        
        # 下载图表
        print("\n下载训练图表...")
        plots_dir = Path("downloaded_plots")
        plots_dir.mkdir(exist_ok=True)
        
        plot_names = ["training_curves.png", "loss_analysis.png", "accuracy_analysis.png", 
                     "comprehensive_dashboard.png"]
        
        for plot_name in plot_names:
            save_path = plots_dir / f"{job_id}_{plot_name}"
            if client.download_plot(job_id, plot_name, str(save_path)):
                print(f"  已下载: {plot_name}")
        
        print(f"\n训练完成！任务ID: {job_id}")
        
    except KeyboardInterrupt:
        print("\n用户中断")
    except Exception as e:
        print(f"训练过程中发生错误: {e}")


if __name__ == "__main__":
    main()
