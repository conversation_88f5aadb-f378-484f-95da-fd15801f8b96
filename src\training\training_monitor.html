<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CCPD训练监控面板</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 300;
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .content {
            padding: 30px;
        }

        .control-panel {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .control-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            border: 1px solid #e9ecef;
        }

        .control-card h3 {
            color: #495057;
            margin-bottom: 15px;
            font-size: 1.2em;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            color: #6c757d;
            font-weight: 500;
        }

        .form-group input, .form-group select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ced4da;
            border-radius: 5px;
            font-size: 14px;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            margin: 5px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .btn-danger {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
        }

        .status-panel {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 30px;
        }

        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }

        .status-item {
            text-align: center;
            padding: 15px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .status-item .value {
            font-size: 2em;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 5px;
        }

        .status-item .label {
            color: #6c757d;
            font-size: 0.9em;
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 20px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4facfe 0%, #00f2fe 100%);
            transition: width 0.3s ease;
            border-radius: 10px;
        }

        .jobs-list {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
        }

        .job-item {
            background: white;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
            border-left: 4px solid #667eea;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }

        .job-item.completed {
            border-left-color: #28a745;
        }

        .job-item.failed {
            border-left-color: #dc3545;
        }

        .job-item.running {
            border-left-color: #ffc107;
        }

        .log-panel {
            background: #2d3748;
            color: #e2e8f0;
            border-radius: 10px;
            padding: 20px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 300px;
            overflow-y: auto;
            margin-top: 20px;
        }

        .hidden {
            display: none;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .loading {
            animation: pulse 1.5s infinite;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚗 CCPD训练监控面板</h1>
            <p>Chinese City Parking Dataset Training Monitor</p>
        </div>

        <div class="content">
            <!-- 控制面板 -->
            <div class="control-panel">
                <div class="control-card">
                    <h3>🚀 启动新训练</h3>
                    <div class="form-group">
                        <label>实验名称</label>
                        <input type="text" id="experimentName" placeholder="自动生成">
                    </div>
                    <div class="form-group">
                        <label>模型类型</label>
                        <select id="modelType">
                            <option value="resnet18">ResNet18</option>
                            <option value="efficientnet">EfficientNet</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>批次大小</label>
                        <input type="number" id="batchSize" value="32" min="1" max="128">
                    </div>
                    <div class="form-group">
                        <label>学习率</label>
                        <input type="number" id="learningRate" value="0.001" step="0.0001" min="0.0001" max="0.1">
                    </div>
                    <div class="form-group">
                        <label>训练轮数</label>
                        <input type="number" id="epochs" value="20" min="1" max="200">
                    </div>
                    <button class="btn" onclick="startTraining()">开始训练</button>
                </div>

                <div class="control-card">
                    <h3>📊 当前训练状态</h3>
                    <div id="currentStatus">
                        <p>暂无活动训练任务</p>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" id="progressFill" style="width: 0%"></div>
                    </div>
                    <button class="btn btn-danger" onclick="stopCurrentTraining()" id="stopBtn" disabled>停止训练</button>
                </div>
            </div>

            <!-- 状态面板 -->
            <div class="status-panel">
                <h3>📈 训练指标</h3>
                <div class="status-grid">
                    <div class="status-item">
                        <div class="value" id="currentEpoch">0</div>
                        <div class="label">当前轮数</div>
                    </div>
                    <div class="status-item">
                        <div class="value" id="trainLoss">0.000</div>
                        <div class="label">训练损失</div>
                    </div>
                    <div class="status-item">
                        <div class="value" id="valLoss">0.000</div>
                        <div class="label">验证损失</div>
                    </div>
                    <div class="status-item">
                        <div class="value" id="trainAcc">0.000</div>
                        <div class="label">训练准确率</div>
                    </div>
                    <div class="status-item">
                        <div class="value" id="valAcc">0.000</div>
                        <div class="label">验证准确率</div>
                    </div>
                    <div class="status-item">
                        <div class="value" id="bestAcc">0.000</div>
                        <div class="label">最佳准确率</div>
                    </div>
                </div>
            </div>

            <!-- 任务列表 -->
            <div class="jobs-list">
                <h3>📋 训练任务列表</h3>
                <div id="jobsList">
                    <p>正在加载任务列表...</p>
                </div>
            </div>

            <!-- 日志面板 -->
            <div class="log-panel" id="logPanel">
                <div>系统日志:</div>
                <div id="logContent">等待连接到训练API服务器...</div>
            </div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://127.0.0.1:8001';
        let currentJobId = null;
        let updateInterval = null;

        // 启动训练
        async function startTraining() {
            const config = {
                experiment_name: document.getElementById('experimentName').value || null,
                model_type: document.getElementById('modelType').value,
                batch_size: parseInt(document.getElementById('batchSize').value),
                learning_rate: parseFloat(document.getElementById('learningRate').value),
                epochs: parseInt(document.getElementById('epochs').value),
                num_workers: 2
            };

            try {
                const response = await fetch(`${API_BASE}/api/training/start`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(config)
                });

                const result = await response.json();
                currentJobId = result.job_id;
                
                addLog(`训练任务已启动: ${currentJobId}`);
                document.getElementById('stopBtn').disabled = false;
                
                // 开始监控
                startMonitoring();
                
            } catch (error) {
                addLog(`启动训练失败: ${error.message}`);
            }
        }

        // 停止当前训练
        async function stopCurrentTraining() {
            if (!currentJobId) return;

            try {
                const response = await fetch(`${API_BASE}/api/training/stop/${currentJobId}`, {
                    method: 'POST'
                });

                const result = await response.json();
                addLog(`训练已停止: ${result.message}`);
                
            } catch (error) {
                addLog(`停止训练失败: ${error.message}`);
            }
        }

        // 开始监控
        function startMonitoring() {
            if (updateInterval) {
                clearInterval(updateInterval);
            }

            updateInterval = setInterval(async () => {
                if (currentJobId) {
                    await updateTrainingStatus();
                }
                await updateJobsList();
            }, 2000);
        }

        // 更新训练状态
        async function updateTrainingStatus() {
            try {
                const response = await fetch(`${API_BASE}/api/training/status/${currentJobId}`);
                const status = await response.json();

                // 更新状态显示
                document.getElementById('currentEpoch').textContent = status.current_epoch;
                document.getElementById('trainLoss').textContent = status.train_loss.toFixed(4);
                document.getElementById('valLoss').textContent = status.val_loss.toFixed(4);
                document.getElementById('trainAcc').textContent = status.train_acc.toFixed(4);
                document.getElementById('valAcc').textContent = status.val_acc.toFixed(4);
                document.getElementById('bestAcc').textContent = status.best_acc.toFixed(4);

                // 更新进度条
                document.getElementById('progressFill').style.width = `${status.progress}%`;

                // 更新当前状态
                document.getElementById('currentStatus').innerHTML = `
                    <p><strong>任务ID:</strong> ${status.job_id}</p>
                    <p><strong>状态:</strong> ${status.status}</p>
                    <p><strong>消息:</strong> ${status.message}</p>
                `;

                // 检查是否完成
                if (status.status === 'completed' || status.status === 'failed' || status.status === 'interrupted') {
                    currentJobId = null;
                    document.getElementById('stopBtn').disabled = true;
                    clearInterval(updateInterval);
                    addLog(`训练任务完成: ${status.status}`);
                }

            } catch (error) {
                addLog(`获取状态失败: ${error.message}`);
            }
        }

        // 更新任务列表
        async function updateJobsList() {
            try {
                const response = await fetch(`${API_BASE}/api/training/jobs`);
                const result = await response.json();
                const jobs = result.jobs;

                const jobsList = document.getElementById('jobsList');
                jobsList.innerHTML = '';

                if (jobs.length === 0) {
                    jobsList.innerHTML = '<p>暂无训练任务</p>';
                    return;
                }

                jobs.forEach(job => {
                    const jobDiv = document.createElement('div');
                    jobDiv.className = `job-item ${job.status}`;
                    jobDiv.innerHTML = `
                        <div style="display: flex; justify-content: between; align-items: center;">
                            <div>
                                <strong>${job.job_id.substring(0, 8)}...</strong>
                                <span class="badge ${job.status}">${job.status}</span>
                            </div>
                            <div style="text-align: right;">
                                <div>Epoch: ${job.current_epoch}/${job.total_epochs}</div>
                                <div>Best Acc: ${job.best_acc.toFixed(4)}</div>
                            </div>
                        </div>
                        <div style="margin-top: 10px; font-size: 0.9em; color: #6c757d;">
                            ${job.message}
                        </div>
                    `;
                    jobsList.appendChild(jobDiv);
                });

            } catch (error) {
                addLog(`获取任务列表失败: ${error.message}`);
            }
        }

        // 添加日志
        function addLog(message) {
            const logContent = document.getElementById('logContent');
            const timestamp = new Date().toLocaleTimeString();
            logContent.innerHTML += `<br>[${timestamp}] ${message}`;
            logContent.scrollTop = logContent.scrollHeight;
        }

        // 页面加载时初始化
        window.onload = function() {
            addLog('训练监控面板已启动');
            updateJobsList();
            
            // 每30秒更新一次任务列表
            setInterval(updateJobsList, 30000);
        };
    </script>
</body>
</html>
