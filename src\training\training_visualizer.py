#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
训练可视化器
Training Visualizer

专业的训练数据记录、可视化和科研图表生成系统

Author: License Plate Recognition Team
Date: 2025-07-30
"""

import os
import sys
import json
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import seaborn as sns
from typing import Dict, Any, List, Tuple, Optional
from pathlib import Path
import pandas as pd
from datetime import datetime
import pickle
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent))

from utils.logger import LoggerMixin


class TrainingVisualizer(LoggerMixin):
    """训练可视化器"""
    
    def __init__(self, experiment_name: str = None, save_dir: str = "experiments"):
        """
        初始化可视化器
        
        Args:
            experiment_name (str): 实验名称
            save_dir (str): 保存目录
        """
        self.save_dir = Path(save_dir)
        self.save_dir.mkdir(parents=True, exist_ok=True)
        
        # 生成实验名称
        if experiment_name is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            experiment_name = f"ccpd_training_{timestamp}"
        
        self.experiment_name = experiment_name
        self.experiment_dir = self.save_dir / experiment_name
        self.experiment_dir.mkdir(parents=True, exist_ok=True)
        
        # 创建子目录
        self.plots_dir = self.experiment_dir / "plots"
        self.data_dir = self.experiment_dir / "data"
        self.models_dir = self.experiment_dir / "models"
        self.logs_dir = self.experiment_dir / "logs"
        
        for dir_path in [self.plots_dir, self.data_dir, self.models_dir, self.logs_dir]:
            dir_path.mkdir(exist_ok=True)
        
        # 训练数据
        self.training_history = []
        self.config = {}
        self.metrics = {}
        
        # 设置绘图样式
        sns.set_style("whitegrid")
        plt.style.use('seaborn-v0_8')
        
        self.logger.info(f"训练可视化器初始化完成，实验: {experiment_name}")
    
    def save_config(self, config: Dict[str, Any]) -> None:
        """保存实验配置"""
        self.config = config.copy()
        
        # 添加实验元数据
        metadata = {
            "experiment_name": self.experiment_name,
            "start_time": datetime.now().isoformat(),
            "config": config
        }
        
        # 保存配置文件
        config_file = self.experiment_dir / "config.json"
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(metadata, f, ensure_ascii=False, indent=2)
        
        self.logger.info(f"实验配置已保存: {config_file}")
    
    def log_epoch(self, epoch_data: Dict[str, Any]) -> None:
        """记录单个epoch的数据"""
        # 添加时间戳
        epoch_data['timestamp'] = datetime.now().isoformat()
        
        self.training_history.append(epoch_data)
        
        # 实时保存训练历史
        history_file = self.data_dir / "training_history.json"
        with open(history_file, 'w', encoding='utf-8') as f:
            json.dump(self.training_history, f, ensure_ascii=False, indent=2)
        
        # 保存为pickle格式（便于后续分析）
        pickle_file = self.data_dir / "training_history.pkl"
        with open(pickle_file, 'wb') as f:
            pickle.dump(self.training_history, f)
    
    def plot_training_curves(self, save_plots: bool = True) -> None:
        """绘制训练曲线"""
        if not self.training_history:
            self.logger.warning("没有训练历史数据")
            return
        
        # 提取数据
        epochs = [item['epoch'] for item in self.training_history]
        train_loss = [item['train_loss'] for item in self.training_history]
        val_loss = [item['val_loss'] for item in self.training_history]
        train_acc = [item['train_acc'] for item in self.training_history]
        val_acc = [item['val_acc'] for item in self.training_history]
        learning_rates = [item.get('lr', 0) for item in self.training_history]
        
        # 创建子图
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle(f'训练曲线 - {self.experiment_name}', fontsize=16, fontweight='bold')
        
        # 损失曲线
        axes[0, 0].plot(epochs, train_loss, 'b-', label='训练损失', linewidth=2)
        axes[0, 0].plot(epochs, val_loss, 'r-', label='验证损失', linewidth=2)
        axes[0, 0].set_title('损失函数曲线', fontsize=14, fontweight='bold')
        axes[0, 0].set_xlabel('Epoch')
        axes[0, 0].set_ylabel('Loss')
        axes[0, 0].legend()
        axes[0, 0].grid(True, alpha=0.3)
        
        # 准确率曲线
        axes[0, 1].plot(epochs, train_acc, 'b-', label='训练准确率', linewidth=2)
        axes[0, 1].plot(epochs, val_acc, 'r-', label='验证准确率', linewidth=2)
        axes[0, 1].set_title('准确率曲线', fontsize=14, fontweight='bold')
        axes[0, 1].set_xlabel('Epoch')
        axes[0, 1].set_ylabel('Accuracy')
        axes[0, 1].legend()
        axes[0, 1].grid(True, alpha=0.3)
        
        # 学习率曲线
        axes[1, 0].plot(epochs, learning_rates, 'g-', linewidth=2)
        axes[1, 0].set_title('学习率变化', fontsize=14, fontweight='bold')
        axes[1, 0].set_xlabel('Epoch')
        axes[1, 0].set_ylabel('Learning Rate')
        axes[1, 0].grid(True, alpha=0.3)
        axes[1, 0].set_yscale('log')
        
        # 训练时间分析
        if 'time' in self.training_history[0]:
            epoch_times = [item['time'] for item in self.training_history]
            axes[1, 1].plot(epochs, epoch_times, 'purple', linewidth=2)
            axes[1, 1].set_title('每轮训练时间', fontsize=14, fontweight='bold')
            axes[1, 1].set_xlabel('Epoch')
            axes[1, 1].set_ylabel('Time (seconds)')
            axes[1, 1].grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if save_plots:
            plot_file = self.plots_dir / "training_curves.png"
            plt.savefig(plot_file, dpi=300, bbox_inches='tight')
            self.logger.info(f"训练曲线已保存: {plot_file}")
        
        plt.show()
    
    def plot_loss_analysis(self, save_plots: bool = True) -> None:
        """绘制详细的损失分析图"""
        if not self.training_history:
            return
        
        epochs = [item['epoch'] for item in self.training_history]
        train_loss = [item['train_loss'] for item in self.training_history]
        val_loss = [item['val_loss'] for item in self.training_history]
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle(f'损失函数详细分析 - {self.experiment_name}', fontsize=16, fontweight='bold')
        
        # 原始损失曲线
        axes[0, 0].plot(epochs, train_loss, 'b-', label='训练损失', linewidth=2)
        axes[0, 0].plot(epochs, val_loss, 'r-', label='验证损失', linewidth=2)
        axes[0, 0].set_title('损失曲线', fontsize=14)
        axes[0, 0].set_xlabel('Epoch')
        axes[0, 0].set_ylabel('Loss')
        axes[0, 0].legend()
        axes[0, 0].grid(True, alpha=0.3)
        
        # 对数损失曲线
        axes[0, 1].semilogy(epochs, train_loss, 'b-', label='训练损失', linewidth=2)
        axes[0, 1].semilogy(epochs, val_loss, 'r-', label='验证损失', linewidth=2)
        axes[0, 1].set_title('对数损失曲线', fontsize=14)
        axes[0, 1].set_xlabel('Epoch')
        axes[0, 1].set_ylabel('Log Loss')
        axes[0, 1].legend()
        axes[0, 1].grid(True, alpha=0.3)
        
        # 损失差值分析
        loss_diff = [abs(t - v) for t, v in zip(train_loss, val_loss)]
        axes[1, 0].plot(epochs, loss_diff, 'purple', linewidth=2)
        axes[1, 0].set_title('训练-验证损失差值', fontsize=14)
        axes[1, 0].set_xlabel('Epoch')
        axes[1, 0].set_ylabel('|Train Loss - Val Loss|')
        axes[1, 0].grid(True, alpha=0.3)
        
        # 损失平滑曲线（移动平均）
        if len(train_loss) > 5:
            window = min(5, len(train_loss) // 4)
            train_smooth = pd.Series(train_loss).rolling(window=window).mean()
            val_smooth = pd.Series(val_loss).rolling(window=window).mean()
            
            axes[1, 1].plot(epochs, train_smooth, 'b-', label=f'训练损失(MA{window})', linewidth=2)
            axes[1, 1].plot(epochs, val_smooth, 'r-', label=f'验证损失(MA{window})', linewidth=2)
            axes[1, 1].set_title('平滑损失曲线', fontsize=14)
            axes[1, 1].set_xlabel('Epoch')
            axes[1, 1].set_ylabel('Smoothed Loss')
            axes[1, 1].legend()
            axes[1, 1].grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if save_plots:
            plot_file = self.plots_dir / "loss_analysis.png"
            plt.savefig(plot_file, dpi=300, bbox_inches='tight')
            self.logger.info(f"损失分析图已保存: {plot_file}")
        
        plt.show()
    
    def plot_accuracy_analysis(self, save_plots: bool = True) -> None:
        """绘制准确率详细分析图"""
        if not self.training_history:
            return
        
        epochs = [item['epoch'] for item in self.training_history]
        train_acc = [item['train_acc'] for item in self.training_history]
        val_acc = [item['val_acc'] for item in self.training_history]
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle(f'准确率详细分析 - {self.experiment_name}', fontsize=16, fontweight='bold')
        
        # 准确率曲线
        axes[0, 0].plot(epochs, train_acc, 'b-', label='训练准确率', linewidth=2)
        axes[0, 0].plot(epochs, val_acc, 'r-', label='验证准确率', linewidth=2)
        axes[0, 0].set_title('准确率曲线', fontsize=14)
        axes[0, 0].set_xlabel('Epoch')
        axes[0, 0].set_ylabel('Accuracy')
        axes[0, 0].legend()
        axes[0, 0].grid(True, alpha=0.3)
        axes[0, 0].set_ylim([0, 1])
        
        # 准确率提升分析
        train_acc_diff = [0] + [train_acc[i] - train_acc[i-1] for i in range(1, len(train_acc))]
        val_acc_diff = [0] + [val_acc[i] - val_acc[i-1] for i in range(1, len(val_acc))]
        
        axes[0, 1].plot(epochs, train_acc_diff, 'b-', label='训练准确率变化', linewidth=2)
        axes[0, 1].plot(epochs, val_acc_diff, 'r-', label='验证准确率变化', linewidth=2)
        axes[0, 1].axhline(y=0, color='black', linestyle='--', alpha=0.5)
        axes[0, 1].set_title('准确率变化率', fontsize=14)
        axes[0, 1].set_xlabel('Epoch')
        axes[0, 1].set_ylabel('Accuracy Change')
        axes[0, 1].legend()
        axes[0, 1].grid(True, alpha=0.3)
        
        # 过拟合分析
        overfitting = [t - v for t, v in zip(train_acc, val_acc)]
        axes[1, 0].plot(epochs, overfitting, 'orange', linewidth=2)
        axes[1, 0].axhline(y=0, color='black', linestyle='--', alpha=0.5)
        axes[1, 0].set_title('过拟合分析 (训练-验证准确率)', fontsize=14)
        axes[1, 0].set_xlabel('Epoch')
        axes[1, 0].set_ylabel('Train Acc - Val Acc')
        axes[1, 0].grid(True, alpha=0.3)
        
        # 最佳准确率标记
        best_val_epoch = epochs[np.argmax(val_acc)]
        best_val_acc = max(val_acc)
        
        axes[1, 1].plot(epochs, val_acc, 'r-', linewidth=2, label='验证准确率')
        axes[1, 1].scatter([best_val_epoch], [best_val_acc], color='gold', s=100, 
                          label=f'最佳: {best_val_acc:.4f} (Epoch {best_val_epoch})', zorder=5)
        axes[1, 1].set_title('最佳验证准确率', fontsize=14)
        axes[1, 1].set_xlabel('Epoch')
        axes[1, 1].set_ylabel('Validation Accuracy')
        axes[1, 1].legend()
        axes[1, 1].grid(True, alpha=0.3)
        axes[1, 1].set_ylim([0, 1])
        
        plt.tight_layout()
        
        if save_plots:
            plot_file = self.plots_dir / "accuracy_analysis.png"
            plt.savefig(plot_file, dpi=300, bbox_inches='tight')
            self.logger.info(f"准确率分析图已保存: {plot_file}")
        
        plt.show()
    
    def generate_training_report(self) -> Dict[str, Any]:
        """生成训练报告"""
        if not self.training_history:
            return {}
        
        # 提取数据
        train_losses = [item['train_loss'] for item in self.training_history]
        val_losses = [item['val_loss'] for item in self.training_history]
        train_accs = [item['train_acc'] for item in self.training_history]
        val_accs = [item['val_acc'] for item in self.training_history]
        
        # 计算统计指标
        report = {
            "experiment_info": {
                "name": self.experiment_name,
                "total_epochs": len(self.training_history),
                "config": self.config
            },
            "loss_metrics": {
                "final_train_loss": train_losses[-1],
                "final_val_loss": val_losses[-1],
                "min_train_loss": min(train_losses),
                "min_val_loss": min(val_losses),
                "loss_convergence": abs(train_losses[-1] - train_losses[-5]) if len(train_losses) >= 5 else None
            },
            "accuracy_metrics": {
                "final_train_acc": train_accs[-1],
                "final_val_acc": val_accs[-1],
                "best_train_acc": max(train_accs),
                "best_val_acc": max(val_accs),
                "best_val_epoch": np.argmax(val_accs) + 1
            },
            "training_stability": {
                "train_loss_std": np.std(train_losses),
                "val_loss_std": np.std(val_losses),
                "train_acc_std": np.std(train_accs),
                "val_acc_std": np.std(val_accs)
            }
        }
        
        # 计算训练时间统计
        if 'time' in self.training_history[0]:
            epoch_times = [item['time'] for item in self.training_history]
            report["time_metrics"] = {
                "total_training_time": sum(epoch_times),
                "avg_epoch_time": np.mean(epoch_times),
                "min_epoch_time": min(epoch_times),
                "max_epoch_time": max(epoch_times)
            }
        
        # 保存报告
        report_file = self.experiment_dir / "training_report.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        self.logger.info(f"训练报告已保存: {report_file}")
        return report

    def plot_learning_rate_schedule(self, save_plots: bool = True) -> None:
        """绘制学习率调度图"""
        if not self.training_history or 'lr' not in self.training_history[0]:
            return

        epochs = [item['epoch'] for item in self.training_history]
        learning_rates = [item['lr'] for item in self.training_history]

        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
        fig.suptitle(f'学习率调度分析 - {self.experiment_name}', fontsize=16, fontweight='bold')

        # 线性尺度
        ax1.plot(epochs, learning_rates, 'g-', linewidth=2, marker='o', markersize=3)
        ax1.set_title('学习率变化 (线性尺度)', fontsize=14)
        ax1.set_xlabel('Epoch')
        ax1.set_ylabel('Learning Rate')
        ax1.grid(True, alpha=0.3)

        # 对数尺度
        ax2.semilogy(epochs, learning_rates, 'g-', linewidth=2, marker='o', markersize=3)
        ax2.set_title('学习率变化 (对数尺度)', fontsize=14)
        ax2.set_xlabel('Epoch')
        ax2.set_ylabel('Learning Rate (log scale)')
        ax2.grid(True, alpha=0.3)

        plt.tight_layout()

        if save_plots:
            plot_file = self.plots_dir / "learning_rate_schedule.png"
            plt.savefig(plot_file, dpi=300, bbox_inches='tight')
            self.logger.info(f"学习率调度图已保存: {plot_file}")

        plt.show()

    def plot_training_efficiency(self, save_plots: bool = True) -> None:
        """绘制训练效率分析图"""
        if not self.training_history:
            return

        epochs = [item['epoch'] for item in self.training_history]
        val_accs = [item['val_acc'] for item in self.training_history]

        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle(f'训练效率分析 - {self.experiment_name}', fontsize=16, fontweight='bold')

        # 准确率提升效率
        acc_improvements = [0] + [max(0, val_accs[i] - max(val_accs[:i+1])) for i in range(1, len(val_accs))]
        axes[0, 0].plot(epochs, acc_improvements, 'b-', linewidth=2)
        axes[0, 0].set_title('准确率提升效率', fontsize=14)
        axes[0, 0].set_xlabel('Epoch')
        axes[0, 0].set_ylabel('Accuracy Improvement')
        axes[0, 0].grid(True, alpha=0.3)

        # 累积最佳准确率
        cumulative_best = [max(val_accs[:i+1]) for i in range(len(val_accs))]
        axes[0, 1].plot(epochs, cumulative_best, 'r-', linewidth=2)
        axes[0, 1].set_title('累积最佳验证准确率', fontsize=14)
        axes[0, 1].set_xlabel('Epoch')
        axes[0, 1].set_ylabel('Cumulative Best Accuracy')
        axes[0, 1].grid(True, alpha=0.3)
        axes[0, 1].set_ylim([0, 1])

        # 训练时间效率
        if 'time' in self.training_history[0]:
            epoch_times = [item['time'] for item in self.training_history]
            cumulative_time = np.cumsum(epoch_times)

            axes[1, 0].plot(cumulative_time, val_accs, 'purple', linewidth=2)
            axes[1, 0].set_title('准确率 vs 训练时间', fontsize=14)
            axes[1, 0].set_xlabel('Cumulative Training Time (seconds)')
            axes[1, 0].set_ylabel('Validation Accuracy')
            axes[1, 0].grid(True, alpha=0.3)

            # 时间效率比
            time_efficiency = [acc / time if time > 0 else 0 for acc, time in zip(val_accs, cumulative_time)]
            axes[1, 1].plot(epochs, time_efficiency, 'orange', linewidth=2)
            axes[1, 1].set_title('时间效率比 (准确率/时间)', fontsize=14)
            axes[1, 1].set_xlabel('Epoch')
            axes[1, 1].set_ylabel('Accuracy / Time')
            axes[1, 1].grid(True, alpha=0.3)

        plt.tight_layout()

        if save_plots:
            plot_file = self.plots_dir / "training_efficiency.png"
            plt.savefig(plot_file, dpi=300, bbox_inches='tight')
            self.logger.info(f"训练效率分析图已保存: {plot_file}")

        plt.show()

    def create_comprehensive_dashboard(self, save_plots: bool = True) -> None:
        """创建综合仪表板"""
        if not self.training_history:
            return

        # 创建大型综合图表
        fig = plt.figure(figsize=(20, 16))
        fig.suptitle(f'训练综合仪表板 - {self.experiment_name}', fontsize=20, fontweight='bold')

        # 提取数据
        epochs = [item['epoch'] for item in self.training_history]
        train_loss = [item['train_loss'] for item in self.training_history]
        val_loss = [item['val_loss'] for item in self.training_history]
        train_acc = [item['train_acc'] for item in self.training_history]
        val_acc = [item['val_acc'] for item in self.training_history]
        learning_rates = [item.get('lr', 0) for item in self.training_history]

        # 创建网格布局
        gs = fig.add_gridspec(4, 4, hspace=0.3, wspace=0.3)

        # 主要训练曲线 (占据左上角2x2)
        ax_main = fig.add_subplot(gs[0:2, 0:2])
        ax_main_twin = ax_main.twinx()

        line1 = ax_main.plot(epochs, train_loss, 'b-', label='训练损失', linewidth=2)
        line2 = ax_main.plot(epochs, val_loss, 'r-', label='验证损失', linewidth=2)
        line3 = ax_main_twin.plot(epochs, train_acc, 'g--', label='训练准确率', linewidth=2)
        line4 = ax_main_twin.plot(epochs, val_acc, 'orange', linestyle='--', label='验证准确率', linewidth=2)

        ax_main.set_xlabel('Epoch')
        ax_main.set_ylabel('Loss', color='blue')
        ax_main_twin.set_ylabel('Accuracy', color='green')
        ax_main.set_title('训练主曲线', fontsize=14, fontweight='bold')

        # 合并图例
        lines = line1 + line2 + line3 + line4
        labels = [l.get_label() for l in lines]
        ax_main.legend(lines, labels, loc='center right')

        # 学习率曲线
        ax_lr = fig.add_subplot(gs[0, 2])
        ax_lr.semilogy(epochs, learning_rates, 'purple', linewidth=2)
        ax_lr.set_title('学习率', fontsize=12)
        ax_lr.set_xlabel('Epoch')
        ax_lr.grid(True, alpha=0.3)

        # 最佳准确率
        ax_best = fig.add_subplot(gs[0, 3])
        best_val_acc = max(val_acc)
        best_epoch = epochs[np.argmax(val_acc)]
        ax_best.bar(['最佳验证准确率'], [best_val_acc], color='gold')
        ax_best.set_ylim([0, 1])
        ax_best.set_title(f'最佳: {best_val_acc:.4f}\n(Epoch {best_epoch})', fontsize=12)

        # 损失分析
        ax_loss_diff = fig.add_subplot(gs[1, 2])
        loss_diff = [abs(t - v) for t, v in zip(train_loss, val_loss)]
        ax_loss_diff.plot(epochs, loss_diff, 'red', linewidth=2)
        ax_loss_diff.set_title('训练-验证损失差', fontsize=12)
        ax_loss_diff.set_xlabel('Epoch')
        ax_loss_diff.grid(True, alpha=0.3)

        # 过拟合分析
        ax_overfit = fig.add_subplot(gs[1, 3])
        overfitting = [t - v for t, v in zip(train_acc, val_acc)]
        ax_overfit.plot(epochs, overfitting, 'orange', linewidth=2)
        ax_overfit.axhline(y=0, color='black', linestyle='--', alpha=0.5)
        ax_overfit.set_title('过拟合指标', fontsize=12)
        ax_overfit.set_xlabel('Epoch')
        ax_overfit.grid(True, alpha=0.3)

        # 训练时间分析
        if 'time' in self.training_history[0]:
            epoch_times = [item['time'] for item in self.training_history]

            ax_time = fig.add_subplot(gs[2, 0])
            ax_time.plot(epochs, epoch_times, 'brown', linewidth=2)
            ax_time.set_title('每轮训练时间', fontsize=12)
            ax_time.set_xlabel('Epoch')
            ax_time.set_ylabel('Time (s)')
            ax_time.grid(True, alpha=0.3)

            # 累积训练时间
            ax_cumtime = fig.add_subplot(gs[2, 1])
            cumulative_time = np.cumsum(epoch_times)
            ax_cumtime.plot(epochs, cumulative_time, 'navy', linewidth=2)
            ax_cumtime.set_title('累积训练时间', fontsize=12)
            ax_cumtime.set_xlabel('Epoch')
            ax_cumtime.set_ylabel('Cumulative Time (s)')
            ax_cumtime.grid(True, alpha=0.3)

        # 准确率分布
        ax_acc_dist = fig.add_subplot(gs[2, 2])
        ax_acc_dist.hist(val_acc, bins=20, alpha=0.7, color='skyblue', edgecolor='black')
        ax_acc_dist.set_title('验证准确率分布', fontsize=12)
        ax_acc_dist.set_xlabel('Accuracy')
        ax_acc_dist.set_ylabel('Frequency')

        # 训练稳定性
        ax_stability = fig.add_subplot(gs[2, 3])
        train_loss_smooth = pd.Series(train_loss).rolling(window=5).std()
        val_loss_smooth = pd.Series(val_loss).rolling(window=5).std()
        ax_stability.plot(epochs, train_loss_smooth, 'b-', label='训练损失标准差', linewidth=2)
        ax_stability.plot(epochs, val_loss_smooth, 'r-', label='验证损失标准差', linewidth=2)
        ax_stability.set_title('训练稳定性', fontsize=12)
        ax_stability.set_xlabel('Epoch')
        ax_stability.legend()
        ax_stability.grid(True, alpha=0.3)

        # 配置信息文本
        ax_config = fig.add_subplot(gs[3, :])
        ax_config.axis('off')

        config_text = f"""
实验配置:
• 模型: {self.config.get('model_type', 'N/A')}
• 批次大小: {self.config.get('batch_size', 'N/A')}
• 学习率: {self.config.get('learning_rate', 'N/A')}
• 总轮数: {len(self.training_history)}
• 最佳验证准确率: {max(val_acc):.4f} (Epoch {epochs[np.argmax(val_acc)]})
• 最终训练损失: {train_loss[-1]:.4f}
• 最终验证损失: {val_loss[-1]:.4f}
        """

        ax_config.text(0.1, 0.5, config_text, fontsize=12, verticalalignment='center',
                      bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgray", alpha=0.5))

        if save_plots:
            plot_file = self.plots_dir / "comprehensive_dashboard.png"
            plt.savefig(plot_file, dpi=300, bbox_inches='tight')
            self.logger.info(f"综合仪表板已保存: {plot_file}")

        plt.show()

    def save_training_data_csv(self) -> None:
        """保存训练数据为CSV格式"""
        if not self.training_history:
            return

        # 转换为DataFrame
        df = pd.DataFrame(self.training_history)

        # 保存CSV
        csv_file = self.data_dir / "training_data.csv"
        df.to_csv(csv_file, index=False, encoding='utf-8-sig')

        self.logger.info(f"训练数据CSV已保存: {csv_file}")

    def compare_experiments(self, other_experiments: List[str], save_plots: bool = True) -> None:
        """比较多个实验结果"""
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('实验对比分析', fontsize=16, fontweight='bold')

        # 当前实验
        if self.training_history:
            epochs = [item['epoch'] for item in self.training_history]
            val_acc = [item['val_acc'] for item in self.training_history]
            val_loss = [item['val_loss'] for item in self.training_history]

            axes[0, 0].plot(epochs, val_acc, label=self.experiment_name, linewidth=2)
            axes[0, 1].plot(epochs, val_loss, label=self.experiment_name, linewidth=2)

        # 其他实验
        for exp_name in other_experiments:
            exp_dir = self.save_dir / exp_name
            history_file = exp_dir / "data" / "training_history.json"

            if history_file.exists():
                with open(history_file, 'r', encoding='utf-8') as f:
                    history = json.load(f)

                epochs = [item['epoch'] for item in history]
                val_acc = [item['val_acc'] for item in history]
                val_loss = [item['val_loss'] for item in history]

                axes[0, 0].plot(epochs, val_acc, label=exp_name, linewidth=2)
                axes[0, 1].plot(epochs, val_loss, label=exp_name, linewidth=2)

        axes[0, 0].set_title('验证准确率对比')
        axes[0, 0].set_xlabel('Epoch')
        axes[0, 0].set_ylabel('Validation Accuracy')
        axes[0, 0].legend()
        axes[0, 0].grid(True, alpha=0.3)

        axes[0, 1].set_title('验证损失对比')
        axes[0, 1].set_xlabel('Epoch')
        axes[0, 1].set_ylabel('Validation Loss')
        axes[0, 1].legend()
        axes[0, 1].grid(True, alpha=0.3)

        plt.tight_layout()

        if save_plots:
            plot_file = self.plots_dir / "experiment_comparison.png"
            plt.savefig(plot_file, dpi=300, bbox_inches='tight')
            self.logger.info(f"实验对比图已保存: {plot_file}")

        plt.show()
