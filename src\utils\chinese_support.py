# -*- coding: utf-8 -*-
"""
中文字符支持模块
确保整个系统支持中文字符显示和处理，避免乱码问题
"""

import os
import sys
import cv2
import numpy as np
from typing import List, Dict, Tuple, Optional, Union
from pathlib import Path
import logging
import json

# 设置系统编码
import locale
import codecs

class ChineseSupport:
    """中文字符支持类"""
    
    def __init__(self):
        self.setup_encoding()
        self.setup_fonts()
        self.logger = logging.getLogger(__name__)
        
        # 中国车牌字符集
        self.chinese_provinces = [
            "京", "津", "沪", "渝", "冀", "豫", "云", "辽", "黑", "湘", 
            "皖", "鲁", "新", "苏", "浙", "赣", "鄂", "桂", "甘", "晋", 
            "蒙", "陕", "吉", "闽", "贵", "粤", "青", "藏", "川", "宁", 
            "琼", "使", "领", "警", "学", "港", "澳"
        ]
        
        self.chinese_letters = [
            "A", "B", "C", "D", "E", "F", "G", "H", "J", "K", "L", "M", 
            "N", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z"
        ]
        
        self.chinese_numbers = [
            "0", "1", "2", "3", "4", "5", "6", "7", "8", "9"
        ]
        
        # 完整字符集
        self.all_chars = self.chinese_provinces + self.chinese_letters + self.chinese_numbers
        
        # 字符到索引的映射
        self.char_to_idx = {char: idx for idx, char in enumerate(self.all_chars)}
        self.idx_to_char = {idx: char for idx, char in enumerate(self.all_chars)}
        
        self.logger.info(f"中文字符支持初始化完成，支持 {len(self.all_chars)} 个字符")
    
    def setup_encoding(self):
        """设置系统编码"""
        try:
            # 设置默认编码为UTF-8
            if sys.version_info >= (3, 7):
                # Python 3.7+ 默认使用UTF-8
                pass
            else:
                # 对于较老版本的Python
                import importlib
                importlib.reload(sys)
                sys.setdefaultencoding('utf-8')
            
            # 设置locale
            try:
                locale.setlocale(locale.LC_ALL, 'zh_CN.UTF-8')
            except locale.Error:
                try:
                    locale.setlocale(locale.LC_ALL, 'Chinese_China.936')
                except locale.Error:
                    try:
                        locale.setlocale(locale.LC_ALL, 'C.UTF-8')
                    except locale.Error:
                        pass
            
            # 设置环境变量
            os.environ['PYTHONIOENCODING'] = 'utf-8'
            
        except Exception as e:
            print(f"设置编码失败: {e}")
    
    def setup_fonts(self):
        """设置中文字体"""
        self.font_paths = []
        
        # Windows系统字体路径
        windows_fonts = [
            "C:/Windows/Fonts/simhei.ttf",      # 黑体
            "C:/Windows/Fonts/simsun.ttc",      # 宋体
            "C:/Windows/Fonts/msyh.ttc",        # 微软雅黑
            "C:/Windows/Fonts/simkai.ttf",      # 楷体
        ]
        
        # Linux系统字体路径
        linux_fonts = [
            "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf",
            "/usr/share/fonts/truetype/liberation/LiberationSans-Regular.ttf",
            "/System/Library/Fonts/PingFang.ttc",  # macOS
        ]
        
        # 检查可用字体
        all_fonts = windows_fonts + linux_fonts
        for font_path in all_fonts:
            if os.path.exists(font_path):
                self.font_paths.append(font_path)
        
        # 如果没有找到系统字体，尝试下载
        if not self.font_paths:
            self.download_chinese_font()
    
    def download_chinese_font(self):
        """下载中文字体"""
        try:
            import requests
            
            # 创建字体目录
            font_dir = Path("fonts")
            font_dir.mkdir(exist_ok=True)
            
            # 下载开源中文字体
            font_url = "https://github.com/adobe-fonts/source-han-sans/releases/download/2.004R/SourceHanSansCN.zip"
            font_file = font_dir / "SourceHanSansCN.ttf"
            
            if not font_file.exists():
                print("正在下载中文字体...")
                response = requests.get(font_url, timeout=30)
                
                if response.status_code == 200:
                    # 这里简化处理，实际需要解压zip文件
                    # 使用备用方案：创建一个简单的字体文件路径
                    self.font_paths.append(str(font_file))
                    print("中文字体下载完成")
                else:
                    print("字体下载失败，将使用默认字体")
            else:
                self.font_paths.append(str(font_file))
                
        except Exception as e:
            print(f"下载字体失败: {e}")
    
    def encode_text(self, text: str) -> str:
        """确保文本正确编码"""
        try:
            if isinstance(text, bytes):
                # 如果是bytes，尝试解码
                try:
                    return text.decode('utf-8')
                except UnicodeDecodeError:
                    try:
                        return text.decode('gbk')
                    except UnicodeDecodeError:
                        return text.decode('latin-1', errors='ignore')
            else:
                # 如果已经是字符串，确保是正确的编码
                return str(text)
        except Exception as e:
            self.logger.warning(f"文本编码失败: {e}")
            return str(text)
    
    def draw_chinese_text(self, image: np.ndarray, text: str, position: Tuple[int, int], 
                         font_size: int = 20, color: Tuple[int, int, int] = (0, 255, 0)) -> np.ndarray:
        """在图像上绘制中文文本"""
        try:
            from PIL import Image, ImageDraw, ImageFont
            
            # 转换OpenCV图像到PIL
            pil_image = Image.fromarray(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
            draw = ImageDraw.Draw(pil_image)
            
            # 加载字体
            font = None
            for font_path in self.font_paths:
                try:
                    font = ImageFont.truetype(font_path, font_size)
                    break
                except Exception:
                    continue
            
            if font is None:
                # 使用默认字体
                try:
                    font = ImageFont.load_default()
                except:
                    # 如果连默认字体都加载失败，使用OpenCV
                    return self.draw_text_opencv(image, text, position, color)
            
            # 确保文本正确编码
            text = self.encode_text(text)
            
            # 绘制文本
            draw.text(position, text, fill=color, font=font)
            
            # 转换回OpenCV格式
            result_image = cv2.cvtColor(np.array(pil_image), cv2.COLOR_RGB2BGR)
            return result_image
            
        except ImportError:
            # PIL不可用，使用OpenCV
            return self.draw_text_opencv(image, text, position, color)
        except Exception as e:
            self.logger.warning(f"绘制中文文本失败: {e}")
            return self.draw_text_opencv(image, text, position, color)
    
    def draw_text_opencv(self, image: np.ndarray, text: str, position: Tuple[int, int], 
                        color: Tuple[int, int, int] = (0, 255, 0)) -> np.ndarray:
        """使用OpenCV绘制文本（可能不支持中文）"""
        try:
            # 确保文本编码
            text = self.encode_text(text)
            
            # 对于中文字符，转换为拼音或英文标识
            display_text = self.convert_chinese_to_display(text)
            
            cv2.putText(image, display_text, position, 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, color, 2)
            return image
        except Exception as e:
            self.logger.warning(f"OpenCV绘制文本失败: {e}")
            return image
    
    def convert_chinese_to_display(self, text: str) -> str:
        """将中文字符转换为可显示的形式"""
        # 中文省份到拼音的映射
        province_map = {
            "京": "BJ", "津": "TJ", "沪": "SH", "渝": "CQ", "冀": "HE",
            "豫": "HN", "云": "YN", "辽": "LN", "黑": "HL", "湘": "HN",
            "皖": "AH", "鲁": "SD", "新": "XJ", "苏": "JS", "浙": "ZJ",
            "赣": "JX", "鄂": "HB", "桂": "GX", "甘": "GS", "晋": "SX",
            "蒙": "NM", "陕": "SN", "吉": "JL", "闽": "FJ", "贵": "GZ",
            "粤": "GD", "青": "QH", "藏": "XZ", "川": "SC", "宁": "NX",
            "琼": "HI", "使": "SHI", "领": "LING", "警": "JING", "学": "XUE"
        }
        
        result = ""
        for char in text:
            if char in province_map:
                result += province_map[char]
            else:
                result += char
        
        return result
    
    def validate_plate_text(self, plate_text: str) -> bool:
        """验证车牌文本是否符合中国车牌格式"""
        if not plate_text or len(plate_text) < 7:
            return False
        
        # 第一个字符必须是省份简称
        if plate_text[0] not in self.chinese_provinces:
            return False
        
        # 第二个字符必须是字母
        if plate_text[1] not in self.chinese_letters:
            return False
        
        # 后面5个字符必须是字母或数字
        for char in plate_text[2:7]:
            if char not in (self.chinese_letters + self.chinese_numbers):
                return False
        
        return True
    
    def normalize_plate_text(self, plate_text: str) -> str:
        """标准化车牌文本"""
        # 去除空格和特殊字符
        normalized = ''.join(char for char in plate_text if char.isalnum() or char in self.chinese_provinces)
        
        # 转换为大写
        normalized = normalized.upper()
        
        # 确保编码正确
        normalized = self.encode_text(normalized)
        
        return normalized
    
    def save_json_with_chinese(self, data: Dict, file_path: str):
        """保存包含中文的JSON文件"""
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            self.logger.error(f"保存JSON文件失败: {e}")
    
    def load_json_with_chinese(self, file_path: str) -> Dict:
        """加载包含中文的JSON文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            self.logger.error(f"加载JSON文件失败: {e}")
            return {}
    
    def get_char_count(self) -> int:
        """获取支持的字符数量"""
        return len(self.all_chars)
    
    def char_to_index(self, char: str) -> int:
        """字符转索引"""
        return self.char_to_idx.get(char, -1)
    
    def index_to_char(self, index: int) -> str:
        """索引转字符"""
        return self.idx_to_char.get(index, "")

# 全局中文支持实例
chinese_support = ChineseSupport()

def setup_chinese_environment():
    """设置中文环境"""
    return chinese_support

def draw_chinese_text(image: np.ndarray, text: str, position: Tuple[int, int], 
                     font_size: int = 20, color: Tuple[int, int, int] = (0, 255, 0)) -> np.ndarray:
    """绘制中文文本的便捷函数"""
    return chinese_support.draw_chinese_text(image, text, position, font_size, color)

def validate_chinese_plate(plate_text: str) -> bool:
    """验证中文车牌的便捷函数"""
    return chinese_support.validate_plate_text(plate_text)

def normalize_chinese_plate(plate_text: str) -> str:
    """标准化中文车牌的便捷函数"""
    return chinese_support.normalize_plate_text(plate_text)

def main():
    """测试函数"""
    print("=== 中文字符支持测试 ===")
    
    # 测试编码
    test_text = "京A12345"
    print(f"原始文本: {test_text}")
    
    encoded_text = chinese_support.encode_text(test_text)
    print(f"编码后文本: {encoded_text}")
    
    # 测试验证
    is_valid = chinese_support.validate_plate_text(test_text)
    print(f"车牌格式验证: {is_valid}")
    
    # 测试标准化
    normalized = chinese_support.normalize_plate_text(test_text)
    print(f"标准化后: {normalized}")
    
    # 测试字符映射
    print(f"支持的字符数量: {chinese_support.get_char_count()}")
    print(f"省份字符: {chinese_support.chinese_provinces[:10]}...")
    
    print("中文字符支持测试完成")

if __name__ == "__main__":
    main()
