# 车牌识别系统 Web应用

基于深度学习的车牌识别系统的Web用户界面，提供美观、易用的车牌识别演示、数据管理和模型训练功能。

## 功能特性

### 🎯 核心功能
- **车牌识别演示**: 上传图片进行实时车牌检测和识别
- **数据管理**: 数据采集、数据集管理、统计分析
- **模型训练**: 深度学习模型训练和管理
- **性能监控**: 实时统计和性能指标展示

### 🎨 界面特性
- **响应式设计**: 支持桌面和移动设备
- **现代化UI**: 基于Bootstrap 5的美观界面
- **实时更新**: AJAX异步更新，无需刷新页面
- **中文支持**: 完整的中文界面和字符支持

### 🚀 技术特性
- **高性能**: 异步处理，支持并发请求
- **易扩展**: 模块化设计，易于添加新功能
- **安全可靠**: 文件上传验证，错误处理机制
- **实时监控**: 训练进度实时显示

## 快速开始

### 1. 环境要求
- Python 3.8+
- PyTorch 2.0+
- OpenCV 4.5+
- Flask 2.3+

### 2. 安装依赖
```bash
# 进入Web应用目录
cd src/web_app

# 安装依赖包
pip install -r requirements.txt
```

### 3. 启动应用
```bash
# 使用默认配置启动
python run_web_app.py

# 自定义配置启动
python run_web_app.py --host 0.0.0.0 --port 8080 --debug

# 查看帮助
python run_web_app.py --help
```

### 4. 访问应用
打开浏览器访问: http://127.0.0.1:5000

## 页面说明

### 首页 (/)
- 系统概览和统计信息
- 快速导航到各功能模块
- 系统特性和技术架构展示

### 识别演示 (/demo)
- 图片上传和预览
- 实时车牌识别
- 结果可视化展示
- 示例图片快速测试

### 数据管理 (/data_management)
- 数据采集配置和执行
- 数据集统计和分析
- 数据集管理和操作

### 模型训练 (/model_training)
- 训练参数配置
- 训练进度实时监控
- 训练曲线可视化
- 模型管理和导出

## API接口

### 文件上传
```
POST /upload
Content-Type: multipart/form-data

参数:
- file: 图片文件 (支持 JPG, PNG, BMP, WEBP)

返回:
{
    "success": true,
    "detections": 2,
    "results": [...],
    "processing_time": 0.85,
    "result_image": "result_xxx.jpg"
}
```

### 统计信息
```
GET /api/stats

返回:
{
    "total_detections": 150,
    "successful_detections": 142,
    "success_rate": 94.67,
    "avg_processing_time": 0.73,
    "runtime_hours": 2.5
}
```

### 数据采集
```
POST /api/data/collect
Content-Type: application/json

参数:
{
    "keywords": ["车牌", "汽车牌照"],
    "max_images": 100
}

返回:
{
    "success": true,
    "message": "开始采集数据...",
    "task_id": "task_1642567890"
}
```

### 模型训练
```
POST /api/model/train
Content-Type: application/json

参数:
{
    "model_type": "detector",
    "epochs": 50
}

返回:
{
    "success": true,
    "message": "开始训练模型...",
    "task_id": "train_1642567890"
}
```

## 文件结构

```
src/web_app/
├── app.py                 # Flask应用主文件
├── run_web_app.py        # 启动脚本
├── requirements.txt      # 依赖包列表
├── README.md            # 说明文档
├── templates/           # HTML模板
│   ├── base.html        # 基础模板
│   ├── index.html       # 首页
│   ├── demo.html        # 识别演示
│   ├── data_management.html  # 数据管理
│   └── model_training.html   # 模型训练
└── static/             # 静态资源
    ├── css/
    │   └── style.css   # 自定义样式
    ├── js/
    │   └── main.js     # 主要JavaScript
    └── images/         # 图片资源
        └── placeholder.jpg
```

## 配置说明

### 应用配置
- `SECRET_KEY`: Flask密钥
- `MAX_CONTENT_LENGTH`: 最大文件上传大小 (16MB)
- `UPLOAD_FOLDER`: 上传文件目录
- `RESULTS_FOLDER`: 结果文件目录

### 模型配置
通过 `config/config.yaml` 配置深度学习模型参数:
- 检测器配置
- 识别器配置
- 训练参数
- 数据路径

## 开发说明

### 添加新页面
1. 在 `templates/` 目录创建HTML模板
2. 在 `app.py` 中添加路由处理函数
3. 在 `static/` 目录添加相应的CSS/JS文件

### 添加新API
1. 在 `LicensePlateWebApp` 类中添加路由方法
2. 实现业务逻辑
3. 返回JSON格式响应

### 自定义样式
在 `static/css/style.css` 中添加自定义CSS样式

### 扩展功能
- 添加用户认证
- 实现文件管理
- 添加更多图表
- 集成更多深度学习模型

## 故障排除

### 常见问题

1. **端口被占用**
   ```bash
   # 更换端口
   python run_web_app.py --port 8080
   ```

2. **模型加载失败**
   - 检查模型文件路径
   - 确认PyTorch版本兼容性
   - 查看日志文件

3. **文件上传失败**
   - 检查文件大小限制
   - 确认文件格式支持
   - 检查磁盘空间

4. **页面显示异常**
   - 清除浏览器缓存
   - 检查网络连接
   - 查看浏览器控制台错误

### 日志查看
```bash
# 查看应用日志
tail -f logs/app.log

# 查看错误日志
tail -f logs/error.log
```

## 许可证

本项目基于MIT许可证开源。

## 贡献

欢迎提交Issue和Pull Request来改进项目。

## 联系方式

如有问题或建议，请通过以下方式联系:
- 项目Issues: 在GitHub上提交Issue
- 邮箱: 项目维护者邮箱
