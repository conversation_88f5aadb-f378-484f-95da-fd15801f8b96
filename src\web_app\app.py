#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
车牌识别系统Web应用 (方案1 - 已停用)
提供美观的用户界面用于车牌识别演示、数据管理、模型训练等功能

注意: 此方案已被新的 React + FastAPI 架构 (方案2) 替代
新的方案位于: src/web_app_v2/
启动新方案: python src/web_app_v2/start_dev.py
"""

# 此文件已停用，请使用新的 React + FastAPI 架构
# 位置: src/web_app_v2/
# 启动命令: python src/web_app_v2/start_dev.py

import sys
print("=" * 80)
print("⚠️  此 Flask 应用 (方案1) 已停用")
print("=" * 80)
print("🚀 请使用新的 React + FastAPI 架构 (方案2)")
print("📁 位置: src/web_app_v2/")
print("🔧 启动命令: python src/web_app_v2/start_dev.py")
print("=" * 80)
print("💡 新架构特性:")
print("   - React 18 + TypeScript + Tailwind CSS 前端")
print("   - FastAPI 后端 API 服务")
print("   - 现代化 UI/UX 设计")
print("   - 前后端分离架构")
print("   - 更好的性能和可维护性")
print("=" * 80)
sys.exit(1)

# 以下代码已注释，保留作为参考
"""

import os
import sys
import json
import time
import base64
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any, Optional

import cv2
import numpy as np
from flask import Flask, render_template, request, jsonify, send_file, redirect, url_for
from werkzeug.utils import secure_filename
from werkzeug.datastructures import FileStorage

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.utils.chinese_support import setup_chinese_environment
from src.utils.config_loader import load_config
from src.utils.logger import setup_logger
from src.detection.improved_yolo_detector import ImprovedYOLODetector
from src.system.license_plate_recognizer import LicensePlateRecognizer

# 设置中文环境
setup_chinese_environment()

class LicensePlateWebApp:
    """车牌识别Web应用类"""
    
    def __init__(self, config_path: str = "config/config.yaml"):
        """初始化Web应用"""
        self.app = Flask(__name__, 
                        template_folder='templates',
                        static_folder='static')
        
        # 配置
        self.app.config['SECRET_KEY'] = 'license_plate_recognition_2025'
        self.app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size
        self.app.config['UPLOAD_FOLDER'] = 'uploads'
        self.app.config['RESULTS_FOLDER'] = 'results'
        
        # 创建必要目录
        os.makedirs(self.app.config['UPLOAD_FOLDER'], exist_ok=True)
        os.makedirs(self.app.config['RESULTS_FOLDER'], exist_ok=True)
        
        # 加载配置
        self.config = load_config(config_path)
        
        # 设置日志
        self.logger = setup_logger(self.config)
        
        # 初始化检测器
        self.detector = None
        self.recognizer = None
        self._init_models()
        
        # 统计信息
        self.stats = {
            'total_detections': 0,
            'successful_detections': 0,
            'total_processing_time': 0.0,
            'start_time': datetime.now()
        }
        
        # 注册路由
        self._register_routes()
    
    def _init_models(self):
        """初始化模型"""
        try:
            self.logger.info("初始化车牌检测模型...")
            self.detector = ImprovedYOLODetector()
            self.detector.load_model()

            self.logger.info("初始化车牌识别模型...")
            # 暂时跳过识别器初始化，避免配置问题
            # self.recognizer = LicensePlateRecognizer(self.config)
            self.recognizer = None

            self.logger.info("模型初始化完成")
        except Exception as e:
            self.logger.error(f"模型初始化失败: {str(e)}")
            # 设置为None，在实际使用时会显示错误信息
            self.detector = None
            self.recognizer = None
            self.logger.warning("模型未加载，Web界面将以演示模式运行")
    
    def _register_routes(self):
        """注册路由"""
        
        @self.app.route('/')
        def index():
            """主页"""
            return render_template('index.html', stats=self.stats)
        
        @self.app.route('/upload', methods=['POST'])
        def upload_file():
            """上传文件进行车牌识别"""
            try:
                if 'file' not in request.files:
                    return jsonify({'error': '没有选择文件'}), 400
                
                file = request.files['file']
                if file.filename == '':
                    return jsonify({'error': '没有选择文件'}), 400
                
                if file and self._allowed_file(file.filename):
                    # 保存上传的文件
                    filename = secure_filename(file.filename)
                    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                    filename = f"{timestamp}_{filename}"
                    filepath = os.path.join(self.app.config['UPLOAD_FOLDER'], filename)
                    file.save(filepath)
                    
                    # 进行车牌识别
                    result = self._process_image(filepath)
                    
                    # 更新统计信息
                    self.stats['total_detections'] += 1
                    if result['success']:
                        self.stats['successful_detections'] += 1
                    self.stats['total_processing_time'] += result.get('processing_time', 0)
                    
                    return jsonify(result)
                else:
                    return jsonify({'error': '不支持的文件格式'}), 400
                    
            except Exception as e:
                self.logger.error(f"文件上传处理失败: {str(e)}")
                return jsonify({'error': f'处理失败: {str(e)}'}), 500
        
        @self.app.route('/api/stats')
        def get_stats():
            """获取统计信息"""
            runtime = datetime.now() - self.stats['start_time']
            current_stats = self.stats.copy()
            current_stats['runtime_hours'] = runtime.total_seconds() / 3600
            current_stats['success_rate'] = (
                self.stats['successful_detections'] / max(self.stats['total_detections'], 1) * 100
            )
            current_stats['avg_processing_time'] = (
                self.stats['total_processing_time'] / max(self.stats['total_detections'], 1)
            )
            return jsonify(current_stats)
        
        @self.app.route('/demo')
        def demo():
            """演示页面"""
            return render_template('demo.html')
        
        @self.app.route('/data_management')
        def data_management():
            """数据管理页面"""
            return render_template('data_management.html')
        
        @self.app.route('/model_training')
        def model_training():
            """模型训练页面"""
            return render_template('model_training.html')
        
        @self.app.route('/api/data/collect', methods=['POST'])
        def collect_data():
            """数据采集API"""
            try:
                data = request.get_json()
                keywords = data.get('keywords', ['车牌'])
                max_images = data.get('max_images', 100)
                
                # 这里可以调用数据采集模块
                result = {
                    'success': True,
                    'message': f'开始采集数据，关键词: {keywords}，目标数量: {max_images}',
                    'task_id': f'task_{int(time.time())}'
                }
                
                return jsonify(result)
            except Exception as e:
                return jsonify({'error': str(e)}), 500
        
        @self.app.route('/api/model/train', methods=['POST'])
        def train_model():
            """模型训练API"""
            try:
                data = request.get_json()
                model_type = data.get('model_type', 'character_recognizer')
                epochs = data.get('epochs', 10)

                # 这里可以调用模型训练模块
                result = {
                    'success': True,
                    'message': f'开始训练{model_type}模型，训练轮数: {epochs}',
                    'task_id': f'train_{int(time.time())}'
                }

                return jsonify(result)
            except Exception as e:
                return jsonify({'error': str(e)}), 500

        @self.app.route('/results/<filename>')
        def serve_result(filename):
            """提供结果图片"""
            try:
                return send_file(os.path.join(self.app.config['RESULTS_FOLDER'], filename))
            except Exception as e:
                self.logger.error(f"提供结果文件失败: {str(e)}")
                return "文件未找到", 404


    
    def _allowed_file(self, filename: str) -> bool:
        """检查文件格式是否允许"""
        allowed_extensions = {'png', 'jpg', 'jpeg', 'gif', 'bmp', 'webp'}
        return '.' in filename and \
               filename.rsplit('.', 1)[1].lower() in allowed_extensions
    
    def _process_image(self, image_path: str) -> Dict[str, Any]:
        """处理图像进行车牌识别"""
        start_time = time.time()
        
        try:
            # 读取图像
            image = cv2.imread(image_path)
            if image is None:
                return {
                    'success': False,
                    'error': '无法读取图像文件',
                    'processing_time': time.time() - start_time
                }
            
            # 车牌检测
            detections = []
            if self.detector:
                detections = self.detector.detect(image)
            
            # 处理检测结果
            results = []
            for i, detection in enumerate(detections):
                bbox = detection.get('bbox', [])
                confidence = detection.get('confidence', 0.0)
                
                # 提取车牌区域
                if len(bbox) == 4:
                    x1, y1, x2, y2 = map(int, bbox)
                    plate_img = image[y1:y2, x1:x2]
                    
                    # 车牌识别（如果有识别器）
                    plate_text = "未识别"
                    if self.recognizer and plate_img.size > 0:
                        try:
                            plate_text = self.recognizer.recognize_plate(plate_img)
                        except:
                            plate_text = "识别失败"
                    
                    results.append({
                        'bbox': bbox,
                        'confidence': float(confidence),
                        'plate_text': plate_text,
                        'plate_id': f'plate_{i+1}'
                    })
            
            # 生成结果图像
            result_image_path = self._draw_results(image, results, image_path)
            
            processing_time = time.time() - start_time
            
            return {
                'success': True,
                'detections': len(results),
                'results': results,
                'result_image': result_image_path,
                'processing_time': processing_time,
                'image_size': f"{image.shape[1]}x{image.shape[0]}"
            }
            
        except Exception as e:
            self.logger.error(f"图像处理失败: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'processing_time': time.time() - start_time
            }
    
    def _draw_results(self, image: np.ndarray, results: List[Dict], 
                     original_path: str) -> str:
        """绘制检测结果"""
        try:
            result_image = image.copy()
            
            for result in results:
                bbox = result['bbox']
                confidence = result['confidence']
                plate_text = result['plate_text']
                
                if len(bbox) == 4:
                    x1, y1, x2, y2 = map(int, bbox)
                    
                    # 绘制边界框
                    cv2.rectangle(result_image, (x1, y1), (x2, y2), (0, 255, 0), 2)
                    
                    # 绘制文本
                    label = f"{plate_text} ({confidence:.2f})"
                    cv2.putText(result_image, label, (x1, y1-10), 
                              cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
            
            # 保存结果图像
            filename = os.path.basename(original_path)
            result_filename = f"result_{filename}"
            result_path = os.path.join(self.app.config['RESULTS_FOLDER'], result_filename)
            cv2.imwrite(result_path, result_image)
            
            return result_filename
            
        except Exception as e:
            self.logger.error(f"绘制结果失败: {str(e)}")
            return ""
    
    def run(self, host: str = '127.0.0.1', port: int = 5000, debug: bool = True):
        """运行Web应用"""
        self.logger.info(f"启动车牌识别Web应用: http://{host}:{port}")
        self.app.run(host=host, port=port, debug=debug)


def create_app(config_path: str = "config/config.yaml") -> Flask:
    """创建Flask应用实例"""
    web_app = LicensePlateWebApp(config_path)
    return web_app.app


if __name__ == '__main__':
    # 创建并运行应用
    web_app = LicensePlateWebApp()
    web_app.run(debug=True)
