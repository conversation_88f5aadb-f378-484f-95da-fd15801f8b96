#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
车牌识别系统Web应用启动脚本
"""

import os
import sys
import argparse
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from app import LicensePlateWebApp


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='车牌识别系统Web应用')
    parser.add_argument('--host', default='127.0.0.1', help='服务器地址 (默认: 127.0.0.1)')
    parser.add_argument('--port', type=int, default=5000, help='服务器端口 (默认: 5000)')
    parser.add_argument('--debug', action='store_true', help='启用调试模式')
    parser.add_argument('--config', default='config/config.yaml', help='配置文件路径')
    
    args = parser.parse_args()
    
    # 设置工作目录为项目根目录
    os.chdir(project_root)
    
    # 创建必要目录
    os.makedirs('uploads', exist_ok=True)
    os.makedirs('results', exist_ok=True)
    os.makedirs('logs', exist_ok=True)
    
    print("=" * 60)
    print("🚗 基于深度学习的车牌识别系统 Web应用")
    print("=" * 60)
    print(f"📍 服务器地址: http://{args.host}:{args.port}")
    print(f"🔧 调试模式: {'开启' if args.debug else '关闭'}")
    print(f"📁 工作目录: {project_root}")
    print(f"📄 配置文件: {args.config}")
    print("=" * 60)
    
    try:
        # 确保配置文件路径正确
        config_path = os.path.join(project_root, args.config)
        if not os.path.exists(config_path):
            config_path = args.config  # 尝试使用原始路径

        # 创建并运行Web应用
        web_app = LicensePlateWebApp(config_path)
        web_app.run(host=args.host, port=args.port, debug=args.debug)
        
    except KeyboardInterrupt:
        print("\n👋 应用已停止")
    except Exception as e:
        print(f"❌ 应用启动失败: {str(e)}")
        sys.exit(1)


if __name__ == '__main__':
    main()
