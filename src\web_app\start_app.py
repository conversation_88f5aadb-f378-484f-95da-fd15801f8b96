#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的Web应用启动脚本
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
current_dir = Path(__file__).parent.resolve()  # src/web_app
project_root = current_dir.parent.parent.resolve()  # 项目根目录
sys.path.insert(0, str(project_root))

# 设置工作目录为项目根目录
print(f"当前脚本目录: {current_dir}")
print(f"项目根目录: {project_root}")
os.chdir(project_root)
print(f"切换后工作目录: {os.getcwd()}")

from app import LicensePlateWebApp

def main():
    """主函数"""
    # 创建必要目录
    os.makedirs('uploads', exist_ok=True)
    os.makedirs('results', exist_ok=True)
    os.makedirs('logs', exist_ok=True)
    
    print("=" * 60)
    print("🚗 基于深度学习的车牌识别系统 Web应用")
    print("=" * 60)
    print(f"📍 服务器地址: http://127.0.0.1:5000")
    print(f"📁 工作目录: {os.getcwd()}")
    print(f"📁 项目根目录: {project_root}")
    print("=" * 60)
    
    try:
        # 创建并运行Web应用
        config_path = "config/config.yaml"
        web_app = LicensePlateWebApp(config_path)
        web_app.run(host='127.0.0.1', port=5000, debug=False)
        
    except KeyboardInterrupt:
        print("\n👋 应用已停止")
    except Exception as e:
        print(f"❌ 应用启动失败: {str(e)}")
        sys.exit(1)


if __name__ == '__main__':
    main()
