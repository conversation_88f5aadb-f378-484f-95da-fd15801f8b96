/* 车牌识别系统 - 现代化样式 */

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

/* 全局样式变量 */
:root {
    --primary-color: #667eea;
    --primary-dark: #5a67d8;
    --secondary-color: #764ba2;
    --success-color: #48bb78;
    --warning-color: #ed8936;
    --danger-color: #f56565;
    --dark-color: #2d3748;
    --light-color: #f7fafc;
    --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --gradient-success: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    --gradient-warning: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
    --border-radius: 12px;
    --box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    --box-shadow-hover: 0 20px 40px rgba(0, 0, 0, 0.15);
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

* {
    box-sizing: border-box;
}

body {
    font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    color: var(--dark-color);
    line-height: 1.6;
    min-height: 100vh;
    margin: 0;
    padding: 0;
}

/* 导航栏样式 */
.navbar {
    background: var(--gradient-primary) !important;
    backdrop-filter: blur(10px);
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
    padding: 1rem 0;
    border: none;
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.8rem;
    color: white !important;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    transition: var(--transition);
}

.navbar-brand:hover {
    transform: scale(1.05);
}

.navbar-nav .nav-link {
    color: rgba(255, 255, 255, 0.9) !important;
    font-weight: 500;
    margin: 0 0.5rem;
    padding: 0.5rem 1rem !important;
    border-radius: 8px;
    transition: var(--transition);
    position: relative;
}

.navbar-nav .nav-link:hover {
    background-color: rgba(255, 255, 255, 0.2);
    color: white !important;
    transform: translateY(-2px);
}

.navbar-nav .nav-link.active {
    background-color: rgba(255, 255, 255, 0.3);
    color: white !important;
}

.navbar-toggler {
    border: none;
    padding: 0.25rem 0.5rem;
}

.navbar-toggler:focus {
    box-shadow: none;
}

/* 主要内容区域 */
.container-fluid {
    padding: 2rem;
}

.main-content {
    background: rgba(255, 255, 255, 0.9);
    border-radius: var(--border-radius);
    backdrop-filter: blur(10px);
    margin-top: 2rem;
}

/* 卡片样式 */
.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    background: white;
    overflow: hidden;
    margin-bottom: 2rem;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: var(--box-shadow-hover);
}

.card-header {
    background: var(--gradient-primary);
    color: white;
    border: none;
    padding: 1.5rem;
    font-weight: 600;
    font-size: 1.2rem;
    position: relative;
}

.card-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(255,255,255,0.1) 0%, transparent 100%);
}

.card-body {
    padding: 2rem;
}

.card-title {
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 1rem;
}

.card-text {
    color: #6c757d;
    line-height: 1.6;
}

/* 按钮样式 */
.btn {
    border-radius: var(--border-radius);
    font-weight: 600;
    padding: 0.75rem 1.5rem;
    transition: var(--transition);
    border: none;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 0.9rem;
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: var(--gradient-primary);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6);
    color: white;
}

.btn-success {
    background: var(--gradient-success);
    box-shadow: 0 4px 15px rgba(79, 172, 254, 0.4);
    color: white;
}

.btn-success:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(79, 172, 254, 0.6);
    color: white;
}

.btn-warning {
    background: var(--gradient-warning);
    color: var(--dark-color);
    box-shadow: 0 4px 15px rgba(237, 137, 54, 0.4);
}

.btn-warning:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(237, 137, 54, 0.6);
}

.btn-danger {
    background: var(--gradient-secondary);
    box-shadow: 0 4px 15px rgba(245, 101, 101, 0.4);
    color: white;
}

.btn-danger:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(245, 101, 101, 0.6);
    color: white;
}

.btn-lg {
    padding: 1rem 2rem;
    font-size: 1.1rem;
}

/* 上传区域样式 */
.upload-area {
    border: 3px dashed rgba(102, 126, 234, 0.3);
    border-radius: var(--border-radius);
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
    transition: var(--transition);
    cursor: pointer;
    min-height: 300px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 3rem;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.upload-area::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at center, rgba(102, 126, 234, 0.1) 0%, transparent 70%);
    opacity: 0;
    transition: var(--transition);
}

.upload-area:hover {
    border-color: var(--success-color);
    background: linear-gradient(135deg, rgba(72, 187, 120, 0.1) 0%, rgba(79, 172, 254, 0.1) 100%);
    transform: scale(1.02);
}

.upload-area:hover::before {
    opacity: 1;
}

.upload-area.dragover {
    border-color: var(--success-color);
    background: linear-gradient(135deg, rgba(72, 187, 120, 0.15) 0%, rgba(79, 172, 254, 0.15) 100%);
    transform: scale(1.05);
    box-shadow: 0 10px 30px rgba(72, 187, 120, 0.3);
}

.upload-icon {
    font-size: 4rem;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 1.5rem;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

.upload-text {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
}

.upload-hint {
    color: #6c757d;
    font-size: 0.9rem;
}

.upload-icon {
    font-size: 4rem;
    color: #6c757d;
    margin-bottom: 1.5rem;
    transition: var(--transition);
}

.upload-text {
    font-size: 1.3rem;
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 0.75rem;
}

.upload-hint {
    color: #6c757d;
    font-size: 0.95rem;
    margin-bottom: 1.5rem;
}

.upload-area:hover {
    border-color: var(--primary-color);
    background: linear-gradient(135deg, #f8f9ff 0%, #e7f3ff 100%);
    transform: translateY(-2px);
}

.upload-area:hover .upload-icon {
    color: var(--primary-color);
    transform: scale(1.1);
}

.upload-area.dragover {
    border-color: var(--success-color);
    background: linear-gradient(135deg, #f0fff4 0%, #dcfce7 100%);
    transform: scale(1.02);
}

.upload-area.dragover .upload-icon {
    color: var(--success-color);
    animation: bounce 0.6s ease-in-out;
}

@keyframes bounce {
    0%, 20%, 60%, 100% { transform: translateY(0) scale(1.1); }
    40% { transform: translateY(-10px) scale(1.1); }
    80% { transform: translateY(-5px) scale(1.1); }
}

/* 图片预览样式 */
.preview-placeholder {
    min-height: 300px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background-color: #f8f9fa;
    border-radius: 0.375rem;
}

/* 进度条样式 */
.progress {
    height: 0.75rem;
    border-radius: 0.375rem;
    background-color: #e9ecef;
}

.progress-bar {
    border-radius: 0.375rem;
    transition: width 0.3s ease;
}

/* 统计卡片样式 */
.stats-card {
    text-align: center;
    padding: 1.5rem;
    border-radius: 0.5rem;
    background: white;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    transition: transform 0.3s ease;
}

.stats-card:hover {
    transform: translateY(-2px);
}

.stats-card .icon {
    font-size: 2.5rem;
    margin-bottom: 1rem;
}

.stats-card .number {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.stats-card .label {
    color: #6c757d;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* 特性列表样式 */
.feature-list {
    list-style: none;
    padding: 0;
}

.feature-list li {
    padding: 0.75rem 0;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    align-items: center;
}

.feature-list li:last-child {
    border-bottom: none;
}

.feature-list .icon {
    margin-right: 1rem;
    color: var(--success-color);
    font-size: 1.25rem;
}

/* 示例图片样式 */
.sample-image {
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.sample-image:hover {
    transform: scale(1.05);
    border-color: var(--primary-color);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.sample-image img {
    border-radius: 0.375rem 0.375rem 0 0;
}

/* 结果表格样式 */
.table {
    margin-bottom: 0;
}

.table th {
    border-top: none;
    font-weight: 600;
    color: var(--dark-color);
    background-color: #f8f9fa;
}

.table td {
    vertical-align: middle;
}

/* 徽章样式 */
.badge {
    font-size: 0.75rem;
    font-weight: 500;
    padding: 0.375rem 0.75rem;
}

/* 警告框样式 */
.alert {
    border: none;
    border-radius: 0.5rem;
    font-weight: 500;
}

.alert .fas {
    margin-right: 0.5rem;
}

/* 加载动画 */
.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .navbar-brand {
        font-size: 1.25rem;
    }

    .hero-title {
        font-size: 2.5rem !important;
    }

    .hero-subtitle {
        font-size: 1rem !important;
    }

    .feature-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }

    .upload-area {
        min-height: 200px;
        padding: 2rem 1rem;
    }

    .upload-icon {
        font-size: 3rem;
    }

    .btn-lg {
        padding: 0.75rem 1.5rem;
        font-size: 1rem;
    }

    .feature-card {
        padding: 1.5rem;
    }

    .stat-item {
        padding: 1rem;
    }

    .stat-value {
        font-size: 2rem;
    }
}

@media (max-width: 576px) {
    .container-fluid {
        padding-left: 1rem;
        padding-right: 1rem;
    }

    .hero-title {
        font-size: 2rem !important;
    }

    .hero-subtitle {
        font-size: 0.9rem !important;
    }

    .stats-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .card-body {
        padding: 1rem;
    }

    .upload-area {
        min-height: 180px;
        padding: 1.5rem 1rem;
    }

    .upload-icon {
        font-size: 2.5rem;
    }

    .upload-text {
        font-size: 1.1rem;
    }

    .upload-hint {
        font-size: 0.85rem;
    }

    .feature-card {
        padding: 1.25rem;
    }

    .feature-icon {
        font-size: 2.5rem;
    }

    .feature-title {
        font-size: 1.2rem;
    }
}

/* 动画效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

.pulse {
    animation: pulse 2s infinite;
}

/* 工具提示样式 */
.tooltip {
    font-size: 0.875rem;
}

.tooltip-inner {
    background-color: var(--dark-color);
    border-radius: 0.375rem;
}

/* 模态框样式 */
.modal-content {
    border: none;
    border-radius: 0.5rem;
    box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175);
}

.modal-header {
    border-bottom: 1px solid #dee2e6;
    background-color: #f8f9fa;
}

.modal-footer {
    border-top: 1px solid #dee2e6;
    background-color: #f8f9fa;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 现代化增强样式 */
.hero-section {
    background: var(--gradient-primary);
    color: white;
    padding: 4rem 0;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

.hero-content {
    position: relative;
    z-index: 1;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.hero-subtitle {
    font-size: 1.3rem;
    opacity: 0.9;
    margin-bottom: 2rem;
}

/* 特色卡片网格 */
.feature-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin: 3rem 0;
}

.feature-card {
    background: white;
    border-radius: var(--border-radius);
    padding: 2rem;
    text-align: center;
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    border: 2px solid transparent;
    position: relative;
    overflow: hidden;
}

.feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-primary);
}

.feature-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--box-shadow-hover);
    border-color: var(--primary-color);
}

.feature-icon {
    font-size: 3.5rem;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 1.5rem;
    display: block;
    animation: float 3s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

.feature-title {
    font-size: 1.4rem;
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 1rem;
}

.feature-description {
    color: #6c757d;
    line-height: 1.6;
}

/* 统计数据增强 */
.stats-section {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    border-radius: var(--border-radius);
    padding: 3rem;
    margin: 2rem 0;
    box-shadow: var(--box-shadow);
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
}

.stat-item {
    text-align: center;
    padding: 1.5rem;
    background: white;
    border-radius: var(--border-radius);
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    transition: var(--transition);
}

.stat-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 15px rgba(0,0,0,0.15);
}

.stat-value {
    font-size: 2.5rem;
    font-weight: 700;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    display: block;
    margin-bottom: 0.5rem;
}

.stat-label {
    color: #6c757d;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 0.9rem;
}

/* 表单增强 */
.form-control {
    border: 2px solid #e9ecef;
    border-radius: var(--border-radius);
    padding: 0.75rem 1rem;
    transition: var(--transition);
    background: white;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    background: white;
}

.form-label {
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 0.75rem;
}

/* 额外的动画和效果 */
.fade-in-up {
    animation: fadeInUp 0.8s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.bounce-in {
    animation: bounceIn 1s ease-out;
}

@keyframes bounceIn {
    0% {
        opacity: 0;
        transform: scale(0.3);
    }
    50% {
        opacity: 1;
        transform: scale(1.05);
    }
    70% {
        transform: scale(0.9);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

/* 滚动条美化 */
::-webkit-scrollbar {
    width: 12px;
}

::-webkit-scrollbar-track {
    background: linear-gradient(90deg, #f1f3f4, #e8eaed);
    border-radius: 6px;
}

::-webkit-scrollbar-thumb {
    background: var(--gradient-primary);
    border-radius: 6px;
    border: 2px solid #f1f3f4;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--gradient-secondary);
}

/* 加载状态优化 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(5px);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 打印样式 */
@media print {
    .navbar,
    .btn,
    .alert {
        display: none !important;
    }

    .card {
        box-shadow: none;
        border: 1px solid #dee2e6;
    }

    body {
        background-color: white;
    }
}
