/**
 * 车牌识别系统主要JavaScript文件
 */

// 全局变量
window.LicensePlateApp = {
    config: {
        maxFileSize: 16 * 1024 * 1024, // 16MB
        allowedTypes: ['image/jpeg', 'image/jpg', 'image/png', 'image/bmp', 'image/webp'],
        apiEndpoints: {
            upload: '/upload',
            stats: '/api/stats',
            dataCollect: '/api/data/collect',
            modelTrain: '/api/model/train'
        }
    },
    
    // 工具函数
    utils: {
        // 格式化文件大小
        formatFileSize: function(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        },
        
        // 格式化时间
        formatDuration: function(seconds) {
            if (seconds < 1) return (seconds * 1000).toFixed(0) + 'ms';
            if (seconds < 60) return seconds.toFixed(2) + 's';
            const minutes = Math.floor(seconds / 60);
            const remainingSeconds = (seconds % 60).toFixed(0);
            return `${minutes}m ${remainingSeconds}s`;
        },
        
        // 验证文件类型
        validateFile: function(file) {
            const errors = [];
            
            if (!file) {
                errors.push('请选择文件');
                return errors;
            }
            
            // 检查文件类型
            if (!this.config.allowedTypes.includes(file.type)) {
                errors.push('不支持的文件格式，请选择图片文件');
            }
            
            // 检查文件大小
            if (file.size > this.config.maxFileSize) {
                errors.push(`文件大小超过${this.formatFileSize(this.config.maxFileSize)}限制`);
            }
            
            return errors;
        }.bind(window.LicensePlateApp),
        
        // 显示提示信息
        showAlert: function(message, type = 'info', duration = 5000) {
            const alertTypes = {
                'success': 'check-circle',
                'danger': 'exclamation-triangle',
                'warning': 'exclamation-circle',
                'info': 'info-circle'
            };
            
            const icon = alertTypes[type] || 'info-circle';
            
            const alertHtml = `
                <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                    <i class="fas fa-${icon}"></i>
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            
            // 查找或创建提示容器
            let alertContainer = document.getElementById('alert-container');
            if (!alertContainer) {
                alertContainer = document.querySelector('main');
            }
            
            if (alertContainer) {
                alertContainer.insertAdjacentHTML('afterbegin', alertHtml);
                
                // 自动消失
                if (duration > 0) {
                    setTimeout(() => {
                        const alert = alertContainer.querySelector('.alert');
                        if (alert) {
                            const bsAlert = new bootstrap.Alert(alert);
                            bsAlert.close();
                        }
                    }, duration);
                }
            }
        },
        
        // 显示加载状态
        showLoading: function(element, text = '处理中...') {
            if (!element) return function() {};
            
            const originalContent = element.innerHTML;
            const originalDisabled = element.disabled;
            
            element.innerHTML = `<i class="fas fa-spinner fa-spin"></i> ${text}`;
            element.disabled = true;
            
            return function hideLoading() {
                element.innerHTML = originalContent;
                element.disabled = originalDisabled;
            };
        },
        
        // 生成唯一ID
        generateId: function() {
            return 'id_' + Math.random().toString(36).substr(2, 9);
        },
        
        // 防抖函数
        debounce: function(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        },
        
        // 节流函数
        throttle: function(func, limit) {
            let inThrottle;
            return function() {
                const args = arguments;
                const context = this;
                if (!inThrottle) {
                    func.apply(context, args);
                    inThrottle = true;
                    setTimeout(() => inThrottle = false, limit);
                }
            };
        }
    },
    
    // API调用
    api: {
        // 上传文件
        uploadFile: function(file, onProgress) {
            return new Promise((resolve, reject) => {
                const formData = new FormData();
                formData.append('file', file);
                
                const xhr = new XMLHttpRequest();
                
                // 进度监听
                if (onProgress) {
                    xhr.upload.addEventListener('progress', function(e) {
                        if (e.lengthComputable) {
                            const percentComplete = (e.loaded / e.total) * 100;
                            onProgress(percentComplete);
                        }
                    });
                }
                
                xhr.addEventListener('load', function() {
                    if (xhr.status === 200) {
                        try {
                            const response = JSON.parse(xhr.responseText);
                            resolve(response);
                        } catch (e) {
                            reject(new Error('响应解析失败'));
                        }
                    } else {
                        try {
                            const error = JSON.parse(xhr.responseText);
                            reject(new Error(error.error || '上传失败'));
                        } catch (e) {
                            reject(new Error('上传失败'));
                        }
                    }
                });
                
                xhr.addEventListener('error', function() {
                    reject(new Error('网络错误'));
                });
                
                xhr.open('POST', window.LicensePlateApp.config.apiEndpoints.upload);
                xhr.send(formData);
            });
        },
        
        // 获取统计信息
        getStats: function() {
            return fetch(window.LicensePlateApp.config.apiEndpoints.stats)
                .then(response => {
                    if (!response.ok) {
                        throw new Error('获取统计信息失败');
                    }
                    return response.json();
                });
        },
        
        // 数据采集
        collectData: function(keywords, maxImages) {
            return fetch(window.LicensePlateApp.config.apiEndpoints.dataCollect, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    keywords: keywords,
                    max_images: maxImages
                })
            }).then(response => {
                if (!response.ok) {
                    throw new Error('数据采集请求失败');
                }
                return response.json();
            });
        },
        
        // 模型训练
        trainModel: function(modelType, epochs) {
            return fetch(window.LicensePlateApp.config.apiEndpoints.modelTrain, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    model_type: modelType,
                    epochs: epochs
                })
            }).then(response => {
                if (!response.ok) {
                    throw new Error('模型训练请求失败');
                }
                return response.json();
            });
        }
    },
    
    // 组件
    components: {
        // 文件上传组件
        FileUploader: function(containerId, options = {}) {
            const container = document.getElementById(containerId);
            if (!container) {
                console.error('找不到容器元素:', containerId);
                return;
            }
            
            const defaultOptions = {
                multiple: false,
                accept: 'image/*',
                maxSize: window.LicensePlateApp.config.maxFileSize,
                onSelect: null,
                onUpload: null,
                onProgress: null,
                onSuccess: null,
                onError: null
            };
            
            const config = Object.assign(defaultOptions, options);
            
            // 创建文件输入
            const fileInput = document.createElement('input');
            fileInput.type = 'file';
            fileInput.accept = config.accept;
            fileInput.multiple = config.multiple;
            fileInput.style.display = 'none';
            container.appendChild(fileInput);
            
            // 文件选择处理
            fileInput.addEventListener('change', function(e) {
                const files = Array.from(e.target.files);
                if (files.length > 0 && config.onSelect) {
                    config.onSelect(files[0]);
                }
            });
            
            return {
                selectFile: function() {
                    fileInput.click();
                },
                uploadFile: function(file) {
                    if (config.onUpload) {
                        config.onUpload(file);
                    }
                }
            };
        },
        
        // 进度条组件
        ProgressBar: function(containerId) {
            const container = document.getElementById(containerId);
            if (!container) return;
            
            const progressHtml = `
                <div class="progress">
                    <div class="progress-bar progress-bar-striped progress-bar-animated" 
                         role="progressbar" style="width: 0%"></div>
                </div>
            `;
            
            container.innerHTML = progressHtml;
            const progressBar = container.querySelector('.progress-bar');
            
            return {
                setProgress: function(percent) {
                    progressBar.style.width = percent + '%';
                    progressBar.setAttribute('aria-valuenow', percent);
                },
                show: function() {
                    container.style.display = 'block';
                },
                hide: function() {
                    container.style.display = 'none';
                }
            };
        }
    },
    
    // 初始化
    init: function() {
        console.log('车牌识别系统初始化...');
        
        // 设置全局错误处理
        window.addEventListener('error', function(e) {
            console.error('页面错误:', e.error);
        });
        
        // 设置AJAX错误处理
        if (window.jQuery) {
            $(document).ajaxError(function(event, xhr, settings, error) {
                console.error('AJAX错误:', error);

                // 只对重要的API请求显示错误提示，忽略统计信息等非关键请求
                if (settings.url && settings.url.includes('/api/stats')) {
                    // 统计信息请求失败时只记录日志，不显示错误提示
                    console.warn('统计信息获取失败，将在下次自动重试');
                    return;
                }

                let message = '请求失败';
                if (xhr.status === 500) {
                    message = '服务器内部错误，请稍后重试';
                } else if (xhr.status === 404) {
                    message = '请求的资源不存在';
                } else if (xhr.status === 0) {
                    message = '网络连接失败，请检查网络';
                }

                window.LicensePlateApp.utils.showAlert(message, 'danger');
            });
        }
        
        console.log('车牌识别系统初始化完成');
    }
};

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    window.LicensePlateApp.init();
});

// 导出到全局
window.formatFileSize = window.LicensePlateApp.utils.formatFileSize;
window.formatDuration = window.LicensePlateApp.utils.formatDuration;
window.showAlert = window.LicensePlateApp.utils.showAlert;
window.showLoading = window.LicensePlateApp.utils.showLoading;
