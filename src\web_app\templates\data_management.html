{% extends "base.html" %}

{% block title %}数据管理{% endblock %}

{% block content %}
<div id="alert-container"></div>

<!-- 页面标题 -->
<div class="hero-section mb-5" style="padding: 3rem 0; background: var(--gradient-success);">
    <div class="hero-content">
        <h1 class="hero-title" style="font-size: 2.5rem;">
            <i class="fas fa-database me-3"></i>数据管理
        </h1>
        <p class="hero-subtitle" style="font-size: 1.1rem;">
            管理训练数据集，进行数据采集和预处理操作
        </p>
    </div>
</div>

<div class="row">
    <!-- 左侧：数据采集 -->
    <div class="col-md-6">
        <div class="card h-100">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-download"></i> 数据采集
                </h5>
            </div>
            <div class="card-body">
                <form id="data-collection-form">
                    <div class="mb-3">
                        <label for="keywords" class="form-label">搜索关键词</label>
                        <input type="text" class="form-control" id="keywords" 
                               placeholder="车牌, 汽车牌照, 车辆" value="车牌">
                        <div class="form-text">多个关键词用逗号分隔</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="max-images" class="form-label">采集数量</label>
                        <input type="number" class="form-control" id="max-images" 
                               min="10" max="1000" value="100">
                        <div class="form-text">建议每次采集100-500张图片</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="source" class="form-label">数据源</label>
                        <select class="form-select" id="source">
                            <option value="baidu">百度图片</option>
                            <option value="ccpd">CCPD数据集</option>
                            <option value="local">本地上传</option>
                        </select>
                    </div>
                    
                    <button type="submit" class="btn btn-success w-100">
                        <i class="fas fa-play"></i> 开始采集
                    </button>
                </form>
                
                <!-- 采集进度 -->
                <div id="collection-progress" class="mt-4 d-none">
                    <h6>采集进度</h6>
                    <div class="progress mb-2">
                        <div id="collection-progress-bar" class="progress-bar bg-success" 
                             role="progressbar" style="width: 0%"></div>
                    </div>
                    <div class="d-flex justify-content-between">
                        <small id="collection-status">准备中...</small>
                        <small id="collection-count">0/0</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 右侧：数据统计 -->
    <div class="col-md-6">
        <div class="card h-100">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-bar"></i> 数据统计
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center mb-4">
                    <div class="col-6">
                        <h4 class="text-primary">1,250</h4>
                        <p class="text-muted">训练图片</p>
                    </div>
                    <div class="col-6">
                        <h4 class="text-info">320</h4>
                        <p class="text-muted">验证图片</p>
                    </div>
                </div>
                
                <div class="mb-3">
                    <h6>数据集分布</h6>
                    <canvas id="dataset-chart" width="400" height="200"></canvas>
                </div>
                
                <div class="mb-3">
                    <h6>车牌类型分布</h6>
                    <div class="progress mb-2">
                        <div class="progress-bar bg-primary" style="width: 60%">蓝牌 60%</div>
                    </div>
                    <div class="progress mb-2">
                        <div class="progress-bar bg-warning" style="width: 25%">黄牌 25%</div>
                    </div>
                    <div class="progress mb-2">
                        <div class="progress-bar bg-success" style="width: 15%">绿牌 15%</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 数据集管理 -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-folder"></i> 数据集管理
                </h5>
                <div>
                    <button class="btn btn-outline-primary btn-sm" id="refresh-datasets">
                        <i class="fas fa-sync"></i> 刷新
                    </button>
                    <button class="btn btn-primary btn-sm" id="upload-dataset">
                        <i class="fas fa-upload"></i> 上传数据集
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>数据集名称</th>
                                <th>图片数量</th>
                                <th>标注数量</th>
                                <th>创建时间</th>
                                <th>大小</th>
                                <th>状态</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="datasets-table">
                            <tr>
                                <td>
                                    <i class="fas fa-folder text-primary"></i>
                                    CCPD训练集
                                </td>
                                <td>1,250</td>
                                <td>1,250</td>
                                <td>2025-01-15</td>
                                <td>245.6 MB</td>
                                <td>
                                    <span class="badge bg-success">已处理</span>
                                </td>
                                <td>
                                    <button class="btn btn-sm btn-outline-info">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-sm btn-outline-warning">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-sm btn-outline-danger">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <i class="fas fa-folder text-warning"></i>
                                    百度采集数据
                                </td>
                                <td>320</td>
                                <td>0</td>
                                <td>2025-01-20</td>
                                <td>68.2 MB</td>
                                <td>
                                    <span class="badge bg-warning">待标注</span>
                                </td>
                                <td>
                                    <button class="btn btn-sm btn-outline-info">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-tags"></i>
                                    </button>
                                    <button class="btn btn-sm btn-outline-danger">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // 初始化图表
    initDatasetChart();

    // 数据采集表单提交
    $('#data-collection-form').on('submit', function(e) {
        e.preventDefault();

        const keywords = $('#keywords').val().split(',').map(k => k.trim()).filter(k => k);
        const maxImages = parseInt($('#max-images').val());
        const source = $('#source').val();

        if (keywords.length === 0) {
            showAlert('请输入搜索关键词', 'warning');
            return;
        }

        startDataCollection(keywords, maxImages, source);
    });

    // 开始数据采集
    function startDataCollection(keywords, maxImages, source) {
        const submitBtn = $('#data-collection-form button[type="submit"]');
        const hideLoading = showLoading(submitBtn[0], '采集中...');

        $('#collection-progress').removeClass('d-none');
        $('#collection-status').text('正在连接数据源...');

        // 模拟采集进度
        let progress = 0;
        const progressInterval = setInterval(() => {
            progress += Math.random() * 10;
            if (progress > 95) progress = 95;

            $('#collection-progress-bar').css('width', progress + '%');
            $('#collection-count').text(`${Math.floor(progress * maxImages / 100)}/${maxImages}`);

            if (progress > 30) $('#collection-status').text('正在下载图片...');
            if (progress > 60) $('#collection-status').text('正在处理图片...');
            if (progress > 80) $('#collection-status').text('正在保存数据...');
        }, 500);

        // 调用API
        window.LicensePlateApp.api.collectData(keywords, maxImages)
            .then(response => {
                clearInterval(progressInterval);
                $('#collection-progress-bar').css('width', '100%');
                $('#collection-status').text('采集完成');
                $('#collection-count').text(`${maxImages}/${maxImages}`);

                setTimeout(() => {
                    $('#collection-progress').addClass('d-none');
                    hideLoading();
                    showAlert(`成功采集 ${maxImages} 张图片`, 'success');
                }, 1000);
            })
            .catch(error => {
                clearInterval(progressInterval);
                hideLoading();
                $('#collection-progress').addClass('d-none');
                showAlert('数据采集失败: ' + error.message, 'danger');
            });
    }

    // 初始化数据集分布图表
    function initDatasetChart() {
        const ctx = document.getElementById('dataset-chart').getContext('2d');
        new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: ['训练集', '验证集', '测试集'],
                datasets: [{
                    data: [1250, 320, 80],
                    backgroundColor: [
                        '#0d6efd',
                        '#198754',
                        '#ffc107'
                    ]
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    }

    // 刷新数据集
    $('#refresh-datasets').on('click', function() {
        const hideLoading = showLoading(this);

        setTimeout(() => {
            hideLoading();
            showAlert('数据集列表已刷新', 'success');
        }, 1000);
    });

    // 上传数据集
    $('#upload-dataset').on('click', function() {
        showAlert('数据集上传功能开发中...', 'info');
    });
});
</script>
{% endblock %}
