{% extends "base.html" %}

{% block title %}车牌识别演示{% endblock %}

{% block content %}
<div id="alert-container"></div>

<!-- 页面标题 -->
<div class="hero-section mb-5" style="padding: 3rem 0;">
    <div class="hero-content">
        <h1 class="hero-title" style="font-size: 2.5rem;">
            <i class="fas fa-camera me-3"></i>车牌识别演示
        </h1>
        <p class="hero-subtitle" style="font-size: 1.1rem;">
            上传图片进行实时车牌检测和识别，体验AI的强大能力
        </p>
    </div>
</div>

<div class="row">
    <!-- 左侧：上传区域 -->
    <div class="col-md-6">
        <div class="card h-100">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-upload"></i> 图片上传
                </h5>
            </div>
            <div class="card-body">
                <!-- 拖拽上传区域 -->
                <div id="upload-area" class="upload-area text-center p-5">
                    <div class="upload-icon">
                        <i class="fas fa-cloud-upload-alt"></i>
                    </div>
                    <div class="upload-text">拖拽图片到此处或点击选择</div>
                    <div class="upload-hint">支持 JPG, PNG, BMP, WEBP 格式 | 最大 16MB</div>
                    <input type="file" id="file-input" class="d-none" accept="image/*">
                    <button type="button" class="btn btn-primary btn-lg mt-3" onclick="document.getElementById('file-input').click()">
                        <i class="fas fa-folder-open me-2"></i>选择图片
                    </button>
                </div>
                
                <!-- 文件信息 -->
                <div id="file-info" class="mt-3 d-none">
                    <div class="card bg-light">
                        <div class="card-body">
                            <h6>文件信息</h6>
                            <p class="mb-1"><strong>文件名:</strong> <span id="file-name"></span></p>
                            <p class="mb-1"><strong>文件大小:</strong> <span id="file-size"></span></p>
                            <p class="mb-0"><strong>文件类型:</strong> <span id="file-type"></span></p>
                        </div>
                    </div>
                </div>
                
                <!-- 上传按钮 -->
                <div class="mt-3">
                    <button id="upload-btn" class="btn btn-success btn-lg w-100" disabled>
                        <i class="fas fa-play"></i> 开始识别
                    </button>
                </div>
                
                <!-- 进度条 -->
                <div id="progress-container" class="mt-3 d-none">
                    <div class="progress">
                        <div id="progress-bar" class="progress-bar progress-bar-striped progress-bar-animated" 
                             role="progressbar" style="width: 0%"></div>
                    </div>
                    <small class="text-muted">正在处理图片...</small>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 右侧：预览区域 -->
    <div class="col-md-6">
        <div class="card h-100">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-image"></i> 图片预览
                </h5>
            </div>
            <div class="card-body">
                <div id="image-preview" class="text-center">
                    <div class="preview-placeholder">
                        <i class="fas fa-image fa-4x text-muted mb-3"></i>
                        <h5 class="text-muted">选择图片后将在此显示预览</h5>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 识别结果 -->
<div id="results-section" class="row mt-4 d-none">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-search"></i> 识别结果
                </h5>
            </div>
            <div class="card-body">
                <!-- 结果统计 -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="text-center">
                            <h4 id="detection-count" class="text-primary">0</h4>
                            <p class="text-muted">检测到车牌</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <h4 id="processing-time" class="text-info">0.00s</h4>
                            <p class="text-muted">处理时间</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <h4 id="image-size" class="text-warning">0x0</h4>
                            <p class="text-muted">图片尺寸</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <h4 id="avg-confidence" class="text-success">0%</h4>
                            <p class="text-muted">平均置信度</p>
                        </div>
                    </div>
                </div>
                
                <!-- 结果图片 -->
                <div class="row">
                    <div class="col-md-6">
                        <h6>原始图片</h6>
                        <div class="border rounded p-2">
                            <img id="original-image" class="img-fluid" style="max-height: 300px;">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h6>检测结果</h6>
                        <div class="border rounded p-2">
                            <img id="result-image" class="img-fluid" style="max-height: 300px;">
                        </div>
                    </div>
                </div>
                
                <!-- 详细结果 -->
                <div class="mt-4">
                    <h6>检测详情</h6>
                    <div id="detection-details" class="table-responsive">
                        <!-- 动态生成表格 -->
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 示例图片 -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-images"></i> 示例图片
                </h5>
            </div>
            <div class="card-body">
                <p class="text-muted">点击下方示例图片进行快速测试</p>
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <div class="card sample-image" data-sample="placeholder.jpg">
                            <img src="{{ url_for('static', filename='images/placeholder.jpg') }}"
                                 class="card-img-top" style="height: 150px; object-fit: cover;">
                            <div class="card-body text-center p-2">
                                <small>单车牌示例</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card sample-image" data-sample="placeholder.jpg">
                            <img src="{{ url_for('static', filename='images/placeholder.jpg') }}"
                                 class="card-img-top" style="height: 150px; object-fit: cover;">
                            <div class="card-body text-center p-2">
                                <small>多车牌示例</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card sample-image" data-sample="placeholder.jpg">
                            <img src="{{ url_for('static', filename='images/placeholder.jpg') }}"
                                 class="card-img-top" style="height: 150px; object-fit: cover;">
                            <div class="card-body text-center p-2">
                                <small>复杂环境</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card sample-image" data-sample="placeholder.jpg">
                            <img src="{{ url_for('static', filename='images/placeholder.jpg') }}"
                                 class="card-img-top" style="height: 150px; object-fit: cover;">
                            <div class="card-body text-center p-2">
                                <small>新能源车牌</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.upload-area {
    border: 2px dashed #dee2e6;
    border-radius: 0.375rem;
    transition: all 0.3s ease;
    cursor: pointer;
}

.upload-area:hover {
    border-color: #0d6efd;
    background-color: #f8f9fa;
}

.upload-area.dragover {
    border-color: #0d6efd;
    background-color: #e7f3ff;
}

.preview-placeholder {
    padding: 3rem 1rem;
}

.sample-image {
    cursor: pointer;
    transition: transform 0.2s ease;
}

.sample-image:hover {
    transform: scale(1.05);
}

.border-dashed {
    border-style: dashed !important;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    let selectedFile = null;
    
    // 文件选择处理
    $('#file-input').on('change', function(e) {
        handleFileSelect(e.target.files[0]);
    });
    
    // 拖拽上传
    const uploadArea = document.getElementById('upload-area');
    
    uploadArea.addEventListener('dragover', function(e) {
        e.preventDefault();
        uploadArea.classList.add('dragover');
    });
    
    uploadArea.addEventListener('dragleave', function(e) {
        e.preventDefault();
        uploadArea.classList.remove('dragover');
    });
    
    uploadArea.addEventListener('drop', function(e) {
        e.preventDefault();
        uploadArea.classList.remove('dragover');
        
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            handleFileSelect(files[0]);
        }
    });
    
    // 点击上传区域
    uploadArea.addEventListener('click', function() {
        document.getElementById('file-input').click();
    });
    
    // 处理文件选择
    function handleFileSelect(file) {
        if (!file) return;
        
        // 验证文件类型
        const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/bmp', 'image/webp'];
        if (!allowedTypes.includes(file.type)) {
            showAlert('不支持的文件格式，请选择图片文件', 'danger');
            return;
        }
        
        // 验证文件大小 (16MB)
        if (file.size > 16 * 1024 * 1024) {
            showAlert('文件大小超过16MB限制', 'danger');
            return;
        }
        
        selectedFile = file;
        
        // 显示文件信息
        $('#file-name').text(file.name);
        $('#file-size').text(formatFileSize(file.size));
        $('#file-type').text(file.type);
        $('#file-info').removeClass('d-none');
        
        // 启用上传按钮
        $('#upload-btn').prop('disabled', false);
        
        // 显示预览
        const reader = new FileReader();
        reader.onload = function(e) {
            $('#image-preview').html(`
                <img src="${e.target.result}" class="img-fluid" style="max-height: 400px;">
            `);
        };
        reader.readAsDataURL(file);
    }
    
    // 上传并识别
    $('#upload-btn').on('click', function() {
        if (!selectedFile) {
            showAlert('请先选择图片', 'warning');
            return;
        }
        
        const formData = new FormData();
        formData.append('file', selectedFile);
        
        const hideLoading = showLoading(this);
        $('#progress-container').removeClass('d-none');
        
        // 模拟进度
        let progress = 0;
        const progressInterval = setInterval(() => {
            progress += Math.random() * 30;
            if (progress > 90) progress = 90;
            $('#progress-bar').css('width', progress + '%');
        }, 200);
        
        $.ajax({
            url: '/upload',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                clearInterval(progressInterval);
                $('#progress-bar').css('width', '100%');
                
                setTimeout(() => {
                    $('#progress-container').addClass('d-none');
                    hideLoading();
                    displayResults(response);
                }, 500);
            },
            error: function(xhr) {
                clearInterval(progressInterval);
                $('#progress-container').addClass('d-none');
                hideLoading();
                
                const error = xhr.responseJSON ? xhr.responseJSON.error : '处理失败';
                showAlert(error, 'danger');
            }
        });
    });
    
    // 显示识别结果
    function displayResults(response) {
        if (!response.success) {
            showAlert(response.error || '识别失败', 'danger');
            return;
        }
        
        // 显示结果区域
        $('#results-section').removeClass('d-none');
        
        // 更新统计信息
        $('#detection-count').text(response.detections);
        $('#processing-time').text(formatDuration(response.processing_time));
        $('#image-size').text(response.image_size);
        
        // 计算平均置信度
        let avgConfidence = 0;
        if (response.results && response.results.length > 0) {
            const totalConfidence = response.results.reduce((sum, r) => sum + r.confidence, 0);
            avgConfidence = totalConfidence / response.results.length;
        }
        $('#avg-confidence').text((avgConfidence * 100).toFixed(1) + '%');
        
        // 显示原始图片
        const reader = new FileReader();
        reader.onload = function(e) {
            $('#original-image').attr('src', e.target.result);
        };
        reader.readAsDataURL(selectedFile);
        
        // 显示结果图片
        if (response.result_image) {
            $('#result-image').attr('src', `/results/${response.result_image}`);
        }
        
        // 显示详细结果
        displayDetectionDetails(response.results);
        
        showAlert(`成功检测到 ${response.detections} 个车牌`, 'success');
    }
    
    // 显示检测详情
    function displayDetectionDetails(results) {
        if (!results || results.length === 0) {
            $('#detection-details').html('<p class="text-muted">未检测到车牌</p>');
            return;
        }
        
        let tableHtml = `
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>序号</th>
                        <th>车牌号码</th>
                        <th>置信度</th>
                        <th>位置</th>
                    </tr>
                </thead>
                <tbody>
        `;
        
        results.forEach((result, index) => {
            const bbox = result.bbox;
            const position = `(${bbox[0]}, ${bbox[1]}) - (${bbox[2]}, ${bbox[3]})`;
            
            tableHtml += `
                <tr>
                    <td>${index + 1}</td>
                    <td><strong>${result.plate_text}</strong></td>
                    <td>
                        <span class="badge bg-${result.confidence > 0.8 ? 'success' : result.confidence > 0.6 ? 'warning' : 'danger'}">
                            ${(result.confidence * 100).toFixed(1)}%
                        </span>
                    </td>
                    <td><small>${position}</small></td>
                </tr>
            `;
        });
        
        tableHtml += '</tbody></table>';
        $('#detection-details').html(tableHtml);
    }
    
    // 示例图片点击
    $('.sample-image').on('click', function() {
        const sampleName = $(this).data('sample');
        showAlert('示例图片功能开发中...', 'info');
    });
});
</script>
{% endblock %}
