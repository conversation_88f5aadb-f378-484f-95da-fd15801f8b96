{% extends "base.html" %}

{% block title %}车牌识别系统 - 首页{% endblock %}

{% block content %}
<div id="alert-container"></div>

<!-- 英雄区域 -->
<div class="hero-section mb-5">
    <div class="hero-content">
        <h1 class="hero-title">
            <i class="fas fa-car me-3"></i>智能车牌识别系统
        </h1>
        <p class="hero-subtitle">
            基于深度学习的高精度车牌检测与识别解决方案
        </p>
        <div class="d-flex justify-content-center gap-3 mb-4">
            <a class="btn btn-light btn-lg" href="{{ url_for('demo') }}">
                <i class="fas fa-play me-2"></i>立即体验
            </a>
            <a class="btn btn-outline-light btn-lg" href="{{ url_for('data_management') }}">
                <i class="fas fa-database me-2"></i>数据管理
            </a>
        </div>

        <!-- 特色功能 -->
        <div class="row justify-content-center mt-5">
            <div class="col-md-8">
                <div class="row text-center">
                    <div class="col-md-3 mb-3">
                        <div style="font-size: 2.5rem; color: rgba(255,255,255,0.9); margin-bottom: 1rem;">
                            <i class="fas fa-eye"></i>
                        </div>
                        <h5>智能检测</h5>
                        <p class="small opacity-75">精准定位车牌位置</p>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div style="font-size: 2.5rem; color: rgba(255,255,255,0.9); margin-bottom: 1rem;">
                            <i class="fas fa-bolt"></i>
                        </div>
                        <h5>高速识别</h5>
                        <p class="small opacity-75">毫秒级处理速度</p>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div style="font-size: 2.5rem; color: rgba(255,255,255,0.9); margin-bottom: 1rem;">
                            <i class="fas fa-brain"></i>
                        </div>
                        <h5>深度学习</h5>
                        <p class="small opacity-75">先进AI算法</p>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div style="font-size: 2.5rem; color: rgba(255,255,255,0.9); margin-bottom: 1rem;">
                            <i class="fas fa-language"></i>
                        </div>
                        <h5>中文支持</h5>
                        <p class="small opacity-75">完整中文字符识别</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 系统统计 -->
<div class="stats-section fade-in-up">
    <div class="text-center mb-4">
        <h2 class="fw-bold">系统性能统计</h2>
        <p class="text-muted">实时监控系统运行状态和性能指标</p>
    </div>

    <div class="stats-grid">
        <div class="stat-item">
            <i class="fas fa-images fa-3x mb-3" style="color: var(--primary-color);"></i>
            <div class="stat-value" id="total-detections">{{ stats.total_detections }}</div>
            <div class="stat-label">总检测次数</div>
        </div>
        <div class="stat-item">
            <i class="fas fa-check-circle fa-3x mb-3" style="color: var(--success-color);"></i>
            <div class="stat-value" id="successful-detections">{{ stats.successful_detections }}</div>
            <div class="stat-label">成功识别</div>
        </div>
        <div class="stat-item">
            <i class="fas fa-percentage fa-3x mb-3" style="color: #0dcaf0;"></i>
            <div class="stat-value" id="success-rate">
                {% if stats.total_detections > 0 %}
                    {{ "%.1f"|format(stats.successful_detections / stats.total_detections * 100) }}%
                {% else %}
                    0.0%
                {% endif %}
            </div>
            <div class="stat-label">识别准确率</div>
        </div>
        <div class="stat-item">
            <i class="fas fa-clock fa-3x mb-3" style="color: var(--warning-color);"></i>
            <div class="stat-value" id="avg-time">
                {% if stats.total_detections > 0 %}
                    {{ "%.2f"|format(stats.total_processing_time / stats.total_detections) }}s
                {% else %}
                    0.00s
                {% endif %}
            </div>
            <div class="stat-label">平均处理时间</div>
        </div>
    </div>
</div>

<!-- 快速开始 -->
<div class="mb-5 fade-in-up">
    <div class="text-center mb-4">
        <h2 class="fw-bold">
            <i class="fas fa-rocket me-2"></i>快速开始
        </h2>
        <p class="text-muted">选择您需要的功能模块，快速开始使用系统</p>
    </div>

    <div class="feature-grid">
        <div class="feature-card">
            <i class="fas fa-camera feature-icon"></i>
            <h3 class="feature-title">车牌识别演示</h3>
            <p class="feature-description">上传图片进行实时车牌检测和识别，体验系统的核心功能</p>
            <a href="{{ url_for('demo') }}" class="btn btn-primary btn-lg">
                <i class="fas fa-play me-2"></i>开始演示
            </a>
        </div>

        <div class="feature-card">
            <i class="fas fa-database feature-icon"></i>
            <h3 class="feature-title">数据管理</h3>
            <p class="feature-description">管理训练数据集，进行数据收集和预处理操作</p>
            <a href="{{ url_for('data_management') }}" class="btn btn-success btn-lg">
                <i class="fas fa-cog me-2"></i>数据管理
            </a>
        </div>

        <div class="feature-card">
            <i class="fas fa-brain feature-icon"></i>
            <h3 class="feature-title">模型训练</h3>
            <p class="feature-description">训练和优化深度学习模型，提升识别准确率</p>
            <a href="{{ url_for('model_training') }}" class="btn btn-warning btn-lg">
                <i class="fas fa-graduation-cap me-2"></i>开始训练
            </a>
        </div>
    </div>
</div>

<!-- 系统特性 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-star"></i> 系统特性
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <div class="d-flex">
                            <div class="flex-shrink-0">
                                <i class="fas fa-check-circle text-success fa-lg"></i>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h6>高精度检测</h6>
                                <p class="text-muted mb-0">使用改进的YOLO算法，检测准确率≥95%</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <div class="d-flex">
                            <div class="flex-shrink-0">
                                <i class="fas fa-check-circle text-success fa-lg"></i>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h6>实时处理</h6>
                                <p class="text-muted mb-0">单张图片处理时间≤1秒</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <div class="d-flex">
                            <div class="flex-shrink-0">
                                <i class="fas fa-check-circle text-success fa-lg"></i>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h6>多车牌支持</h6>
                                <p class="text-muted mb-0">支持蓝牌、黄牌、绿牌等多种车牌类型</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <div class="d-flex">
                            <div class="flex-shrink-0">
                                <i class="fas fa-check-circle text-success fa-lg"></i>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h6>中文字符</h6>
                                <p class="text-muted mb-0">完整支持中文省份简称和字符识别</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <div class="d-flex">
                            <div class="flex-shrink-0">
                                <i class="fas fa-check-circle text-success fa-lg"></i>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h6>复杂环境</h6>
                                <p class="text-muted mb-0">适应不同光照、角度、遮挡等复杂场景</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <div class="d-flex">
                            <div class="flex-shrink-0">
                                <i class="fas fa-check-circle text-success fa-lg"></i>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h6>深度学习</h6>
                                <p class="text-muted mb-0">基于PyTorch的先进深度学习架构</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 技术架构 -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-sitemap"></i> 技术架构
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-md-2">
                        <div class="p-3 border rounded">
                            <i class="fas fa-upload fa-2x text-primary mb-2"></i>
                            <h6>图像输入</h6>
                            <small class="text-muted">支持多种格式</small>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="p-3 border rounded">
                            <i class="fas fa-cogs fa-2x text-info mb-2"></i>
                            <h6>预处理</h6>
                            <small class="text-muted">去噪增强</small>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="p-3 border rounded">
                            <i class="fas fa-search fa-2x text-warning mb-2"></i>
                            <h6>车牌检测</h6>
                            <small class="text-muted">YOLO算法</small>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="p-3 border rounded">
                            <i class="fas fa-cut fa-2x text-danger mb-2"></i>
                            <h6>字符分割</h6>
                            <small class="text-muted">精确分割</small>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="p-3 border rounded">
                            <i class="fas fa-brain fa-2x text-success mb-2"></i>
                            <h6>字符识别</h6>
                            <small class="text-muted">CNN识别</small>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="p-3 border rounded">
                            <i class="fas fa-file-alt fa-2x text-secondary mb-2"></i>
                            <h6>结果输出</h6>
                            <small class="text-muted">车牌号码</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // 定期更新统计信息
    function updateStats() {
        $.get('/api/stats')
            .done(function(data) {
                if (data && typeof data === 'object') {
                    $('#total-detections').text(data.total_detections || 0);
                    $('#successful-detections').text(data.successful_detections || 0);
                    $('#success-rate').text((data.success_rate || 0).toFixed(1) + '%');
                    $('#avg-time').text((data.avg_processing_time || 0).toFixed(2) + 's');
                }
            })
            .fail(function(xhr, status, error) {
                // 静默处理失败，不显示错误提示
                console.warn('统计信息更新失败:', status, error);
            });
    }
    
    // 每30秒更新一次统计信息
    setInterval(updateStats, 30000);
});
</script>
{% endblock %}
