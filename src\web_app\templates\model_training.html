{% extends "base.html" %}

{% block title %}模型训练{% endblock %}

{% block content %}
<div id="alert-container"></div>

<!-- 页面标题 -->
<div class="hero-section mb-5" style="padding: 3rem 0; background: var(--gradient-warning);">
    <div class="hero-content">
        <h1 class="hero-title" style="font-size: 2.5rem; color: var(--dark-color);">
            <i class="fas fa-brain me-3"></i>模型训练
        </h1>
        <p class="hero-subtitle" style="font-size: 1.1rem; color: var(--dark-color); opacity: 0.8;">
            训练和优化深度学习模型，提升识别准确率和性能
        </p>
    </div>
</div>

<div class="row">
    <!-- 左侧：训练配置 -->
    <div class="col-md-6">
        <div class="card h-100">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-cogs"></i> 训练配置
                </h5>
            </div>
            <div class="card-body">
                <form id="training-form">
                    <div class="mb-3">
                        <label for="model-type" class="form-label">模型类型</label>
                        <select class="form-select" id="model-type">
                            <option value="detector">车牌检测器 (YOLO)</option>
                            <option value="recognizer">字符识别器 (CNN)</option>
                            <option value="segmenter">字符分割器</option>
                            <option value="end2end">端到端模型</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="epochs" class="form-label">训练轮数</label>
                        <input type="number" class="form-control" id="epochs" 
                               min="1" max="1000" value="50">
                        <div class="form-text">建议检测器50-100轮，识别器20-50轮</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="batch-size" class="form-label">批次大小</label>
                        <select class="form-select" id="batch-size">
                            <option value="8">8 (低显存)</option>
                            <option value="16" selected>16 (推荐)</option>
                            <option value="32">32 (高显存)</option>
                            <option value="64">64 (超高显存)</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="learning-rate" class="form-label">学习率</label>
                        <select class="form-select" id="learning-rate">
                            <option value="0.001" selected>0.001 (推荐)</option>
                            <option value="0.0001">0.0001 (保守)</option>
                            <option value="0.01">0.01 (激进)</option>
                            <option value="auto">自动调整</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="dataset" class="form-label">训练数据集</label>
                        <select class="form-select" id="dataset">
                            <option value="ccpd">CCPD训练集</option>
                            <option value="mixed">混合数据集</option>
                            <option value="custom">自定义数据集</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="use-pretrained" checked>
                            <label class="form-check-label" for="use-pretrained">
                                使用预训练权重
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="data-augmentation" checked>
                            <label class="form-check-label" for="data-augmentation">
                                数据增强
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="early-stopping">
                            <label class="form-check-label" for="early-stopping">
                                早停机制
                            </label>
                        </div>
                    </div>
                    
                    <button type="submit" class="btn btn-info w-100 btn-lg">
                        <i class="fas fa-play"></i> 开始训练
                    </button>
                </form>
            </div>
        </div>
    </div>
    
    <!-- 右侧：训练状态 -->
    <div class="col-md-6">
        <div class="card h-100">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-line"></i> 训练状态
                </h5>
            </div>
            <div class="card-body">
                <div id="training-status" class="text-center py-5">
                    <i class="fas fa-play-circle fa-4x text-muted mb-3"></i>
                    <h5 class="text-muted">点击开始训练按钮启动模型训练</h5>
                </div>
                
                <div id="training-progress" class="d-none">
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>训练进度</span>
                            <span id="epoch-progress">0/50</span>
                        </div>
                        <div class="progress">
                            <div id="epoch-progress-bar" class="progress-bar bg-info" 
                                 role="progressbar" style="width: 0%"></div>
                        </div>
                    </div>
                    
                    <div class="row text-center mb-3">
                        <div class="col-6">
                            <h5 id="current-loss" class="text-danger">0.000</h5>
                            <small class="text-muted">当前损失</small>
                        </div>
                        <div class="col-6">
                            <h5 id="current-accuracy" class="text-success">0.0%</h5>
                            <small class="text-muted">当前精度</small>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <h6>训练曲线</h6>
                        <canvas id="training-chart" width="400" height="200"></canvas>
                    </div>
                    
                    <div class="text-center">
                        <button id="stop-training" class="btn btn-danger btn-sm">
                            <i class="fas fa-stop"></i> 停止训练
                        </button>
                        <button id="pause-training" class="btn btn-warning btn-sm">
                            <i class="fas fa-pause"></i> 暂停训练
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 模型管理 -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-save"></i> 模型管理
                </h5>
                <div>
                    <button class="btn btn-outline-primary btn-sm" id="refresh-models">
                        <i class="fas fa-sync"></i> 刷新
                    </button>
                    <button class="btn btn-primary btn-sm" id="export-model">
                        <i class="fas fa-download"></i> 导出模型
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>模型名称</th>
                                <th>类型</th>
                                <th>精度</th>
                                <th>训练时间</th>
                                <th>文件大小</th>
                                <th>状态</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="models-table">
                            <tr>
                                <td>
                                    <i class="fas fa-brain text-primary"></i>
                                    yolo_detector_v1.0
                                </td>
                                <td>车牌检测</td>
                                <td>
                                    <span class="badge bg-success">96.8%</span>
                                </td>
                                <td>2h 15m</td>
                                <td>245.6 MB</td>
                                <td>
                                    <span class="badge bg-success">已完成</span>
                                </td>
                                <td>
                                    <button class="btn btn-sm btn-outline-success">
                                        <i class="fas fa-play"></i>
                                    </button>
                                    <button class="btn btn-sm btn-outline-info">
                                        <i class="fas fa-chart-bar"></i>
                                    </button>
                                    <button class="btn btn-sm btn-outline-warning">
                                        <i class="fas fa-download"></i>
                                    </button>
                                    <button class="btn btn-sm btn-outline-danger">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <i class="fas fa-brain text-info"></i>
                                    char_recognizer_v2.1
                                </td>
                                <td>字符识别</td>
                                <td>
                                    <span class="badge bg-warning">训练中</span>
                                </td>
                                <td>45m</td>
                                <td>128.3 MB</td>
                                <td>
                                    <span class="badge bg-warning">训练中</span>
                                </td>
                                <td>
                                    <button class="btn btn-sm btn-outline-warning">
                                        <i class="fas fa-pause"></i>
                                    </button>
                                    <button class="btn btn-sm btn-outline-danger">
                                        <i class="fas fa-stop"></i>
                                    </button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    let trainingChart = null;
    let trainingInterval = null;

    // 训练表单提交
    $('#training-form').on('submit', function(e) {
        e.preventDefault();

        const modelType = $('#model-type').val();
        const epochs = parseInt($('#epochs').val());
        const batchSize = parseInt($('#batch-size').val());
        const learningRate = $('#learning-rate').val();
        const dataset = $('#dataset').val();
        const usePretrained = $('#use-pretrained').is(':checked');
        const dataAugmentation = $('#data-augmentation').is(':checked');
        const earlyStopping = $('#early-stopping').is(':checked');

        startTraining({
            modelType,
            epochs,
            batchSize,
            learningRate,
            dataset,
            usePretrained,
            dataAugmentation,
            earlyStopping
        });
    });

    // 开始训练
    function startTraining(config) {
        const submitBtn = $('#training-form button[type="submit"]');
        const hideLoading = showLoading(submitBtn[0], '启动中...');

        // 隐藏状态，显示进度
        $('#training-status').addClass('d-none');
        $('#training-progress').removeClass('d-none');

        // 初始化训练曲线
        initTrainingChart();

        // 重置进度
        $('#epoch-progress').text(`0/${config.epochs}`);
        $('#epoch-progress-bar').css('width', '0%');
        $('#current-loss').text('0.000');
        $('#current-accuracy').text('0.0%');

        // 模拟训练过程
        let currentEpoch = 0;
        let loss = 2.5;
        let accuracy = 0;

        trainingInterval = setInterval(() => {
            currentEpoch++;

            // 模拟损失下降和精度提升
            loss = Math.max(0.01, loss - (Math.random() * 0.1 + 0.02));
            accuracy = Math.min(98, accuracy + (Math.random() * 2 + 0.5));

            // 更新进度
            const progress = (currentEpoch / config.epochs) * 100;
            $('#epoch-progress').text(`${currentEpoch}/${config.epochs}`);
            $('#epoch-progress-bar').css('width', progress + '%');
            $('#current-loss').text(loss.toFixed(3));
            $('#current-accuracy').text(accuracy.toFixed(1) + '%');

            // 更新图表
            updateTrainingChart(currentEpoch, loss, accuracy);

            // 训练完成
            if (currentEpoch >= config.epochs) {
                clearInterval(trainingInterval);
                hideLoading();
                showAlert(`模型训练完成！最终精度: ${accuracy.toFixed(1)}%`, 'success');
            }
        }, 1000);

        // 调用API
        window.LicensePlateApp.api.trainModel(config.modelType, config.epochs)
            .then(response => {
                showAlert('训练任务已启动', 'success');
                hideLoading();
            })
            .catch(error => {
                clearInterval(trainingInterval);
                hideLoading();
                $('#training-progress').addClass('d-none');
                $('#training-status').removeClass('d-none');
                showAlert('训练启动失败: ' + error.message, 'danger');
            });
    }

    // 初始化训练曲线图表
    function initTrainingChart() {
        const ctx = document.getElementById('training-chart').getContext('2d');

        if (trainingChart) {
            trainingChart.destroy();
        }

        trainingChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: [],
                datasets: [{
                    label: '损失',
                    data: [],
                    borderColor: '#dc3545',
                    backgroundColor: 'rgba(220, 53, 69, 0.1)',
                    yAxisID: 'y'
                }, {
                    label: '精度 (%)',
                    data: [],
                    borderColor: '#198754',
                    backgroundColor: 'rgba(25, 135, 84, 0.1)',
                    yAxisID: 'y1'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        type: 'linear',
                        display: true,
                        position: 'left',
                        title: {
                            display: true,
                            text: '损失'
                        }
                    },
                    y1: {
                        type: 'linear',
                        display: true,
                        position: 'right',
                        title: {
                            display: true,
                            text: '精度 (%)'
                        },
                        grid: {
                            drawOnChartArea: false,
                        },
                    }
                },
                plugins: {
                    legend: {
                        position: 'top'
                    }
                }
            }
        });
    }

    // 更新训练曲线
    function updateTrainingChart(epoch, loss, accuracy) {
        if (!trainingChart) return;

        trainingChart.data.labels.push(epoch);
        trainingChart.data.datasets[0].data.push(loss);
        trainingChart.data.datasets[1].data.push(accuracy);

        // 保持最近50个点
        if (trainingChart.data.labels.length > 50) {
            trainingChart.data.labels.shift();
            trainingChart.data.datasets[0].data.shift();
            trainingChart.data.datasets[1].data.shift();
        }

        trainingChart.update('none');
    }

    // 停止训练
    $('#stop-training').on('click', function() {
        if (trainingInterval) {
            clearInterval(trainingInterval);
            trainingInterval = null;
            showAlert('训练已停止', 'warning');
        }
    });

    // 暂停训练
    $('#pause-training').on('click', function() {
        if (trainingInterval) {
            clearInterval(trainingInterval);
            trainingInterval = null;
            $(this).html('<i class="fas fa-play"></i> 继续训练');
            showAlert('训练已暂停', 'info');
        } else {
            // 继续训练逻辑
            $(this).html('<i class="fas fa-pause"></i> 暂停训练');
            showAlert('训练已继续', 'info');
        }
    });

    // 刷新模型列表
    $('#refresh-models').on('click', function() {
        const hideLoading = showLoading(this);

        setTimeout(() => {
            hideLoading();
            showAlert('模型列表已刷新', 'success');
        }, 1000);
    });

    // 导出模型
    $('#export-model').on('click', function() {
        showAlert('模型导出功能开发中...', 'info');
    });
});
</script>
{% endblock %}
