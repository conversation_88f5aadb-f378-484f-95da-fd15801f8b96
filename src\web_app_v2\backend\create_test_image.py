#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建测试图片用于验证车牌识别功能
"""

import os
from PIL import Image, ImageDraw, ImageFont
import random

def create_test_license_plate_image():
    """创建一个包含车牌的测试图片"""
    
    # 创建图片
    width, height = 640, 480
    img = Image.new('RGB', (width, height), color='lightblue')
    draw = ImageDraw.Draw(img)
    
    # 绘制背景场景（模拟停车场）
    # 绘制地面
    draw.rectangle([0, height//2, width, height], fill='gray')
    
    # 绘制车辆轮廓
    car_x = width // 4
    car_y = height // 2 - 80
    car_width = width // 2
    car_height = 120
    
    # 车身
    draw.rectangle([car_x, car_y, car_x + car_width, car_y + car_height], fill='darkblue')
    
    # 车窗
    window_margin = 20
    draw.rectangle([car_x + window_margin, car_y + 10, 
                   car_x + car_width - window_margin, car_y + 40], fill='lightcyan')
    
    # 车牌位置
    plate_width = 140
    plate_height = 45
    plate_x = car_x + (car_width - plate_width) // 2
    plate_y = car_y + car_height - 60
    
    # 绘制车牌背景
    draw.rectangle([plate_x, plate_y, plate_x + plate_width, plate_y + plate_height], 
                  fill='white', outline='black', width=2)
    
    # 生成车牌号码
    provinces = ["京", "沪", "津", "渝", "冀", "豫", "云", "辽"]
    letters = ["A", "B", "C", "D", "E", "F", "G", "H"]
    province = random.choice(provinces)
    letter = random.choice(letters)
    numbers = ''.join([str(random.randint(0, 9)) for _ in range(5)])
    plate_number = f"{province}{letter}{numbers}"
    
    # 尝试使用系统字体绘制车牌号码
    try:
        # Windows系统字体
        font_paths = [
            "C:/Windows/Fonts/simhei.ttf",  # 黑体
            "C:/Windows/Fonts/simsun.ttc",  # 宋体
            "C:/Windows/Fonts/arial.ttf",   # Arial
        ]
        
        font = None
        for font_path in font_paths:
            if os.path.exists(font_path):
                try:
                    font = ImageFont.truetype(font_path, 20)
                    break
                except:
                    continue
        
        if font is None:
            font = ImageFont.load_default()
            
    except:
        font = ImageFont.load_default()
    
    # 绘制车牌号码
    text_bbox = draw.textbbox((0, 0), plate_number, font=font)
    text_width = text_bbox[2] - text_bbox[0]
    text_height = text_bbox[3] - text_bbox[1]
    
    text_x = plate_x + (plate_width - text_width) // 2
    text_y = plate_y + (plate_height - text_height) // 2
    
    draw.text((text_x, text_y), plate_number, fill='black', font=font)
    
    # 保存图片
    output_path = "static/test_car_image.jpg"
    img.save(output_path, "JPEG", quality=95)
    
    print(f"✅ 测试图片已创建: {output_path}")
    print(f"🚗 车牌号码: {plate_number}")
    print(f"📍 车牌位置: [{plate_x}, {plate_y}, {plate_x + plate_width}, {plate_y + plate_height}]")
    
    return output_path, plate_number, [plate_x, plate_y, plate_x + plate_width, plate_y + plate_height]

if __name__ == "__main__":
    create_test_license_plate_image()
