#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FastAPI 后端服务启动脚本
确保在 pytorch_env 环境中运行
"""

import os
import sys
import subprocess
from pathlib import Path

def check_environment():
    """检查是否在正确的conda环境中"""
    conda_env = os.environ.get('CONDA_DEFAULT_ENV')
    if conda_env != 'pytorch_env':
        print("=" * 60)
        print("⚠️  警告: 当前不在 pytorch_env 环境中")
        print(f"📍 当前环境: {conda_env or 'base'}")
        print("=" * 60)
        print("🔧 请先激活 pytorch_env 环境:")
        print("   conda activate pytorch_env")
        print("   然后重新运行此脚本")
        print("=" * 60)
        return False
    return True

def install_dependencies():
    """安装必要的依赖包"""
    print("📦 检查并安装依赖包...")
    
    required_packages = [
        "fastapi",
        "uvicorn",
        "python-multipart"
    ]
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"✅ {package} 已安装")
        except ImportError:
            print(f"📥 安装 {package}...")
            try:
                subprocess.run([sys.executable, "-m", "pip", "install", package], 
                             check=True, capture_output=True)
                print(f"✅ {package} 安装成功")
            except subprocess.CalledProcessError as e:
                print(f"❌ {package} 安装失败: {e}")
                return False
    
    return True

def start_fastapi():
    """启动FastAPI服务"""
    print("=" * 60)
    print("🚗 车牌识别系统 FastAPI 后端服务")
    print("=" * 60)
    print(f"🐍 Python 环境: {sys.executable}")
    print(f"📁 工作目录: {os.getcwd()}")
    print(f"🌍 Conda 环境: {os.environ.get('CONDA_DEFAULT_ENV', 'unknown')}")
    print("=" * 60)
    
    # 检查环境
    if not check_environment():
        return False
    
    # 安装依赖
    if not install_dependencies():
        return False
    
    print("🚀 启动 FastAPI 服务...")
    
    try:
        # 导入并启动FastAPI应用
        from main import app
        import uvicorn
        
        print("✅ 模块导入成功")
        print("=" * 60)
        print("🌐 服务地址:")
        print("   - API 服务: http://127.0.0.1:8000")
        print("   - API 文档: http://127.0.0.1:8000/api/docs")
        print("   - 交互文档: http://127.0.0.1:8000/api/redoc")
        print("=" * 60)
        print("💡 按 Ctrl+C 停止服务")
        print("=" * 60)
        
        # 启动服务
        uvicorn.run(
            "main:app",
            host="127.0.0.1",
            port=8000,
            reload=True,
            log_level="info"
        )
        
    except ImportError as e:
        print(f"❌ 模块导入失败: {e}")
        print("💡 请确保项目依赖已正确安装")
        return False
    except Exception as e:
        print(f"❌ 服务启动失败: {e}")
        return False
    
    return True

def main():
    """主函数"""
    # 切换到脚本所在目录
    script_dir = Path(__file__).parent.resolve()
    os.chdir(script_dir)
    
    try:
        start_fastapi()
    except KeyboardInterrupt:
        print("\n🛑 服务已停止")
    except Exception as e:
        print(f"\n❌ 发生错误: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
