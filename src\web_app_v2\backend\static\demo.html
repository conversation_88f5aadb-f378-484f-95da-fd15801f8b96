<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>车牌识别系统 - 演示页面</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .content {
            padding: 40px;
        }
        
        .section {
            margin-bottom: 40px;
            padding: 30px;
            background: #f8f9fa;
            border-radius: 10px;
            border-left: 5px solid #4facfe;
        }
        
        .section h2 {
            color: #333;
            margin-bottom: 20px;
            font-size: 1.8em;
        }
        
        .upload-area {
            border: 3px dashed #4facfe;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            background: white;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .upload-area:hover {
            border-color: #00f2fe;
            background: #f0f8ff;
        }
        
        .upload-area input {
            display: none;
        }
        
        .upload-icon {
            font-size: 3em;
            color: #4facfe;
            margin-bottom: 20px;
        }
        
        .btn {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            font-size: 1.1em;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(79, 172, 254, 0.3);
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #4facfe;
            margin-bottom: 10px;
        }
        
        .stat-label {
            color: #666;
            font-size: 0.9em;
        }
        
        .result-area {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
            min-height: 200px;
            border: 1px solid #e0e0e0;
        }

        .image-preview {
            max-width: 100%;
            max-height: 400px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin: 15px 0;
        }

        .image-container {
            text-align: center;
            margin: 20px 0;
        }

        .image-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }

        .image-card {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 10px;
            border: 1px solid #e0e0e0;
        }

        .image-card h4 {
            color: #333;
            margin-bottom: 10px;
            text-align: center;
        }

        .bbox-overlay {
            position: relative;
            display: inline-block;
        }

        .bbox-rect {
            position: absolute;
            border: 3px solid #ff4757;
            background: rgba(255, 71, 87, 0.1);
            pointer-events: none;
        }

        .plate-label {
            position: absolute;
            background: #ff4757;
            color: white;
            padding: 2px 8px;
            border-radius: 3px;
            font-size: 12px;
            font-weight: bold;
            top: -25px;
            left: 0;
        }
        
        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }
        
        .success {
            color: #28a745;
            background: #d4edda;
            border: 1px solid #c3e6cb;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        
        .error {
            color: #dc3545;
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚗 车牌识别系统</h1>
            <p>基于深度学习的智能车牌识别 - 优化版演示</p>
        </div>
        
        <div class="content">
            <!-- 图片上传检测区域 -->
            <div class="section">
                <h2>📸 车牌检测</h2>

                <!-- 快速测试按钮 -->
                <div style="margin-bottom: 20px; text-align: center;">
                    <button class="btn" onclick="useTestImage()">🚗 使用测试图片</button>
                    <span style="margin: 0 10px; color: #666;">或</span>
                    <button class="btn" onclick="document.getElementById('fileInput').click()">📁 上传自己的图片</button>
                </div>

                <div class="upload-area" onclick="document.getElementById('fileInput').click()"
                     ondrop="handleDrop(event)" ondragover="handleDragOver(event)" ondragleave="handleDragLeave(event)">
                    <div class="upload-icon">📁</div>
                    <p>点击或拖拽图片到此处进行车牌识别</p>
                    <p style="color: #666; font-size: 0.9em;">支持 JPG, PNG, GIF 格式</p>
                    <input type="file" id="fileInput" accept="image/*" onchange="uploadImage()">
                </div>

                <!-- 图片预览区域 -->
                <div id="imagePreview" class="image-container" style="display: none;">
                    <h4>📷 待检测的图片</h4>
                    <img id="previewImage" class="image-preview" alt="预览图片">
                </div>
                <div id="uploadResult" class="result-area" style="display: none;">
                    <div id="resultContent"></div>
                </div>
            </div>
            
            <!-- 系统统计区域 -->
            <div class="section">
                <h2>📊 系统统计</h2>
                <button class="btn" onclick="loadStats()">刷新统计数据</button>
                <div id="statsGrid" class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-number" id="totalDetections">-</div>
                        <div class="stat-label">总检测次数</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="successRate">-</div>
                        <div class="stat-label">成功率 (%)</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="avgTime">-</div>
                        <div class="stat-label">平均处理时间 (秒)</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="runtime">-</div>
                        <div class="stat-label">运行时间 (小时)</div>
                    </div>
                </div>
            </div>
            
            <!-- API测试区域 -->
            <div class="section">
                <h2>🔧 API 测试</h2>
                <button class="btn" onclick="testHealth()">健康检查</button>
                <button class="btn" onclick="testDataCollection()">数据采集测试</button>
                <button class="btn" onclick="testModelTraining()">模型训练测试</button>
                <div id="apiResult" class="result-area" style="display: none;">
                    <div id="apiContent"></div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 页面加载时自动加载统计数据
        window.onload = function() {
            loadStats();
        };

        // 使用测试图片
        async function useTestImage() {
            const testImageUrl = '/static/test_car_image.jpg';

            // 显示图片预览
            const previewDiv = document.getElementById('imagePreview');
            const previewImg = document.getElementById('previewImage');

            previewImg.src = testImageUrl;
            previewDiv.style.display = 'block';

            // 模拟文件上传
            try {
                const response = await fetch(testImageUrl);
                const blob = await response.blob();
                const file = new File([blob], 'test_car_image.jpg', { type: 'image/jpeg' });

                // 创建FormData并上传
                const formData = new FormData();
                formData.append('file', file);

                const resultDiv = document.getElementById('uploadResult');
                const contentDiv = document.getElementById('resultContent');

                resultDiv.style.display = 'block';
                contentDiv.innerHTML = '<div class="loading">🔄 正在处理测试图片，请稍候...</div>';

                const uploadResponse = await fetch('/api/v1/upload', {
                    method: 'POST',
                    body: formData
                });

                const result = await uploadResponse.json();

                if (result.success) {
                    const plateResult = result.results[0];
                    contentDiv.innerHTML = `
                        <div class="success">
                            <h3>✅ 检测成功！</h3>
                            <div class="image-grid">
                                <div class="image-card">
                                    <h4>原始图片</h4>
                                    <div class="bbox-overlay">
                                        <img src="${testImageUrl}"
                                             class="image-preview" id="originalImage" onload="drawBoundingBox()">
                                        <div id="bboxOverlay"></div>
                                    </div>
                                </div>
                                <div class="image-card">
                                    <h4>检测结果</h4>
                                    <div style="background: white; padding: 20px; border-radius: 8px; text-align: left;">
                                        <p><strong>🚗 车牌号码:</strong> <span style="color: #4facfe; font-size: 1.2em; font-weight: bold;">${plateResult.license_plate}</span></p>
                                        <p><strong>📊 置信度:</strong> ${(plateResult.confidence * 100).toFixed(1)}%</p>
                                        <p><strong>🏷️ 车牌类型:</strong> ${plateResult.type}</p>
                                        <p><strong>⏱️ 处理时间:</strong> ${result.processing_time.toFixed(3)} 秒</p>
                                        <p><strong>📍 位置坐标:</strong> [${plateResult.bbox.join(', ')}]</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;

                    // 存储边界框信息用于绘制
                    window.currentBbox = plateResult.bbox;
                    window.currentPlate = plateResult.license_plate;

                    loadStats(); // 刷新统计数据
                } else {
                    contentDiv.innerHTML = `<div class="error">❌ 检测失败: ${result.message}</div>`;
                }
            } catch (error) {
                const contentDiv = document.getElementById('resultContent');
                contentDiv.innerHTML = `<div class="error">❌ 测试图片加载失败: ${error.message}</div>`;
            }
        };

        // 处理拖拽事件
        function handleDragOver(event) {
            event.preventDefault();
            event.currentTarget.style.borderColor = '#00f2fe';
            event.currentTarget.style.background = '#f0f8ff';
        }

        function handleDragLeave(event) {
            event.preventDefault();
            event.currentTarget.style.borderColor = '#4facfe';
            event.currentTarget.style.background = 'white';
        }

        function handleDrop(event) {
            event.preventDefault();
            event.currentTarget.style.borderColor = '#4facfe';
            event.currentTarget.style.background = 'white';

            const files = event.dataTransfer.files;
            if (files.length > 0) {
                const fileInput = document.getElementById('fileInput');
                fileInput.files = files;
                uploadImage();
            }
        }

        // 预览图片
        function previewImage(file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                const previewDiv = document.getElementById('imagePreview');
                const previewImg = document.getElementById('previewImage');

                previewImg.src = e.target.result;
                previewDiv.style.display = 'block';
            };
            reader.readAsDataURL(file);
        }

        // 上传图片进行检测
        async function uploadImage() {
            const fileInput = document.getElementById('fileInput');
            const file = fileInput.files[0];

            if (!file) return;

            // 显示图片预览
            previewImage(file);

            const resultDiv = document.getElementById('uploadResult');
            const contentDiv = document.getElementById('resultContent');

            resultDiv.style.display = 'block';
            contentDiv.innerHTML = '<div class="loading">🔄 正在处理图片，请稍候...</div>';

            const formData = new FormData();
            formData.append('file', file);

            try {
                const response = await fetch('/api/v1/upload', {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();

                if (result.success) {
                    const plateResult = result.results[0];
                    contentDiv.innerHTML = `
                        <div class="success">
                            <h3>✅ 检测成功！</h3>
                            <div class="image-grid">
                                <div class="image-card">
                                    <h4>原始图片</h4>
                                    <div class="bbox-overlay">
                                        <img src="${document.getElementById('previewImage').src}"
                                             class="image-preview" id="originalImage" onload="drawBoundingBox()">
                                        <div id="bboxOverlay"></div>
                                    </div>
                                </div>
                                <div class="image-card">
                                    <h4>检测结果</h4>
                                    <div style="background: white; padding: 20px; border-radius: 8px; text-align: left;">
                                        <p><strong>🚗 车牌号码:</strong> <span style="color: #4facfe; font-size: 1.2em; font-weight: bold;">${plateResult.license_plate}</span></p>
                                        <p><strong>📊 置信度:</strong> ${(plateResult.confidence * 100).toFixed(1)}%</p>
                                        <p><strong>🏷️ 车牌类型:</strong> ${plateResult.type}</p>
                                        <p><strong>⏱️ 处理时间:</strong> ${result.processing_time.toFixed(3)} 秒</p>
                                        <p><strong>📍 位置坐标:</strong> [${plateResult.bbox.join(', ')}]</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;

                    // 存储边界框信息用于绘制
                    window.currentBbox = plateResult.bbox;
                    window.currentPlate = plateResult.license_plate;

                    loadStats(); // 刷新统计数据
                } else {
                    contentDiv.innerHTML = `<div class="error">❌ 检测失败: ${result.message}</div>`;
                }
            } catch (error) {
                contentDiv.innerHTML = `<div class="error">❌ 网络错误: ${error.message}</div>`;
            }
        }

        // 绘制边界框
        function drawBoundingBox() {
            if (!window.currentBbox) return;

            const img = document.getElementById('originalImage');
            const overlay = document.getElementById('bboxOverlay');

            if (!img || !overlay) return;

            // 清除之前的边界框
            overlay.innerHTML = '';

            // 获取图片的显示尺寸
            const imgRect = img.getBoundingClientRect();
            const imgNaturalWidth = img.naturalWidth;
            const imgNaturalHeight = img.naturalHeight;

            // 计算缩放比例
            const scaleX = img.offsetWidth / imgNaturalWidth;
            const scaleY = img.offsetHeight / imgNaturalHeight;

            // 计算边界框在显示图片上的位置
            const [x1, y1, x2, y2] = window.currentBbox;
            const displayX = x1 * scaleX;
            const displayY = y1 * scaleY;
            const displayWidth = (x2 - x1) * scaleX;
            const displayHeight = (y2 - y1) * scaleY;

            // 创建边界框元素
            const bboxRect = document.createElement('div');
            bboxRect.className = 'bbox-rect';
            bboxRect.style.left = displayX + 'px';
            bboxRect.style.top = displayY + 'px';
            bboxRect.style.width = displayWidth + 'px';
            bboxRect.style.height = displayHeight + 'px';

            // 创建标签
            const label = document.createElement('div');
            label.className = 'plate-label';
            label.textContent = window.currentPlate;
            bboxRect.appendChild(label);

            overlay.appendChild(bboxRect);
        }

        // 加载统计数据
        async function loadStats() {
            try {
                const response = await fetch('/api/v1/stats');
                const stats = await response.json();
                
                document.getElementById('totalDetections').textContent = stats.total_detections;
                document.getElementById('successRate').textContent = stats.success_rate.toFixed(1);
                document.getElementById('avgTime').textContent = stats.avg_processing_time.toFixed(2);
                document.getElementById('runtime').textContent = stats.runtime_hours.toFixed(2);
            } catch (error) {
                console.error('加载统计数据失败:', error);
            }
        }

        // 健康检查
        async function testHealth() {
            showApiResult('🔄 正在检查服务健康状态...');
            
            try {
                const response = await fetch('/api/health');
                const result = await response.json();
                
                showApiResult(`
                    <div class="success">
                        <h3>✅ 健康检查结果</h3>
                        <p><strong>状态:</strong> ${result.status}</p>
                        <p><strong>消息:</strong> ${result.message}</p>
                        <p><strong>模型加载:</strong> ${result.models_loaded ? '是' : '否'}</p>
                        <p><strong>时间戳:</strong> ${result.timestamp}</p>
                    </div>
                `);
            } catch (error) {
                showApiResult(`<div class="error">❌ 健康检查失败: ${error.message}</div>`);
            }
        }

        // 数据采集测试
        async function testDataCollection() {
            showApiResult('🔄 正在测试数据采集功能...');
            
            try {
                const response = await fetch('/api/v1/data/collect', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        keywords: ['车牌', '汽车'],
                        max_images: 50,
                        source: 'baidu'
                    })
                });
                
                const result = await response.json();
                
                showApiResult(`
                    <div class="success">
                        <h3>✅ 数据采集测试成功</h3>
                        <p><strong>消息:</strong> ${result.message}</p>
                        <p><strong>任务ID:</strong> ${result.task_id}</p>
                    </div>
                `);
            } catch (error) {
                showApiResult(`<div class="error">❌ 数据采集测试失败: ${error.message}</div>`);
            }
        }

        // 模型训练测试
        async function testModelTraining() {
            showApiResult('🔄 正在测试模型训练功能...');
            
            try {
                const response = await fetch('/api/v1/model/train', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        model_type: 'YOLO',
                        epochs: 100,
                        batch_size: 32,
                        learning_rate: 0.001
                    })
                });
                
                const result = await response.json();
                
                showApiResult(`
                    <div class="success">
                        <h3>✅ 模型训练测试成功</h3>
                        <p><strong>消息:</strong> ${result.message}</p>
                        <p><strong>任务ID:</strong> ${result.task_id}</p>
                    </div>
                `);
            } catch (error) {
                showApiResult(`<div class="error">❌ 模型训练测试失败: ${error.message}</div>`);
            }
        }

        // 显示API测试结果
        function showApiResult(content) {
            const resultDiv = document.getElementById('apiResult');
            const contentDiv = document.getElementById('apiContent');
            
            resultDiv.style.display = 'block';
            contentDiv.innerHTML = content;
        }
    </script>
</body>
</html>
