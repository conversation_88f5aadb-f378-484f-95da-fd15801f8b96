#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的FastAPI测试服务
"""

import os
import sys
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import uvicorn

# 创建FastAPI应用
app = FastAPI(
    title="车牌识别系统 API",
    description="基于深度学习的车牌识别系统后端服务",
    version="2.0.0"
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
async def root():
    """根路径"""
    return {"message": "车牌识别系统 API 服务正在运行", "version": "2.0.0"}

@app.get("/api/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "message": "服务运行正常",
        "timestamp": "2025-01-30T12:00:00Z",
        "models_loaded": True
    }

@app.get("/api/v1/stats")
async def get_stats():
    """获取系统统计信息"""
    return {
        "total_detections": 1250,
        "successful_detections": 1180,
        "success_rate": 94.4,
        "avg_processing_time": 0.8,
        "runtime_hours": 48.5,
        "start_time": "2025-01-28T12:00:00Z"
    }

if __name__ == "__main__":
    print("=" * 60)
    print("🚗 车牌识别系统 FastAPI 测试服务")
    print("=" * 60)
    print(f"🐍 Python: {sys.executable}")
    print(f"🌍 环境: {os.environ.get('CONDA_DEFAULT_ENV', 'unknown')}")
    print("📍 API 服务地址: http://127.0.0.1:8000")
    print("📖 API 文档地址: http://127.0.0.1:8000/docs")
    print("=" * 60)
    print("💡 按 Ctrl+C 停止服务")
    print("=" * 60)
    
    try:
        uvicorn.run(
            "test_main:app",
            host="127.0.0.1",
            port=8000,
            reload=True,
            log_level="info"
        )
    except KeyboardInterrupt:
        print("\n🛑 服务已停止")
    except Exception as e:
        print(f"❌ 服务启动失败: {e}")
