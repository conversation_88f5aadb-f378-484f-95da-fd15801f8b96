{"name": "license-plate-recognition-frontend", "private": true, "version": "2.0.0", "type": "module", "description": "车牌识别系统前端应用 - 基于React + TypeScript + Tailwind CSS", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "type-check": "tsc --noEmit"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.8.1", "axios": "^1.3.4", "react-hook-form": "^7.43.5", "framer-motion": "^10.8.0", "lucide-react": "^0.323.0", "clsx": "^1.2.1", "react-dropzone": "^14.2.3", "recharts": "^2.5.0", "react-hot-toast": "^2.4.0"}, "devDependencies": {"@types/react": "^18.0.28", "@types/react-dom": "^18.0.11", "@typescript-eslint/eslint-plugin": "^5.57.1", "@typescript-eslint/parser": "^5.57.1", "@vitejs/plugin-react": "^4.0.0", "autoprefixer": "^10.4.14", "eslint": "^8.38.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.3.4", "postcss": "^8.4.21", "tailwindcss": "^3.2.7", "typescript": "^5.0.2", "vite": "^4.3.2"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}}