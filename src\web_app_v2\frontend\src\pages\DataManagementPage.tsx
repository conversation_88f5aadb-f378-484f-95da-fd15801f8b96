import React, { useState } from 'react';
import { motion } from 'framer-motion';
import {
  Database,
  Download,
  Search,
  Plus,
  Trash2,
  RefreshCw,
  Globe,
  Image,
  Tag,
  Calendar,
  AlertCircle,
  CheckCircle,
  Loader2,
} from 'lucide-react';
import { ApiService } from '../services/api';
import { DataCollectionRequest, DataCollectionResponse } from '../types';
import toast from 'react-hot-toast';

const DataManagementPage: React.FC = () => {
  const [keywords, setKeywords] = useState<string[]>(['车牌', '汽车牌照']);
  const [newKeyword, setNewKeyword] = useState('');
  const [maxImages, setMaxImages] = useState(100);
  const [isCollecting, setIsCollecting] = useState(false);
  const [collectionResult, setCollectionResult] = useState<DataCollectionResponse | null>(null);

  // 添加关键词
  const addKeyword = () => {
    if (newKeyword.trim() && !keywords.includes(newKeyword.trim())) {
      setKeywords([...keywords, newKeyword.trim()]);
      setNewKeyword('');
    }
  };

  // 删除关键词
  const removeKeyword = (index: number) => {
    setKeywords(keywords.filter((_, i) => i !== index));
  };

  // 开始数据采集
  const startDataCollection = async () => {
    if (keywords.length === 0) {
      toast.error('请至少添加一个关键词');
      return;
    }

    setIsCollecting(true);
    setCollectionResult(null);

    try {
      const request: DataCollectionRequest = {
        keywords,
        max_images: maxImages,
      };

      const result = await ApiService.collectData(request);
      setCollectionResult(result);

      if (result.success) {
        toast.success('数据采集任务已启动');
      } else {
        toast.error(result.message || '数据采集启动失败');
      }
    } catch (error) {
      console.error('数据采集失败:', error);
      toast.error(error instanceof Error ? error.message : '数据采集失败');
    } finally {
      setIsCollecting(false);
    }
  };

  // 数据统计卡片
  const dataStats = [
    {
      title: '训练数据',
      value: '2,456',
      icon: Database,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50 dark:bg-blue-900/20',
      description: '已标注的训练图片',
    },
    {
      title: '验证数据',
      value: '612',
      icon: CheckCircle,
      color: 'text-green-600',
      bgColor: 'bg-green-50 dark:bg-green-900/20',
      description: '用于模型验证的图片',
    },
    {
      title: '测试数据',
      value: '306',
      icon: Search,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50 dark:bg-purple-900/20',
      description: '用于模型测试的图片',
    },
    {
      title: '原始数据',
      value: '5,234',
      icon: Image,
      color: 'text-orange-600',
      bgColor: 'bg-orange-50 dark:bg-orange-900/20',
      description: '未处理的原始图片',
    },
  ];

  return (
    <div className="max-w-6xl mx-auto space-y-8">
      {/* 页面标题 */}
      <motion.div
        className="text-center"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
          数据管理
        </h1>
        <p className="text-lg text-gray-600 dark:text-gray-400">
          管理训练数据集和采集新的车牌图片数据
        </p>
      </motion.div>

      {/* 数据统计 */}
      <motion.section
        className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2, duration: 0.6 }}
      >
        {dataStats.map((stat, index) => {
          const Icon = stat.icon;
          return (
            <motion.div
              key={stat.title}
              className={`card p-6 ${stat.bgColor} border-0`}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 + index * 0.1, duration: 0.4 }}
              whileHover={{ scale: 1.02 }}
            >
              <div className="flex items-center justify-between mb-4">
                <Icon className={`w-8 h-8 ${stat.color}`} />
              </div>
              <div className="text-2xl font-bold text-gray-900 dark:text-white mb-1">
                {stat.value}
              </div>
              <div className="text-sm font-medium text-gray-900 dark:text-white mb-1">
                {stat.title}
              </div>
              <div className="text-xs text-gray-600 dark:text-gray-400">
                {stat.description}
              </div>
            </motion.div>
          );
        })}
      </motion.section>

      {/* 数据采集 */}
      <motion.section
        className="card p-6"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.4, duration: 0.6 }}
      >
        <div className="flex items-center space-x-3 mb-6">
          <div className="p-2 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
            <Globe className="w-6 h-6 text-blue-600 dark:text-blue-400" />
          </div>
          <div>
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
              在线数据采集
            </h2>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              从网络爬取车牌图片数据用于模型训练
            </p>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* 采集配置 */}
          <div className="space-y-6">
            <div>
              <label className="label">搜索关键词</label>
              <div className="space-y-3">
                <div className="flex space-x-2">
                  <input
                    type="text"
                    value={newKeyword}
                    onChange={(e) => setNewKeyword(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && addKeyword()}
                    placeholder="输入关键词..."
                    className="input flex-1"
                  />
                  <button
                    onClick={addKeyword}
                    className="btn btn-secondary"
                    disabled={!newKeyword.trim()}
                  >
                    <Plus className="w-4 h-4" />
                  </button>
                </div>
                
                <div className="flex flex-wrap gap-2">
                  {keywords.map((keyword, index) => (
                    <motion.div
                      key={index}
                      className="flex items-center space-x-2 bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300 px-3 py-1 rounded-full text-sm"
                      initial={{ opacity: 0, scale: 0.8 }}
                      animate={{ opacity: 1, scale: 1 }}
                      transition={{ duration: 0.2 }}
                    >
                      <Tag className="w-3 h-3" />
                      <span>{keyword}</span>
                      <button
                        onClick={() => removeKeyword(index)}
                        className="text-blue-500 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-200"
                      >
                        <Trash2 className="w-3 h-3" />
                      </button>
                    </motion.div>
                  ))}
                </div>
              </div>
            </div>

            <div>
              <label className="label">最大图片数量</label>
              <input
                type="number"
                value={maxImages}
                onChange={(e) => setMaxImages(parseInt(e.target.value) || 100)}
                min="10"
                max="1000"
                className="input"
              />
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                建议每次采集 50-200 张图片
              </p>
            </div>

            <button
              onClick={startDataCollection}
              disabled={isCollecting || keywords.length === 0}
              className="btn btn-primary w-full"
            >
              {isCollecting ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  采集中...
                </>
              ) : (
                <>
                  <Download className="w-4 h-4 mr-2" />
                  开始采集
                </>
              )}
            </button>
          </div>

          {/* 采集状态 */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              采集状态
            </h3>
            
            {collectionResult ? (
              <motion.div
                className={`p-4 rounded-lg border-l-4 ${
                  collectionResult.success
                    ? 'border-l-green-500 bg-green-50 dark:bg-green-900/20'
                    : 'border-l-red-500 bg-red-50 dark:bg-red-900/20'
                }`}
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3 }}
              >
                <div className="flex items-center space-x-2 mb-2">
                  {collectionResult.success ? (
                    <CheckCircle className="w-5 h-5 text-green-600 dark:text-green-400" />
                  ) : (
                    <AlertCircle className="w-5 h-5 text-red-600 dark:text-red-400" />
                  )}
                  <span className="font-medium text-gray-900 dark:text-white">
                    {collectionResult.success ? '采集任务已启动' : '采集失败'}
                  </span>
                </div>
                <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                  {collectionResult.message}
                </p>
                {collectionResult.task_id && (
                  <p className="text-xs text-gray-500 dark:text-gray-400 font-mono">
                    任务ID: {collectionResult.task_id}
                  </p>
                )}
              </motion.div>
            ) : (
              <div className="p-4 bg-gray-50 dark:bg-gray-800 rounded-lg text-center">
                <Database className="w-12 h-12 text-gray-400 mx-auto mb-2" />
                <p className="text-gray-500 dark:text-gray-400">
                  暂无采集任务
                </p>
              </div>
            )}

            {/* 采集历史 */}
            <div className="space-y-3">
              <h4 className="font-medium text-gray-900 dark:text-white">
                最近采集记录
              </h4>
              <div className="space-y-2">
                {[
                  { date: '2025-01-20', keywords: ['车牌', '汽车'], count: 156, status: 'success' },
                  { date: '2025-01-19', keywords: ['license plate'], count: 89, status: 'success' },
                  { date: '2025-01-18', keywords: ['车牌识别'], count: 203, status: 'success' },
                ].map((record, index) => (
                  <motion.div
                    key={index}
                    className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg"
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1, duration: 0.3 }}
                  >
                    <div className="flex items-center space-x-3">
                      <Calendar className="w-4 h-4 text-gray-400" />
                      <div>
                        <div className="text-sm font-medium text-gray-900 dark:text-white">
                          {record.keywords.join(', ')}
                        </div>
                        <div className="text-xs text-gray-500 dark:text-gray-400">
                          {record.date}
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className="text-sm text-gray-600 dark:text-gray-400">
                        {record.count} 张
                      </span>
                      <CheckCircle className="w-4 h-4 text-green-500" />
                    </div>
                  </motion.div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </motion.section>

      {/* 数据管理操作 */}
      <motion.section
        className="grid grid-cols-1 md:grid-cols-3 gap-6"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.6, duration: 0.6 }}
      >
        <div className="card p-6 text-center">
          <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-full w-16 h-16 mx-auto mb-4">
            <Database className="w-8 h-8 text-blue-600 dark:text-blue-400" />
          </div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
            数据预处理
          </h3>
          <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
            对采集的图片进行清洗、标注和格式化
          </p>
          <button className="btn btn-secondary w-full">
            开始预处理
          </button>
        </div>

        <div className="card p-6 text-center">
          <div className="p-4 bg-green-50 dark:bg-green-900/20 rounded-full w-16 h-16 mx-auto mb-4">
            <RefreshCw className="w-8 h-8 text-green-600 dark:text-green-400" />
          </div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
            数据增强
          </h3>
          <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
            通过旋转、缩放等方式增加训练数据
          </p>
          <button className="btn btn-secondary w-full">
            数据增强
          </button>
        </div>

        <div className="card p-6 text-center">
          <div className="p-4 bg-purple-50 dark:bg-purple-900/20 rounded-full w-16 h-16 mx-auto mb-4">
            <Download className="w-8 h-8 text-purple-600 dark:text-purple-400" />
          </div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
            导出数据
          </h3>
          <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
            导出处理好的数据集用于训练
          </p>
          <button className="btn btn-secondary w-full">
            导出数据集
          </button>
        </div>
      </motion.section>
    </div>
  );
};

export default DataManagementPage;
