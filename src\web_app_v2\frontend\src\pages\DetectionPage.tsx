import React, { useState, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useDropzone } from 'react-dropzone';
import {
  Upload,
  Camera,
  FileImage,
  X,
  CheckCircle,
  AlertCircle,
  Loader2,
  Download,
  Eye,
  Clock,
  Target,
} from 'lucide-react';
import { ApiService } from '../services/api';
import { DetectionResult, LicensePlateDetection, UploadProgress } from '../types';
import toast from 'react-hot-toast';

const DetectionPage: React.FC = () => {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState<UploadProgress | null>(null);
  const [detectionResult, setDetectionResult] = useState<DetectionResult | null>(null);

  // 文件拖拽处理
  const onDrop = useCallback((acceptedFiles: File[]) => {
    const file = acceptedFiles[0];
    if (file) {
      setSelectedFile(file);
      setPreviewUrl(URL.createObjectURL(file));
      setDetectionResult(null);
    }
  }, []);

  const { getRootProps, getInputProps, isDragActive, isDragReject } = useDropzone({
    onDrop,
    accept: {
      'image/*': ['.jpeg', '.jpg', '.png', '.bmp', '.webp'],
    },
    maxSize: 16 * 1024 * 1024, // 16MB
    multiple: false,
  });

  // 清除选择的文件
  const clearFile = () => {
    setSelectedFile(null);
    if (previewUrl) {
      URL.revokeObjectURL(previewUrl);
      setPreviewUrl(null);
    }
    setDetectionResult(null);
    setUploadProgress(null);
  };

  // 上传并检测
  const handleUpload = async () => {
    if (!selectedFile) return;

    setIsUploading(true);
    setUploadProgress(null);

    try {
      const result = await ApiService.uploadAndDetect(
        selectedFile,
        (progress) => {
          setUploadProgress(progress);
        }
      );

      setDetectionResult(result);
      
      if (result.success && result.results && result.results.length > 0) {
        toast.success(`检测完成！发现 ${result.results.length} 个车牌`);
      } else {
        toast.warning('未检测到车牌');
      }
    } catch (error) {
      console.error('检测失败:', error);
      toast.error(error instanceof Error ? error.message : '检测失败');
    } finally {
      setIsUploading(false);
      setUploadProgress(null);
    }
  };

  // 渲染检测结果
  const renderDetectionResults = () => {
    if (!detectionResult || !detectionResult.results) return null;

    return (
      <motion.div
        className="space-y-4"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.4 }}
      >
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            检测结果
          </h3>
          <div className="flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400">
            <div className="flex items-center space-x-1">
              <Clock className="w-4 h-4" />
              <span>{detectionResult.processing_time?.toFixed(2)}s</span>
            </div>
            <div className="flex items-center space-x-1">
              <Target className="w-4 h-4" />
              <span>{detectionResult.results.length} 个车牌</span>
            </div>
          </div>
        </div>

        <div className="grid gap-4">
          {detectionResult.results.map((result, index) => (
            <motion.div
              key={index}
              className="card p-4 border-l-4 border-l-blue-500"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: index * 0.1, duration: 0.3 }}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="p-2 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                    <Camera className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                  </div>
                  <div>
                    <div className="text-lg font-mono font-bold text-gray-900 dark:text-white">
                      {result.license_plate}
                    </div>
                    <div className="text-sm text-gray-500 dark:text-gray-400">
                      {result.type} | 置信度: {(result.confidence * 100).toFixed(1)}%
                    </div>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <div className={`badge ${
                    result.confidence > 0.9 ? 'badge-success' :
                    result.confidence > 0.7 ? 'badge-warning' : 'badge-danger'
                  }`}>
                    {result.confidence > 0.9 ? '高' :
                     result.confidence > 0.7 ? '中' : '低'}置信度
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        {detectionResult.image_url && (
          <div className="flex justify-center space-x-4">
            <button
              onClick={() => window.open(detectionResult.image_url, '_blank')}
              className="btn btn-secondary"
            >
              <Eye className="w-4 h-4 mr-2" />
              查看结果图片
            </button>
          </div>
        )}
      </motion.div>
    );
  };

  return (
    <div className="max-w-4xl mx-auto space-y-8">
      {/* 页面标题 */}
      <motion.div
        className="text-center"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
          车牌检测识别
        </h1>
        <p className="text-lg text-gray-600 dark:text-gray-400">
          上传图片进行智能车牌检测和识别
        </p>
      </motion.div>

      {/* 文件上传区域 */}
      <motion.div
        className="card p-8"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2, duration: 0.6 }}
      >
        {!selectedFile ? (
          <div
            {...getRootProps()}
            className={`dropzone cursor-pointer ${
              isDragActive ? 'dropzone-active' : ''
            } ${isDragReject ? 'dropzone-reject' : ''}`}
          >
            <input {...getInputProps()} />
            <div className="text-center">
              <motion.div
                className="mx-auto mb-4"
                animate={{ y: isDragActive ? -10 : 0 }}
                transition={{ type: 'spring', stiffness: 300, damping: 20 }}
              >
                <Upload className="w-16 h-16 text-gray-400 mx-auto" />
              </motion.div>
              <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                {isDragActive ? '释放文件到此处' : '拖拽图片到此处或点击选择'}
              </h3>
              <p className="text-gray-500 dark:text-gray-400 mb-4">
                支持 JPG、PNG、BMP、WEBP 格式，最大 16MB
              </p>
              <div className="flex items-center justify-center space-x-4 text-sm text-gray-400">
                <div className="flex items-center space-x-1">
                  <FileImage className="w-4 h-4" />
                  <span>图片文件</span>
                </div>
                <div className="flex items-center space-x-1">
                  <Camera className="w-4 h-4" />
                  <span>车牌识别</span>
                </div>
              </div>
            </div>
          </div>
        ) : (
          <div className="space-y-6">
            {/* 文件预览 */}
            <div className="relative">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                  图片预览
                </h3>
                <button
                  onClick={clearFile}
                  className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors duration-200"
                  aria-label="清除文件"
                >
                  <X className="w-5 h-5" />
                </button>
              </div>
              
              {previewUrl && (
                <motion.div
                  className="relative rounded-lg overflow-hidden bg-gray-100 dark:bg-gray-800"
                  initial={{ opacity: 0, scale: 0.95 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.3 }}
                >
                  <img
                    src={previewUrl}
                    alt="预览图片"
                    className="w-full h-auto max-h-96 object-contain"
                  />
                </motion.div>
              )}
            </div>

            {/* 文件信息 */}
            <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
              <div className="flex items-center space-x-3">
                <FileImage className="w-8 h-8 text-blue-600 dark:text-blue-400" />
                <div>
                  <div className="font-medium text-gray-900 dark:text-white">
                    {selectedFile.name}
                  </div>
                  <div className="text-sm text-gray-500 dark:text-gray-400">
                    {(selectedFile.size / 1024 / 1024).toFixed(2)} MB
                  </div>
                </div>
              </div>
              
              {/* 上传按钮 */}
              <button
                onClick={handleUpload}
                disabled={isUploading}
                className="btn btn-primary"
              >
                {isUploading ? (
                  <>
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    检测中...
                  </>
                ) : (
                  <>
                    <Camera className="w-4 h-4 mr-2" />
                    开始检测
                  </>
                )}
              </button>
            </div>

            {/* 上传进度 */}
            {uploadProgress && (
              <motion.div
                className="space-y-2"
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                transition={{ duration: 0.3 }}
              >
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-600 dark:text-gray-400">上传进度</span>
                  <span className="font-medium text-gray-900 dark:text-white">
                    {uploadProgress.percentage}%
                  </span>
                </div>
                <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                  <motion.div
                    className="bg-blue-600 h-2 rounded-full"
                    initial={{ width: 0 }}
                    animate={{ width: `${uploadProgress.percentage}%` }}
                    transition={{ duration: 0.3 }}
                  />
                </div>
              </motion.div>
            )}
          </div>
        )}
      </motion.div>

      {/* 检测结果 */}
      <AnimatePresence>
        {detectionResult && (
          <motion.div
            className="card p-6"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.4 }}
          >
            {detectionResult.success ? (
              <div className="space-y-4">
                <div className="flex items-center space-x-2 text-green-600 dark:text-green-400">
                  <CheckCircle className="w-5 h-5" />
                  <span className="font-medium">检测成功</span>
                </div>
                {renderDetectionResults()}
              </div>
            ) : (
              <div className="flex items-center space-x-2 text-red-600 dark:text-red-400">
                <AlertCircle className="w-5 h-5" />
                <span className="font-medium">{detectionResult.message}</span>
              </div>
            )}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default DetectionPage;
