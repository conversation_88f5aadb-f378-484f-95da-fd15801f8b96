import React, { useState, useEffect } from 'react';
import { <PERSON> } from 'react-router-dom';
import { motion } from 'framer-motion';
import {
  Camera,
  Database,
  Brain,
  BarChart3,
  Zap,
  Shield,
  Cpu,
  ArrowRight,
  CheckCircle,
  TrendingUp,
  Clock,
  Target,
} from 'lucide-react';
import { ApiService } from '../services/api';
import { SystemStats } from '../types';
import toast from 'react-hot-toast';

const HomePage: React.FC = () => {
  const [stats, setStats] = useState<SystemStats | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // 获取系统统计信息
  useEffect(() => {
    const fetchStats = async () => {
      try {
        const data = await ApiService.getStats();
        setStats(data);
      } catch (error) {
        console.warn('获取统计信息失败:', error);
        // 使用模拟数据
        setStats({
          total_detections: 1234,
          successful_detections: 1180,
          success_rate: 95.6,
          avg_processing_time: 0.85,
          runtime_hours: 168.5,
          start_time: new Date().toISOString(),
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchStats();

    // 定期更新统计信息
    const interval = setInterval(fetchStats, 30000);
    return () => clearInterval(interval);
  }, []);

  // 功能特性
  const features = [
    {
      icon: Zap,
      title: '高速检测',
      description: '基于YOLO算法，毫秒级车牌检测',
      color: 'text-yellow-500',
      bgColor: 'bg-yellow-50 dark:bg-yellow-900/20',
    },
    {
      icon: Shield,
      title: '高精度识别',
      description: '深度学习模型，识别准确率超过95%',
      color: 'text-green-500',
      bgColor: 'bg-green-50 dark:bg-green-900/20',
    },
    {
      icon: Cpu,
      title: '智能优化',
      description: 'GPU加速推理，支持批量处理',
      color: 'text-blue-500',
      bgColor: 'bg-blue-50 dark:bg-blue-900/20',
    },
  ];

  // 快速开始选项
  const quickStartOptions = [
    {
      icon: Camera,
      title: '车牌检测',
      description: '上传图片进行车牌检测和识别',
      path: '/detection',
      color: 'from-blue-500 to-blue-600',
    },
    {
      icon: Database,
      title: '数据管理',
      description: '管理训练数据和采集新数据',
      path: '/data-management',
      color: 'from-green-500 to-green-600',
    },
    {
      icon: Brain,
      title: '模型训练',
      description: '训练和优化车牌识别模型',
      path: '/model-training',
      color: 'from-purple-500 to-purple-600',
    },
    {
      icon: BarChart3,
      title: '统计分析',
      description: '查看系统性能和使用统计',
      path: '/stats',
      color: 'from-orange-500 to-orange-600',
    },
  ];

  // 统计卡片数据
  const statCards = stats ? [
    {
      title: '总检测次数',
      value: stats.total_detections.toLocaleString(),
      icon: Target,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50 dark:bg-blue-900/20',
    },
    {
      title: '成功识别',
      value: stats.successful_detections.toLocaleString(),
      icon: CheckCircle,
      color: 'text-green-600',
      bgColor: 'bg-green-50 dark:bg-green-900/20',
    },
    {
      title: '识别准确率',
      value: `${stats.success_rate.toFixed(1)}%`,
      icon: TrendingUp,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50 dark:bg-purple-900/20',
    },
    {
      title: '平均处理时间',
      value: `${stats.avg_processing_time.toFixed(2)}s`,
      icon: Clock,
      color: 'text-orange-600',
      bgColor: 'bg-orange-50 dark:bg-orange-900/20',
    },
  ] : [];

  return (
    <div className="space-y-12">
      {/* 英雄区域 */}
      <motion.section
        className="text-center py-16 px-4"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        <div className="max-w-4xl mx-auto">
          <motion.div
            className="inline-flex items-center space-x-2 bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400 px-4 py-2 rounded-full text-sm font-medium mb-6"
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ delay: 0.2, duration: 0.4 }}
          >
            <Zap className="w-4 h-4" />
            <span>基于深度学习的智能识别系统</span>
          </motion.div>

          <motion.h1
            className="text-4xl md:text-6xl font-bold text-gray-900 dark:text-white mb-6"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3, duration: 0.6 }}
          >
            <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              智能车牌识别
            </span>
            <br />
            <span className="text-gray-700 dark:text-gray-300">解决方案</span>
          </motion.h1>

          <motion.p
            className="text-xl text-gray-600 dark:text-gray-400 mb-8 max-w-2xl mx-auto"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4, duration: 0.6 }}
          >
            采用最新的YOLO检测算法和深度学习技术，提供高精度、高速度的车牌检测与识别服务
          </motion.p>

          <motion.div
            className="flex flex-col sm:flex-row gap-4 justify-center"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5, duration: 0.6 }}
          >
            <Link
              to="/detection"
              className="btn btn-primary px-8 py-3 text-lg"
            >
              开始检测
              <ArrowRight className="w-5 h-5 ml-2" />
            </Link>
            <Link
              to="/stats"
              className="btn btn-secondary px-8 py-3 text-lg"
            >
              查看统计
            </Link>
          </motion.div>
        </div>
      </motion.section>

      {/* 系统统计 */}
      {!isLoading && stats && (
        <motion.section
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6, duration: 0.6 }}
        >
          {statCards.map((card, index) => {
            const Icon = card.icon;
            return (
              <motion.div
                key={card.title}
                className={`card p-6 ${card.bgColor} border-0`}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.7 + index * 0.1, duration: 0.4 }}
                whileHover={{ scale: 1.02 }}
              >
                <div className="flex items-center justify-between mb-4">
                  <Icon className={`w-8 h-8 ${card.color}`} />
                </div>
                <div className="text-2xl font-bold text-gray-900 dark:text-white mb-1">
                  {card.value}
                </div>
                <div className="text-sm text-gray-600 dark:text-gray-400">
                  {card.title}
                </div>
              </motion.div>
            );
          })}
        </motion.section>
      )}

      {/* 功能特性 */}
      <motion.section
        className="text-center"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.8, duration: 0.6 }}
      >
        <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
          核心特性
        </h2>
        <p className="text-lg text-gray-600 dark:text-gray-400 mb-12 max-w-2xl mx-auto">
          基于最新的深度学习技术，为您提供专业级的车牌识别解决方案
        </p>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {features.map((feature, index) => {
            const Icon = feature.icon;
            return (
              <motion.div
                key={feature.title}
                className={`card p-8 ${feature.bgColor} border-0 text-center`}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.9 + index * 0.1, duration: 0.4 }}
                whileHover={{ scale: 1.05 }}
              >
                <div className={`inline-flex p-4 rounded-full ${feature.bgColor} mb-6`}>
                  <Icon className={`w-8 h-8 ${feature.color}`} />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-3">
                  {feature.title}
                </h3>
                <p className="text-gray-600 dark:text-gray-400">
                  {feature.description}
                </p>
              </motion.div>
            );
          })}
        </div>
      </motion.section>

      {/* 快速开始 */}
      <motion.section
        className="text-center"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 1.0, duration: 0.6 }}
      >
        <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
          快速开始
        </h2>
        <p className="text-lg text-gray-600 dark:text-gray-400 mb-12 max-w-2xl mx-auto">
          选择您需要的功能模块，开始使用车牌识别系统
        </p>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {quickStartOptions.map((option, index) => {
            const Icon = option.icon;
            return (
              <motion.div
                key={option.title}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 1.1 + index * 0.1, duration: 0.4 }}
              >
                <Link
                  to={option.path}
                  className="block card p-6 hover:shadow-lg transition-all duration-200 group"
                >
                  <div className={`inline-flex p-4 rounded-lg bg-gradient-to-r ${option.color} text-white mb-4 group-hover:scale-110 transition-transform duration-200`}>
                    <Icon className="w-6 h-6" />
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                    {option.title}
                  </h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                    {option.description}
                  </p>
                  <div className="flex items-center justify-center text-blue-600 dark:text-blue-400 group-hover:translate-x-1 transition-transform duration-200">
                    <span className="text-sm font-medium">开始使用</span>
                    <ArrowRight className="w-4 h-4 ml-1" />
                  </div>
                </Link>
              </motion.div>
            );
          })}
        </div>
      </motion.section>
    </div>
  );
};

export default HomePage;
