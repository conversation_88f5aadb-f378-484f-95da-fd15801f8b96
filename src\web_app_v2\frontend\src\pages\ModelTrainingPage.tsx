import React, { useState } from 'react';
import { motion } from 'framer-motion';
import {
  Brain,
  Play,
  Pause,
  Square,
  Settings,
  TrendingUp,
  Clock,
  Cpu,
  BarChart3,
  AlertCircle,
  CheckCircle,
  Loader2,
  Download,
  Eye,
} from 'lucide-react';
import { ApiService } from '../services/api';
import { ModelTrainingRequest, ModelTrainingResponse } from '../types';
import toast from 'react-hot-toast';

const ModelTrainingPage: React.FC = () => {
  const [modelType, setModelType] = useState('yolo');
  const [epochs, setEpochs] = useState(100);
  const [batchSize, setBatchSize] = useState(16);
  const [isTraining, setIsTraining] = useState(false);
  const [trainingResult, setTrainingResult] = useState<ModelTrainingResponse | null>(null);

  // 模拟训练进度数据
  const [trainingProgress] = useState({
    currentEpoch: 45,
    totalEpochs: 100,
    loss: 0.234,
    accuracy: 0.892,
    learningRate: 0.001,
    eta: '2h 15m',
  });

  // 开始训练
  const startTraining = async () => {
    setIsTraining(true);
    setTrainingResult(null);

    try {
      const request: ModelTrainingRequest = {
        model_type: modelType,
        epochs,
        batch_size: batchSize,
      };

      const result = await ApiService.trainModel(request);
      setTrainingResult(result);

      if (result.success) {
        toast.success('模型训练任务已启动');
      } else {
        toast.error(result.message || '训练启动失败');
      }
    } catch (error) {
      console.error('训练启动失败:', error);
      toast.error(error instanceof Error ? error.message : '训练启动失败');
    } finally {
      setIsTraining(false);
    }
  };

  // 模型配置选项
  const modelOptions = [
    { value: 'yolo', label: 'YOLO v8', description: '高精度目标检测模型' },
    { value: 'cnn', label: 'CNN', description: '传统卷积神经网络' },
    { value: 'transformer', label: 'Transformer', description: '基于注意力机制的模型' },
  ];

  // 训练历史记录
  const trainingHistory = [
    {
      id: 1,
      model: 'YOLO v8',
      date: '2025-01-20',
      epochs: 100,
      accuracy: 0.956,
      loss: 0.123,
      status: 'completed',
    },
    {
      id: 2,
      model: 'CNN',
      date: '2025-01-19',
      epochs: 80,
      accuracy: 0.892,
      loss: 0.234,
      status: 'completed',
    },
    {
      id: 3,
      model: 'YOLO v8',
      date: '2025-01-18',
      epochs: 150,
      accuracy: 0.934,
      loss: 0.156,
      status: 'completed',
    },
  ];

  return (
    <div className="max-w-6xl mx-auto space-y-8">
      {/* 页面标题 */}
      <motion.div
        className="text-center"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
          模型训练
        </h1>
        <p className="text-lg text-gray-600 dark:text-gray-400">
          训练和优化车牌识别深度学习模型
        </p>
      </motion.div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* 训练配置 */}
        <motion.div
          className="lg:col-span-2 space-y-6"
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.2, duration: 0.6 }}
        >
          <div className="card p-6">
            <div className="flex items-center space-x-3 mb-6">
              <div className="p-2 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                <Settings className="w-6 h-6 text-blue-600 dark:text-blue-400" />
              </div>
              <div>
                <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                  训练配置
                </h2>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  设置模型训练参数
                </p>
              </div>
            </div>

            <div className="space-y-6">
              {/* 模型类型选择 */}
              <div>
                <label className="label">模型类型</label>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  {modelOptions.map((option) => (
                    <motion.div
                      key={option.value}
                      className={`p-4 border-2 rounded-lg cursor-pointer transition-all duration-200 ${
                        modelType === option.value
                          ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                          : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'
                      }`}
                      onClick={() => setModelType(option.value)}
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                    >
                      <div className="text-center">
                        <div className="font-medium text-gray-900 dark:text-white mb-1">
                          {option.label}
                        </div>
                        <div className="text-xs text-gray-600 dark:text-gray-400">
                          {option.description}
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </div>
              </div>

              {/* 训练参数 */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="label">训练轮数 (Epochs)</label>
                  <input
                    type="number"
                    value={epochs}
                    onChange={(e) => setEpochs(parseInt(e.target.value) || 100)}
                    min="10"
                    max="500"
                    className="input"
                  />
                  <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    建议 50-200 轮
                  </p>
                </div>

                <div>
                  <label className="label">批次大小 (Batch Size)</label>
                  <select
                    value={batchSize}
                    onChange={(e) => setBatchSize(parseInt(e.target.value))}
                    className="input"
                  >
                    <option value={8}>8</option>
                    <option value={16}>16</option>
                    <option value={32}>32</option>
                    <option value={64}>64</option>
                  </select>
                  <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    根据GPU内存选择
                  </p>
                </div>
              </div>

              {/* 训练按钮 */}
              <div className="flex space-x-4">
                <button
                  onClick={startTraining}
                  disabled={isTraining}
                  className="btn btn-primary flex-1"
                >
                  {isTraining ? (
                    <>
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                      启动中...
                    </>
                  ) : (
                    <>
                      <Play className="w-4 h-4 mr-2" />
                      开始训练
                    </>
                  )}
                </button>
                <button className="btn btn-secondary">
                  <Pause className="w-4 h-4 mr-2" />
                  暂停
                </button>
                <button className="btn btn-danger">
                  <Square className="w-4 h-4 mr-2" />
                  停止
                </button>
              </div>
            </div>
          </div>

          {/* 训练结果 */}
          {trainingResult && (
            <motion.div
              className="card p-6"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.4 }}
            >
              <div className="flex items-center space-x-2 mb-4">
                {trainingResult.success ? (
                  <CheckCircle className="w-5 h-5 text-green-600 dark:text-green-400" />
                ) : (
                  <AlertCircle className="w-5 h-5 text-red-600 dark:text-red-400" />
                )}
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                  训练状态
                </h3>
              </div>
              <p className="text-gray-600 dark:text-gray-400 mb-2">
                {trainingResult.message}
              </p>
              {trainingResult.task_id && (
                <p className="text-xs text-gray-500 dark:text-gray-400 font-mono">
                  任务ID: {trainingResult.task_id}
                </p>
              )}
            </motion.div>
          )}
        </motion.div>

        {/* 训练监控 */}
        <motion.div
          className="space-y-6"
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.4, duration: 0.6 }}
        >
          {/* 实时进度 */}
          <div className="card p-6">
            <div className="flex items-center space-x-3 mb-6">
              <div className="p-2 bg-green-50 dark:bg-green-900/20 rounded-lg">
                <TrendingUp className="w-6 h-6 text-green-600 dark:text-green-400" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                  训练进度
                </h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  实时监控训练状态
                </p>
              </div>
            </div>

            <div className="space-y-4">
              {/* 进度条 */}
              <div>
                <div className="flex justify-between text-sm mb-2">
                  <span className="text-gray-600 dark:text-gray-400">
                    Epoch {trainingProgress.currentEpoch}/{trainingProgress.totalEpochs}
                  </span>
                  <span className="font-medium text-gray-900 dark:text-white">
                    {Math.round((trainingProgress.currentEpoch / trainingProgress.totalEpochs) * 100)}%
                  </span>
                </div>
                <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                  <div
                    className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                    style={{
                      width: `${(trainingProgress.currentEpoch / trainingProgress.totalEpochs) * 100}%`,
                    }}
                  />
                </div>
              </div>

              {/* 训练指标 */}
              <div className="grid grid-cols-2 gap-4">
                <div className="text-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  <div className="text-lg font-bold text-gray-900 dark:text-white">
                    {trainingProgress.loss.toFixed(3)}
                  </div>
                  <div className="text-xs text-gray-600 dark:text-gray-400">
                    Loss
                  </div>
                </div>
                <div className="text-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  <div className="text-lg font-bold text-gray-900 dark:text-white">
                    {(trainingProgress.accuracy * 100).toFixed(1)}%
                  </div>
                  <div className="text-xs text-gray-600 dark:text-gray-400">
                    Accuracy
                  </div>
                </div>
              </div>

              {/* 其他信息 */}
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">学习率:</span>
                  <span className="font-mono text-gray-900 dark:text-white">
                    {trainingProgress.learningRate}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">预计剩余:</span>
                  <span className="text-gray-900 dark:text-white">
                    {trainingProgress.eta}
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* 系统资源 */}
          <div className="card p-6">
            <div className="flex items-center space-x-3 mb-4">
              <Cpu className="w-5 h-5 text-purple-600 dark:text-purple-400" />
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                系统资源
              </h3>
            </div>

            <div className="space-y-3">
              <div>
                <div className="flex justify-between text-sm mb-1">
                  <span className="text-gray-600 dark:text-gray-400">GPU 使用率</span>
                  <span className="text-gray-900 dark:text-white">85%</span>
                </div>
                <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                  <div className="bg-purple-600 h-2 rounded-full" style={{ width: '85%' }} />
                </div>
              </div>

              <div>
                <div className="flex justify-between text-sm mb-1">
                  <span className="text-gray-600 dark:text-gray-400">GPU 内存</span>
                  <span className="text-gray-900 dark:text-white">6.2/8.0 GB</span>
                </div>
                <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                  <div className="bg-orange-600 h-2 rounded-full" style={{ width: '77%' }} />
                </div>
              </div>

              <div>
                <div className="flex justify-between text-sm mb-1">
                  <span className="text-gray-600 dark:text-gray-400">CPU 使用率</span>
                  <span className="text-gray-900 dark:text-white">45%</span>
                </div>
                <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                  <div className="bg-green-600 h-2 rounded-full" style={{ width: '45%' }} />
                </div>
              </div>
            </div>
          </div>
        </motion.div>
      </div>

      {/* 训练历史 */}
      <motion.section
        className="card p-6"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.6, duration: 0.6 }}
      >
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-orange-50 dark:bg-orange-900/20 rounded-lg">
              <BarChart3 className="w-6 h-6 text-orange-600 dark:text-orange-400" />
            </div>
            <div>
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                训练历史
              </h2>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                查看历史训练记录和模型性能
              </p>
            </div>
          </div>
        </div>

        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b border-gray-200 dark:border-gray-700">
                <th className="text-left py-3 px-4 font-medium text-gray-900 dark:text-white">
                  模型
                </th>
                <th className="text-left py-3 px-4 font-medium text-gray-900 dark:text-white">
                  日期
                </th>
                <th className="text-left py-3 px-4 font-medium text-gray-900 dark:text-white">
                  轮数
                </th>
                <th className="text-left py-3 px-4 font-medium text-gray-900 dark:text-white">
                  准确率
                </th>
                <th className="text-left py-3 px-4 font-medium text-gray-900 dark:text-white">
                  损失
                </th>
                <th className="text-left py-3 px-4 font-medium text-gray-900 dark:text-white">
                  状态
                </th>
                <th className="text-left py-3 px-4 font-medium text-gray-900 dark:text-white">
                  操作
                </th>
              </tr>
            </thead>
            <tbody>
              {trainingHistory.map((record, index) => (
                <motion.tr
                  key={record.id}
                  className="border-b border-gray-100 dark:border-gray-800 hover:bg-gray-50 dark:hover:bg-gray-800/50"
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1, duration: 0.3 }}
                >
                  <td className="py-3 px-4 text-gray-900 dark:text-white">
                    {record.model}
                  </td>
                  <td className="py-3 px-4 text-gray-600 dark:text-gray-400">
                    {record.date}
                  </td>
                  <td className="py-3 px-4 text-gray-600 dark:text-gray-400">
                    {record.epochs}
                  </td>
                  <td className="py-3 px-4">
                    <span className="text-green-600 dark:text-green-400 font-medium">
                      {(record.accuracy * 100).toFixed(1)}%
                    </span>
                  </td>
                  <td className="py-3 px-4 text-gray-600 dark:text-gray-400 font-mono">
                    {record.loss.toFixed(3)}
                  </td>
                  <td className="py-3 px-4">
                    <span className="badge badge-success">已完成</span>
                  </td>
                  <td className="py-3 px-4">
                    <div className="flex space-x-2">
                      <button className="p-1 text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-200">
                        <Eye className="w-4 h-4" />
                      </button>
                      <button className="p-1 text-green-600 hover:text-green-800 dark:text-green-400 dark:hover:text-green-200">
                        <Download className="w-4 h-4" />
                      </button>
                    </div>
                  </td>
                </motion.tr>
              ))}
            </tbody>
          </table>
        </div>
      </motion.section>
    </div>
  );
};

export default ModelTrainingPage;
