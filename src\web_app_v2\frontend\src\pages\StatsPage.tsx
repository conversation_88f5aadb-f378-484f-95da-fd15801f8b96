import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  BarChart3,
  TrendingUp,
  TrendingDown,
  Target,
  Clock,
  CheckCircle,
  AlertTriangle,
  Calendar,
  Download,
  RefreshCw,
  Activity,
  Zap,
} from 'lucide-react';
import { ApiService } from '../services/api';
import { SystemStats } from '../types';

const StatsPage: React.FC = () => {
  const [stats, setStats] = useState<SystemStats | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedPeriod, setSelectedPeriod] = useState('7d');

  // 获取统计数据
  useEffect(() => {
    const fetchStats = async () => {
      setIsLoading(true);
      try {
        const data = await ApiService.getStats();
        setStats(data);
      } catch (error) {
        console.warn('获取统计信息失败:', error);
        // 使用模拟数据
        setStats({
          total_detections: 12456,
          successful_detections: 11892,
          success_rate: 95.47,
          avg_processing_time: 0.847,
          runtime_hours: 720.5,
          start_time: new Date(Date.now() - 720.5 * 60 * 60 * 1000).toISOString(),
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchStats();
    const interval = setInterval(fetchStats, 30000);
    return () => clearInterval(interval);
  }, []);

  // 时间段选项
  const periodOptions = [
    { value: '1d', label: '今天' },
    { value: '7d', label: '7天' },
    { value: '30d', label: '30天' },
    { value: '90d', label: '90天' },
  ];

  // 模拟图表数据
  const chartData = {
    detections: [
      { date: '01-14', count: 156 },
      { date: '01-15', count: 189 },
      { date: '01-16', count: 234 },
      { date: '01-17', count: 198 },
      { date: '01-18', count: 267 },
      { date: '01-19', count: 301 },
      { date: '01-20', count: 289 },
    ],
    accuracy: [
      { date: '01-14', rate: 94.2 },
      { date: '01-15', rate: 95.1 },
      { date: '01-16', rate: 96.3 },
      { date: '01-17', rate: 94.8 },
      { date: '01-18', rate: 95.9 },
      { date: '01-19', rate: 96.7 },
      { date: '01-20', rate: 95.4 },
    ],
  };

  // 性能指标卡片
  const performanceCards = stats ? [
    {
      title: '总检测次数',
      value: stats.total_detections.toLocaleString(),
      icon: Target,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50 dark:bg-blue-900/20',
      trend: { value: 12.5, isPositive: true },
    },
    {
      title: '成功识别',
      value: stats.successful_detections.toLocaleString(),
      icon: CheckCircle,
      color: 'text-green-600',
      bgColor: 'bg-green-50 dark:bg-green-900/20',
      trend: { value: 8.3, isPositive: true },
    },
    {
      title: '识别准确率',
      value: `${stats.success_rate.toFixed(1)}%`,
      icon: TrendingUp,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50 dark:bg-purple-900/20',
      trend: { value: 2.1, isPositive: true },
    },
    {
      title: '平均处理时间',
      value: `${stats.avg_processing_time.toFixed(2)}s`,
      icon: Clock,
      color: 'text-orange-600',
      bgColor: 'bg-orange-50 dark:bg-orange-900/20',
      trend: { value: 5.7, isPositive: false },
    },
  ] : [];

  // 系统状态指标
  const systemMetrics = [
    {
      title: 'CPU 使用率',
      value: '45%',
      icon: Activity,
      color: 'text-green-600',
      status: 'normal',
    },
    {
      title: 'GPU 使用率',
      value: '78%',
      icon: Zap,
      color: 'text-yellow-600',
      status: 'warning',
    },
    {
      title: '内存使用',
      value: '6.2/16GB',
      icon: BarChart3,
      color: 'text-blue-600',
      status: 'normal',
    },
    {
      title: '磁盘空间',
      value: '234/500GB',
      icon: Target,
      color: 'text-purple-600',
      status: 'normal',
    },
  ];

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="text-center">
          <RefreshCw className="w-8 h-8 text-blue-600 animate-spin mx-auto mb-4" />
          <p className="text-gray-600 dark:text-gray-400">加载统计数据中...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto space-y-8">
      {/* 页面标题 */}
      <motion.div
        className="flex items-center justify-between"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
            统计分析
          </h1>
          <p className="text-lg text-gray-600 dark:text-gray-400">
            系统性能监控和数据分析
          </p>
        </div>
        
        <div className="flex items-center space-x-4">
          {/* 时间段选择 */}
          <select
            value={selectedPeriod}
            onChange={(e) => setSelectedPeriod(e.target.value)}
            className="input w-auto"
          >
            {periodOptions.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
          
          {/* 导出按钮 */}
          <button className="btn btn-secondary">
            <Download className="w-4 h-4 mr-2" />
            导出报告
          </button>
        </div>
      </motion.div>

      {/* 性能指标卡片 */}
      <motion.section
        className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2, duration: 0.6 }}
      >
        {performanceCards.map((card, index) => {
          const Icon = card.icon;
          const TrendIcon = card.trend?.isPositive ? TrendingUp : TrendingDown;
          
          return (
            <motion.div
              key={card.title}
              className={`card p-6 ${card.bgColor} border-0`}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 + index * 0.1, duration: 0.4 }}
              whileHover={{ scale: 1.02 }}
            >
              <div className="flex items-center justify-between mb-4">
                <Icon className={`w-8 h-8 ${card.color}`} />
                {card.trend && (
                  <div className={`flex items-center space-x-1 text-sm ${
                    card.trend.isPositive ? 'text-green-600' : 'text-red-600'
                  }`}>
                    <TrendIcon className="w-4 h-4" />
                    <span>{card.trend.value}%</span>
                  </div>
                )}
              </div>
              <div className="text-2xl font-bold text-gray-900 dark:text-white mb-1">
                {card.value}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">
                {card.title}
              </div>
            </motion.div>
          );
        })}
      </motion.section>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* 检测趋势图 */}
        <motion.div
          className="lg:col-span-2 card p-6"
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.4, duration: 0.6 }}
        >
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
              检测趋势
            </h2>
            <div className="flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400">
              <Calendar className="w-4 h-4" />
              <span>最近7天</span>
            </div>
          </div>
          
          {/* 简化的图表显示 */}
          <div className="space-y-4">
            <div className="flex items-end space-x-2 h-48">
              {chartData.detections.map((item, index) => (
                <motion.div
                  key={item.date}
                  className="flex-1 bg-blue-500 rounded-t-lg relative"
                  style={{ height: `${(item.count / 350) * 100}%` }}
                  initial={{ height: 0 }}
                  animate={{ height: `${(item.count / 350) * 100}%` }}
                  transition={{ delay: 0.5 + index * 0.1, duration: 0.5 }}
                >
                  <div className="absolute -top-6 left-1/2 transform -translate-x-1/2 text-xs text-gray-600 dark:text-gray-400">
                    {item.count}
                  </div>
                  <div className="absolute -bottom-6 left-1/2 transform -translate-x-1/2 text-xs text-gray-500 dark:text-gray-400">
                    {item.date}
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </motion.div>

        {/* 系统状态 */}
        <motion.div
          className="space-y-6"
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.6, duration: 0.6 }}
        >
          <div className="card p-6">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-6">
              系统状态
            </h2>
            
            <div className="space-y-4">
              {systemMetrics.map((metric, index) => {
                const Icon = metric.icon;
                return (
                  <motion.div
                    key={metric.title}
                    className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg"
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.7 + index * 0.1, duration: 0.3 }}
                  >
                    <div className="flex items-center space-x-3">
                      <Icon className={`w-5 h-5 ${metric.color}`} />
                      <span className="text-sm text-gray-600 dark:text-gray-400">
                        {metric.title}
                      </span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className="text-sm font-medium text-gray-900 dark:text-white">
                        {metric.value}
                      </span>
                      {metric.status === 'warning' && (
                        <AlertTriangle className="w-4 h-4 text-yellow-500" />
                      )}
                    </div>
                  </motion.div>
                );
              })}
            </div>
          </div>

          {/* 准确率趋势 */}
          <div className="card p-6">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
              准确率趋势
            </h3>
            
            <div className="space-y-3">
              {chartData.accuracy.slice(-5).map((item, index) => (
                <motion.div
                  key={item.date}
                  className="flex items-center justify-between"
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.8 + index * 0.1, duration: 0.3 }}
                >
                  <span className="text-sm text-gray-600 dark:text-gray-400">
                    {item.date}
                  </span>
                  <div className="flex items-center space-x-2">
                    <div className="w-16 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                      <div
                        className="bg-green-500 h-2 rounded-full"
                        style={{ width: `${item.rate}%` }}
                      />
                    </div>
                    <span className="text-sm font-medium text-gray-900 dark:text-white w-12">
                      {item.rate}%
                    </span>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </motion.div>
      </div>

      {/* 详细统计表格 */}
      <motion.section
        className="card p-6"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.8, duration: 0.6 }}
      >
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-6">
          详细统计
        </h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div className="text-center p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
            <div className="text-2xl font-bold text-blue-600 dark:text-blue-400 mb-2">
              {stats?.runtime_hours.toFixed(1)}h
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-400">
              系统运行时间
            </div>
          </div>
          
          <div className="text-center p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
            <div className="text-2xl font-bold text-green-600 dark:text-green-400 mb-2">
              {stats && Math.round(stats.total_detections / (stats.runtime_hours || 1))}
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-400">
              每小时检测数
            </div>
          </div>
          
          <div className="text-center p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
            <div className="text-2xl font-bold text-purple-600 dark:text-purple-400 mb-2">
              {stats && (stats.total_detections - stats.successful_detections)}
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-400">
              识别失败次数
            </div>
          </div>
          
          <div className="text-center p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
            <div className="text-2xl font-bold text-orange-600 dark:text-orange-400 mb-2">
              {stats && new Date(stats.start_time).toLocaleDateString()}
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-400">
              系统启动日期
            </div>
          </div>
        </div>
      </motion.section>
    </div>
  );
};

export default StatsPage;
