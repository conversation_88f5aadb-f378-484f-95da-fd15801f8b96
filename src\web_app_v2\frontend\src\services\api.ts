/**
 * API 服务层
 * 封装所有与后端的通信逻辑
 */

import axios, { AxiosInstance, AxiosResponse, AxiosError } from 'axios';
import {
  DetectionResult,
  SystemStats,
  HealthCheck,
  DataCollectionRequest,
  DataCollectionResponse,
  ModelTrainingRequest,
  ModelTrainingResponse,
  UploadProgress,
  ErrorResponse,
} from '../types';

// 创建 axios 实例
const createApiInstance = (): AxiosInstance => {
  const instance = axios.create({
    baseURL: '/api/v1',
    timeout: 30000,
    headers: {
      'Content-Type': 'application/json',
    },
  });

  // 请求拦截器
  instance.interceptors.request.use(
    (config) => {
      console.log(`🚀 API Request: ${config.method?.toUpperCase()} ${config.url}`);
      return config;
    },
    (error) => {
      console.error('❌ Request Error:', error);
      return Promise.reject(error);
    }
  );

  // 响应拦截器
  instance.interceptors.response.use(
    (response: AxiosResponse) => {
      console.log(`✅ API Response: ${response.status} ${response.config.url}`);
      return response;
    },
    (error: AxiosError<ErrorResponse>) => {
      console.error('❌ Response Error:', error);
      
      // 统一错误处理
      const errorMessage = error.response?.data?.detail || error.message || '请求失败';
      const statusCode = error.response?.status;
      
      // 根据状态码处理不同类型的错误
      switch (statusCode) {
        case 400:
          console.error('Bad Request:', errorMessage);
          break;
        case 401:
          console.error('Unauthorized:', errorMessage);
          break;
        case 403:
          console.error('Forbidden:', errorMessage);
          break;
        case 404:
          console.error('Not Found:', errorMessage);
          break;
        case 500:
          console.error('Internal Server Error:', errorMessage);
          break;
        default:
          console.error('Unknown Error:', errorMessage);
      }
      
      return Promise.reject(new Error(errorMessage));
    }
  );

  return instance;
};

// API 实例
const api = createApiInstance();

/**
 * API 服务类
 */
export class ApiService {
  /**
   * 健康检查
   */
  static async healthCheck(): Promise<HealthCheck> {
    const response = await api.get<HealthCheck>('/health');
    return response.data;
  }

  /**
   * 获取系统统计信息
   */
  static async getStats(): Promise<SystemStats> {
    const response = await api.get<SystemStats>('/stats');
    return response.data;
  }

  /**
   * 上传文件并进行车牌检测
   */
  static async uploadAndDetect(
    file: File,
    onProgress?: (progress: UploadProgress) => void
  ): Promise<DetectionResult> {
    const formData = new FormData();
    formData.append('file', file);

    const response = await api.post<DetectionResult>('/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      onUploadProgress: (progressEvent) => {
        if (onProgress && progressEvent.total) {
          const progress: UploadProgress = {
            loaded: progressEvent.loaded,
            total: progressEvent.total,
            percentage: Math.round((progressEvent.loaded * 100) / progressEvent.total),
          };
          onProgress(progress);
        }
      },
    });

    return response.data;
  }

  /**
   * 数据采集
   */
  static async collectData(request: DataCollectionRequest): Promise<DataCollectionResponse> {
    const response = await api.post<DataCollectionResponse>('/data/collect', request);
    return response.data;
  }

  /**
   * 模型训练
   */
  static async trainModel(request: ModelTrainingRequest): Promise<ModelTrainingResponse> {
    const response = await api.post<ModelTrainingResponse>('/model/train', request);
    return response.data;
  }

  /**
   * 获取结果文件
   */
  static getResultFileUrl(filename: string): string {
    return `/api/v1/results/${filename}`;
  }

  /**
   * 下载结果文件
   */
  static async downloadResultFile(filename: string): Promise<Blob> {
    const response = await api.get(`/results/${filename}`, {
      responseType: 'blob',
    });
    return response.data;
  }
}

/**
 * 错误处理工具
 */
export class ApiError extends Error {
  constructor(
    message: string,
    public statusCode?: number,
    public originalError?: any
  ) {
    super(message);
    this.name = 'ApiError';
  }

  static fromAxiosError(error: AxiosError<ErrorResponse>): ApiError {
    const message = error.response?.data?.detail || error.message || '请求失败';
    const statusCode = error.response?.status;
    return new ApiError(message, statusCode, error);
  }
}

/**
 * 重试机制
 */
export const withRetry = async <T>(
  fn: () => Promise<T>,
  maxRetries: number = 3,
  delay: number = 1000
): Promise<T> => {
  let lastError: Error;

  for (let i = 0; i <= maxRetries; i++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error as Error;
      
      if (i === maxRetries) {
        break;
      }

      console.warn(`Retry ${i + 1}/${maxRetries} after ${delay}ms:`, error);
      await new Promise(resolve => setTimeout(resolve, delay));
      delay *= 2; // 指数退避
    }
  }

  throw lastError!;
};

/**
 * 批量请求工具
 */
export const batchRequest = async <T>(
  requests: Array<() => Promise<T>>,
  concurrency: number = 3
): Promise<T[]> => {
  const results: T[] = [];
  
  for (let i = 0; i < requests.length; i += concurrency) {
    const batch = requests.slice(i, i + concurrency);
    const batchResults = await Promise.all(batch.map(request => request()));
    results.push(...batchResults);
  }
  
  return results;
};

export default ApiService;
