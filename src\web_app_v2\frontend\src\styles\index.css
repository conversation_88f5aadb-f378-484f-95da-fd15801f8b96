@tailwind base;
@tailwind components;
@tailwind utilities;

/* 基础样式 */
@layer base {
  html {
    scroll-behavior: smooth;
  }
  
  body {
    @apply bg-gray-50 dark:bg-gray-900 text-gray-900 dark:text-gray-100;
    @apply transition-colors duration-300;
  }
  
  * {
    @apply border-gray-200 dark:border-gray-700;
  }
}

/* 组件样式 */
@layer components {
  /* 按钮样式 */
  .btn {
    @apply inline-flex items-center justify-center px-4 py-2 rounded-lg font-medium;
    @apply transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2;
    @apply disabled:opacity-50 disabled:cursor-not-allowed;
  }
  
  .btn-primary {
    @apply bg-primary-600 hover:bg-primary-700 text-white;
    @apply focus:ring-primary-500 dark:focus:ring-primary-400;
  }
  
  .btn-secondary {
    @apply bg-gray-200 hover:bg-gray-300 text-gray-900;
    @apply dark:bg-gray-700 dark:hover:bg-gray-600 dark:text-gray-100;
    @apply focus:ring-gray-500;
  }
  
  .btn-success {
    @apply bg-success-600 hover:bg-success-700 text-white;
    @apply focus:ring-success-500;
  }
  
  .btn-warning {
    @apply bg-warning-600 hover:bg-warning-700 text-white;
    @apply focus:ring-warning-500;
  }
  
  .btn-danger {
    @apply bg-danger-600 hover:bg-danger-700 text-white;
    @apply focus:ring-danger-500;
  }
  
  /* 卡片样式 */
  .card {
    @apply bg-white dark:bg-gray-800 rounded-xl shadow-sm border;
    @apply border-gray-200 dark:border-gray-700;
    @apply transition-all duration-200;
  }
  
  .card-hover {
    @apply hover:shadow-md hover:scale-[1.02];
  }
  
  /* 输入框样式 */
  .input {
    @apply w-full px-3 py-2 border rounded-lg;
    @apply border-gray-300 dark:border-gray-600;
    @apply bg-white dark:bg-gray-800;
    @apply text-gray-900 dark:text-gray-100;
    @apply placeholder-gray-500 dark:placeholder-gray-400;
    @apply focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent;
    @apply transition-all duration-200;
  }
  
  /* 标签样式 */
  .label {
    @apply block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1;
  }
  
  /* 徽章样式 */
  .badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }
  
  .badge-primary {
    @apply bg-primary-100 text-primary-800 dark:bg-primary-900 dark:text-primary-200;
  }
  
  .badge-success {
    @apply bg-success-100 text-success-800 dark:bg-success-900 dark:text-success-200;
  }
  
  .badge-warning {
    @apply bg-warning-100 text-warning-800 dark:bg-warning-900 dark:text-warning-200;
  }
  
  .badge-danger {
    @apply bg-danger-100 text-danger-800 dark:bg-danger-900 dark:text-danger-200;
  }
  
  /* 加载动画 */
  .loading-spinner {
    @apply animate-spin rounded-full border-2 border-gray-300 border-t-primary-600;
  }
  
  /* 渐变背景 */
  .gradient-bg {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  }
  
  .gradient-success {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  }
  
  .gradient-warning {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  }
  
  /* 玻璃态效果 */
  .glass {
    @apply backdrop-blur-sm bg-white/10 dark:bg-gray-900/10;
    @apply border border-white/20 dark:border-gray-700/20;
  }
  
  /* 拖拽区域 */
  .dropzone {
    @apply border-2 border-dashed border-gray-300 dark:border-gray-600;
    @apply rounded-lg p-8 text-center;
    @apply transition-all duration-200;
  }
  
  .dropzone-active {
    @apply border-primary-500 bg-primary-50 dark:bg-primary-900/20;
  }
  
  .dropzone-reject {
    @apply border-danger-500 bg-danger-50 dark:bg-danger-900/20;
  }
}

/* 工具样式 */
@layer utilities {
  /* 滚动条样式 */
  .scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: rgb(156 163 175) transparent;
  }
  
  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
  }
  
  .scrollbar-thin::-webkit-scrollbar-track {
    background: transparent;
  }
  
  .scrollbar-thin::-webkit-scrollbar-thumb {
    background-color: rgb(156 163 175);
    border-radius: 3px;
  }
  
  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background-color: rgb(107 114 128);
  }
  
  /* 文本省略 */
  .text-ellipsis-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
  
  .text-ellipsis-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
  
  /* 动画延迟 */
  .animate-delay-100 {
    animation-delay: 100ms;
  }
  
  .animate-delay-200 {
    animation-delay: 200ms;
  }
  
  .animate-delay-300 {
    animation-delay: 300ms;
  }
  
  .animate-delay-500 {
    animation-delay: 500ms;
  }
}

/* 自定义动画 */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

/* 响应式隐藏 */
@media (max-width: 640px) {
  .hide-mobile {
    display: none;
  }
}

@media (min-width: 641px) {
  .show-mobile {
    display: none;
  }
}
