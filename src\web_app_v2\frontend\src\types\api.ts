/**
 * API 接口类型定义
 */

// 基础响应类型
export interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  data?: T;
}

// 车牌检测结果
export interface LicensePlateDetection {
  license_plate: string;
  confidence: number;
  bbox: [number, number, number, number]; // [x1, y1, x2, y2]
  type: string;
}

// 检测结果响应
export interface DetectionResult {
  success: boolean;
  message: string;
  results?: LicensePlateDetection[];
  processing_time?: number;
  image_url?: string;
}

// 系统统计信息
export interface SystemStats {
  total_detections: number;
  successful_detections: number;
  success_rate: number;
  avg_processing_time: number;
  runtime_hours: number;
  start_time: string;
}

// 健康检查响应
export interface HealthCheck {
  status: string;
  timestamp: string;
  models_loaded: boolean;
}

// 数据采集请求
export interface DataCollectionRequest {
  keywords: string[];
  max_images: number;
}

// 数据采集响应
export interface DataCollectionResponse {
  success: boolean;
  message: string;
  task_id: string;
}

// 模型训练请求
export interface ModelTrainingRequest {
  model_type: string;
  epochs: number;
  batch_size: number;
}

// 模型训练响应
export interface ModelTrainingResponse {
  success: boolean;
  message: string;
  task_id: string;
}

// 文件上传进度
export interface UploadProgress {
  loaded: number;
  total: number;
  percentage: number;
}

// 错误响应
export interface ErrorResponse {
  detail: string;
  status_code?: number;
}
