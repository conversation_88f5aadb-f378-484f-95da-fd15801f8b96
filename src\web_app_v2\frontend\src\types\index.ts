/**
 * 通用类型定义
 */

export * from './api';

// 主题类型
export type Theme = 'light' | 'dark';

// 页面路由类型
export interface Route {
  path: string;
  name: string;
  icon?: string;
  component: React.ComponentType;
}

// 导航菜单项
export interface MenuItem {
  key: string;
  label: string;
  icon?: React.ReactNode;
  path: string;
  children?: MenuItem[];
}

// 文件类型
export interface FileInfo {
  file: File;
  preview?: string;
  id: string;
}

// 上传状态
export type UploadStatus = 'idle' | 'uploading' | 'success' | 'error';

// 通知类型
export type NotificationType = 'success' | 'error' | 'warning' | 'info';

// 通知消息
export interface Notification {
  id: string;
  type: NotificationType;
  title: string;
  message?: string;
  duration?: number;
}

// 加载状态
export interface LoadingState {
  isLoading: boolean;
  message?: string;
}

// 分页信息
export interface Pagination {
  current: number;
  pageSize: number;
  total: number;
}

// 表格列定义
export interface TableColumn<T = any> {
  key: string;
  title: string;
  dataIndex?: keyof T;
  render?: (value: any, record: T, index: number) => React.ReactNode;
  width?: number | string;
  align?: 'left' | 'center' | 'right';
  sorter?: boolean;
  fixed?: 'left' | 'right';
}

// 表单字段类型
export interface FormField {
  name: string;
  label: string;
  type: 'text' | 'number' | 'email' | 'password' | 'select' | 'textarea' | 'file' | 'checkbox';
  required?: boolean;
  placeholder?: string;
  options?: Array<{ label: string; value: any }>;
  validation?: {
    min?: number;
    max?: number;
    pattern?: RegExp;
    message?: string;
  };
}

// 图表数据点
export interface ChartDataPoint {
  name: string;
  value: number;
  color?: string;
}

// 统计卡片数据
export interface StatCard {
  title: string;
  value: string | number;
  icon: React.ReactNode;
  color: string;
  trend?: {
    value: number;
    isPositive: boolean;
  };
}
