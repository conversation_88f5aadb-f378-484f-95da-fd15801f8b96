#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
车牌识别系统快速启动脚本
直接在当前环境中启动，无需环境检查
"""

import os
import sys
import subprocess
from pathlib import Path

def install_opencv():
    """安装opencv-python"""
    try:
        import cv2
        print("✅ OpenCV 已安装")
        return True
    except ImportError:
        print("📥 正在安装 OpenCV...")
        try:
            subprocess.run([sys.executable, "-m", "pip", "install", "opencv-python"], check=True)
            print("✅ OpenCV 安装成功")
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ OpenCV 安装失败: {e}")
            return False

def start_backend():
    """启动后端服务"""
    print("=" * 60)
    print("🚗 车牌识别系统 FastAPI 后端")
    print("=" * 60)
    print(f"🐍 Python: {sys.executable}")
    print(f"🌍 环境: {os.environ.get('CONDA_DEFAULT_ENV', 'unknown')}")
    print("=" * 60)
    
    # 安装OpenCV
    if not install_opencv():
        return False
    
    # 切换到后端目录
    backend_dir = Path(__file__).parent / "backend"
    original_dir = os.getcwd()
    
    try:
        os.chdir(backend_dir)
        print(f"📁 工作目录: {os.getcwd()}")
        print("🚀 启动 FastAPI 服务...")
        
        # 直接导入并运行
        from main import app
        import uvicorn
        
        print("✅ 模块导入成功")
        print("=" * 60)
        print("🌐 服务地址:")
        print("   - API: http://127.0.0.1:8000")
        print("   - 文档: http://127.0.0.1:8000/api/docs")
        print("=" * 60)
        print("💡 按 Ctrl+C 停止服务")
        print("=" * 60)
        
        # 启动服务
        uvicorn.run(
            "main:app",
            host="127.0.0.1",
            port=8000,
            reload=True,
            log_level="info"
        )
        
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        return False
    finally:
        os.chdir(original_dir)
    
    return True

def main():
    """主函数"""
    try:
        start_backend()
    except KeyboardInterrupt:
        print("\n🛑 服务已停止")
    except Exception as e:
        print(f"❌ 发生错误: {e}")

if __name__ == "__main__":
    main()
