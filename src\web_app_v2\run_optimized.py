#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
车牌识别系统优化启动脚本
确保在 pytorch_env 环境中运行，并自动安装缺失的依赖
"""

import os
import sys
import subprocess
import time
import signal
import threading
from pathlib import Path

def check_and_activate_environment():
    """检查并确保在pytorch_env环境中"""
    conda_env = os.environ.get('CONDA_DEFAULT_ENV')
    
    if conda_env != 'pytorch_env':
        print("=" * 80)
        print("⚠️  当前不在 pytorch_env 环境中")
        print(f"📍 当前环境: {conda_env or 'base'}")
        print("=" * 80)
        print("🔧 尝试自动激活 pytorch_env 环境...")
        
        try:
            # 尝试激活环境
            if os.name == 'nt':  # Windows
                activate_cmd = f"conda activate pytorch_env"
            else:  # Linux/Mac
                activate_cmd = f"source activate pytorch_env"
            
            print(f"执行命令: {activate_cmd}")
            print("💡 请手动在终端中执行以下命令:")
            print(f"   {activate_cmd}")
            print("   然后重新运行此脚本")
            print("=" * 80)
            return False
            
        except Exception as e:
            print(f"❌ 自动激活失败: {e}")
            return False
    
    print(f"✅ 当前在正确的环境中: {conda_env}")
    return True

def install_missing_dependencies():
    """安装缺失的依赖包"""
    print("📦 检查并安装必要的依赖包...")
    
    dependencies = [
        ("opencv-python", "cv2"),
        ("torch", "torch"),
        ("fastapi", "fastapi"),
        ("uvicorn", "uvicorn"),
        ("python-multipart", "multipart")
    ]
    
    missing_packages = []
    
    for package_name, import_name in dependencies:
        try:
            if import_name == "multipart":
                import multipart
            else:
                __import__(import_name)
            print(f"✅ {package_name} 已安装")
        except ImportError:
            print(f"❌ {package_name} 未安装")
            missing_packages.append(package_name)
    
    if missing_packages:
        print(f"📥 需要安装的包: {', '.join(missing_packages)}")
        for package in missing_packages:
            try:
                print(f"正在安装 {package}...")
                result = subprocess.run(
                    [sys.executable, "-m", "pip", "install", package],
                    capture_output=True,
                    text=True,
                    check=True
                )
                print(f"✅ {package} 安装成功")
            except subprocess.CalledProcessError as e:
                print(f"❌ {package} 安装失败: {e.stderr}")
                return False
    
    return True

def start_backend_service():
    """启动后端服务"""
    print("=" * 80)
    print("🚗 车牌识别系统 - 优化版启动")
    print("=" * 80)
    print("🔧 架构: React + TypeScript + Tailwind CSS + FastAPI")
    print("🌍 环境: pytorch_env")
    print("=" * 80)
    
    # 检查环境
    if not check_and_activate_environment():
        return False
    
    # 安装依赖
    if not install_missing_dependencies():
        return False
    
    # 切换到后端目录
    backend_dir = Path(__file__).parent / "backend"
    os.chdir(backend_dir)
    
    print("🚀 启动 FastAPI 后端服务...")
    
    try:
        # 启动FastAPI服务
        process = subprocess.Popen(
            [sys.executable, "main.py"],
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True,
            bufsize=1
        )
        
        print("✅ 后端服务启动成功")
        print("=" * 80)
        print("🌐 服务地址:")
        print("   - API 服务: http://127.0.0.1:8000")
        print("   - API 文档: http://127.0.0.1:8000/api/docs")
        print("   - 交互文档: http://127.0.0.1:8000/api/redoc")
        print("=" * 80)
        print("💡 按 Ctrl+C 停止服务")
        print("=" * 80)
        
        # 实时输出日志
        def log_output():
            for line in iter(process.stdout.readline, ''):
                print(f"[Backend] {line.rstrip()}")
        
        log_thread = threading.Thread(target=log_output, daemon=True)
        log_thread.start()
        
        # 等待进程结束
        process.wait()
        
    except KeyboardInterrupt:
        print("\n🛑 正在停止服务...")
        process.terminate()
        process.wait()
        print("✅ 服务已停止")
    except Exception as e:
        print(f"❌ 服务启动失败: {e}")
        return False
    
    return True

def check_node_js():
    """检查Node.js是否安装"""
    try:
        subprocess.run(["node", "--version"], capture_output=True, check=True)
        subprocess.run(["npm", "--version"], capture_output=True, check=True)
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        return False

def show_frontend_instructions():
    """显示前端启动说明"""
    print("\n" + "=" * 80)
    print("📱 前端启动说明")
    print("=" * 80)
    
    if check_node_js():
        print("✅ Node.js 已安装")
        print("🚀 启动前端服务:")
        print("   cd src/web_app_v2/frontend")
        print("   npm install")
        print("   npm run dev")
        print("   然后访问: http://localhost:3000")
    else:
        print("❌ Node.js 未安装")
        print("💡 请先安装 Node.js:")
        print("   下载地址: https://nodejs.org/")
        print("   安装完成后运行前端启动命令")
    
    print("=" * 80)

def main():
    """主函数"""
    try:
        # 启动后端服务
        if start_backend_service():
            show_frontend_instructions()
        else:
            print("❌ 后端服务启动失败")
            sys.exit(1)
            
    except Exception as e:
        print(f"❌ 发生错误: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
