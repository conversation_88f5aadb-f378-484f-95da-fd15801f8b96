#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
车牌识别系统开发环境启动脚本
同时启动 FastAPI 后端和 React 前端开发服务器
"""

import os
import sys
import subprocess
import time
import signal
import threading
from pathlib import Path

# 获取项目路径
current_dir = Path(__file__).parent.resolve()
backend_dir = current_dir / "backend"
frontend_dir = current_dir / "frontend"

class DevServer:
    def __init__(self):
        self.backend_process = None
        self.frontend_process = None
        self.running = True

    def start_backend(self):
        """启动 FastAPI 后端服务"""
        print("🚀 启动 FastAPI 后端服务...")
        print("📋 激活 pytorch_env 环境...")
        try:
            os.chdir(backend_dir)
            # 使用conda激活pytorch_env环境并启动FastAPI
            if os.name == 'nt':  # Windows
                cmd = ["conda", "run", "-n", "pytorch_env", "python", "main.py"]
            else:  # Linux/Mac
                cmd = ["conda", "run", "-n", "pytorch_env", "python", "main.py"]

            self.backend_process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                bufsize=1
            )
            
            # 实时输出后端日志
            def log_backend():
                for line in iter(self.backend_process.stdout.readline, ''):
                    if self.running:
                        print(f"[Backend] {line.rstrip()}")
                    else:
                        break
            
            threading.Thread(target=log_backend, daemon=True).start()
            print("✅ FastAPI 后端服务启动成功")
            
        except Exception as e:
            print(f"❌ FastAPI 后端启动失败: {e}")
            return False
        
        return True

    def start_frontend(self):
        """启动 React 前端开发服务器"""
        print("🚀 启动 React 前端开发服务器...")
        try:
            os.chdir(frontend_dir)
            
            # 检查是否需要安装依赖
            if not (frontend_dir / "node_modules").exists():
                print("📦 安装前端依赖...")
                install_process = subprocess.run(
                    ["npm", "install"],
                    capture_output=True,
                    text=True
                )
                if install_process.returncode != 0:
                    print(f"❌ 依赖安装失败: {install_process.stderr}")
                    return False
                print("✅ 前端依赖安装完成")
            
            # 启动开发服务器
            self.frontend_process = subprocess.Popen(
                ["npm", "run", "dev"],
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                bufsize=1
            )
            
            # 实时输出前端日志
            def log_frontend():
                for line in iter(self.frontend_process.stdout.readline, ''):
                    if self.running:
                        print(f"[Frontend] {line.rstrip()}")
                    else:
                        break
            
            threading.Thread(target=log_frontend, daemon=True).start()
            print("✅ React 前端开发服务器启动成功")
            
        except Exception as e:
            print(f"❌ React 前端启动失败: {e}")
            return False
        
        return True

    def stop_servers(self):
        """停止所有服务"""
        print("\n🛑 正在停止开发服务器...")
        self.running = False
        
        if self.backend_process:
            try:
                self.backend_process.terminate()
                self.backend_process.wait(timeout=5)
                print("✅ FastAPI 后端服务已停止")
            except subprocess.TimeoutExpired:
                self.backend_process.kill()
                print("⚠️ 强制终止 FastAPI 后端服务")
        
        if self.frontend_process:
            try:
                self.frontend_process.terminate()
                self.frontend_process.wait(timeout=5)
                print("✅ React 前端服务已停止")
            except subprocess.TimeoutExpired:
                self.frontend_process.kill()
                print("⚠️ 强制终止 React 前端服务")

    def run(self):
        """运行开发环境"""
        print("=" * 80)
        print("🚗 车牌识别系统 - 开发环境启动")
        print("=" * 80)
        print("📋 架构: React + TypeScript + Tailwind CSS (前端) + FastAPI (后端)")
        print("🔧 模式: 前后端分离开发模式")
        print("=" * 80)
        
        # 设置信号处理
        def signal_handler(signum, frame):
            self.stop_servers()
            sys.exit(0)
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
        
        try:
            # 启动后端服务
            if not self.start_backend():
                return
            
            # 等待后端启动
            print("⏳ 等待后端服务启动...")
            time.sleep(3)
            
            # 启动前端服务
            if not self.start_frontend():
                self.stop_servers()
                return
            
            print("\n" + "=" * 80)
            print("🎉 开发环境启动完成!")
            print("=" * 80)
            print("🌐 前端地址: http://localhost:3000")
            print("🔧 后端地址: http://127.0.0.1:8000")
            print("📖 API 文档: http://127.0.0.1:8000/api/docs")
            print("=" * 80)
            print("💡 提示: 按 Ctrl+C 停止所有服务")
            print("=" * 80)
            
            # 保持运行
            while self.running:
                time.sleep(1)
                
                # 检查进程状态
                if self.backend_process and self.backend_process.poll() is not None:
                    print("❌ 后端服务意外停止")
                    break
                    
                if self.frontend_process and self.frontend_process.poll() is not None:
                    print("❌ 前端服务意外停止")
                    break
        
        except KeyboardInterrupt:
            pass
        finally:
            self.stop_servers()

def main():
    """主函数"""
    # 检查环境
    if not frontend_dir.exists():
        print(f"❌ 前端目录不存在: {frontend_dir}")
        return
    
    if not backend_dir.exists():
        print(f"❌ 后端目录不存在: {backend_dir}")
        return
    
    # 检查 Node.js
    try:
        subprocess.run(["node", "--version"], capture_output=True, check=True)
        subprocess.run(["npm", "--version"], capture_output=True, check=True)
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("❌ 请先安装 Node.js 和 npm")
        print("💡 下载地址: https://nodejs.org/")
        return
    
    # 启动开发服务器
    server = DevServer()
    server.run()

if __name__ == "__main__":
    main()
