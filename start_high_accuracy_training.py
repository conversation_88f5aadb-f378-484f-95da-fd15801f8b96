#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
启动高精度训练
High Accuracy Training

Author: License Plate Recognition Team
Date: 2025-07-30
"""

import requests
import json
import time
import os

def start_high_accuracy_training():
    """启动高精度训练"""
    print("🎯 启动高精度车牌识别训练")
    print("=" * 60)
    
    # 高精度训练配置
    config = {
        "experiment_name": "高精度车牌识别训练-ResNet50",
        "model_type": "resnet50",
        "batch_size": 32,
        "learning_rate": 0.0005,
        "epochs": 30,
        "dataset_size": 5000,
        "use_pretrained": True,
        "optimizer_type": "adamw",
        "scheduler_type": "cosine",
        "data_augmentation": True,
        "early_stopping": True,
        "patience": 10,
        "weight_decay": 0.0001
    }
    
    try:
        # 启动训练
        print("🚀 正在启动高精度训练...")
        response = requests.post('http://127.0.0.1:8007/api/training/start', json=config)
        result = response.json()
        
        print("✅ 高精度训练已启动!")
        print(f"任务ID: {result['job_id']}")
        print(f"实验名称: {config['experiment_name']}")
        print(f"模型类型: {config['model_type']} (预训练)")
        print(f"训练轮次: {config['epochs']}")
        print(f"数据集大小: {config['dataset_size']}")
        print(f"批次大小: {config['batch_size']}")
        print(f"学习率: {config['learning_rate']}")
        print(f"优化器: {config['optimizer_type']}")
        print(f"调度器: {config['scheduler_type']}")
        print(f"数据增强: {'✅' if config['data_augmentation'] else '❌'}")
        print(f"早停机制: {'✅' if config['early_stopping'] else '❌'}")
        
        job_id = result['job_id']
        
        print("\n📊 开始实时监控训练进度...")
        print("按 Ctrl+C 停止监控 (训练将继续在后台运行)")
        print("=" * 60)
        
        last_epoch = 0
        
        # 监控训练进度
        while True:
            try:
                status_response = requests.get(f'http://127.0.0.1:8007/api/training/status/{job_id}')
                job = status_response.json()
                
                # 清屏显示完整状态
                if job['current_epoch'] != last_epoch:
                    os.system('cls' if os.name == 'nt' else 'clear')
                    display_detailed_status(job)
                    last_epoch = job['current_epoch']
                else:
                    # 只更新进度行
                    print(f"\r📈 实时状态: {job['status']} | 进度: {job['progress']:.1f}% | "
                          f"轮次: {job['current_epoch']}/{job['config']['epochs']} | "
                          f"验证准确率: {job['val_acc']*100:.2f}% | "
                          f"最佳: {job['best_acc']*100:.2f}% | "
                          f"学习率: {job['learning_rate']:.6f}", end="")
                
                if job['status'] in ['completed', 'failed', 'stopped']:
                    print(f"\n\n🏁 训练结束! 状态: {job['status']}")
                    display_final_results(job)
                    break
                
                time.sleep(1)
                
            except KeyboardInterrupt:
                print(f"\n\n⏹️ 监控已停止")
                print(f"训练任务 {job_id} 仍在后台运行")
                print("可以通过以下方式继续监控:")
                print(f"1. 网页界面: http://127.0.0.1:8007")
                print(f"2. 终端客户端: python terminal_training_client.py")
                print(f"3. 重新运行此脚本")
                break
            except Exception as e:
                print(f"\n❌ 监控出错: {e}")
                time.sleep(5)  # 等待5秒后重试
                
    except Exception as e:
        print(f"❌ 启动训练失败: {e}")

def display_detailed_status(job):
    """显示详细状态"""
    print("🎯 高精度车牌识别训练 - 实时监控")
    print("=" * 80)
    
    # 基本信息
    print(f"📋 任务信息:")
    print(f"  任务ID: {job['job_id']}")
    print(f"  实验名称: {job['config']['experiment_name']}")
    print(f"  状态: {job['status'].upper()}")
    print(f"  进度: {job['progress']:.1f}%")
    print(f"  当前轮次: {job['current_epoch']}/{job['config']['epochs']}")
    
    # 训练指标
    print(f"\n📊 训练指标:")
    print(f"  训练损失: {job['train_loss']:.4f}")
    print(f"  验证损失: {job['val_loss']:.4f}")
    print(f"  训练准确率: {job['train_acc']*100:.2f}%")
    print(f"  验证准确率: {job['val_acc']*100:.2f}%")
    print(f"  最佳准确率: {job['best_acc']*100:.2f}%")
    print(f"  当前学习率: {job['learning_rate']:.6f}")
    
    # 模型信息
    print(f"\n🔧 模型配置:")
    config = job['config']
    print(f"  模型类型: {config['model_type']}")
    print(f"  批次大小: {config['batch_size']}")
    print(f"  优化器: {config['optimizer_type']}")
    print(f"  调度器: {config['scheduler_type']}")
    print(f"  数据增强: {'✅' if config['data_augmentation'] else '❌'}")
    print(f"  早停机制: {'✅' if config['early_stopping'] else '❌'}")
    
    if job.get('model_params'):
        print(f"  模型参数: {job['model_params']:,} ({job['model_params']/1000000:.1f}M)")
    
    # 性能信息
    if job.get('epoch_times') and len(job['epoch_times']) > 0:
        avg_time = sum(job['epoch_times']) / len(job['epoch_times'])
        last_time = job['epoch_times'][-1]
        print(f"\n⏱️ 性能信息:")
        print(f"  平均轮次耗时: {avg_time:.1f}s")
        print(f"  最近轮次耗时: {last_time:.1f}s")
        
        if job['current_epoch'] > 0:
            remaining_epochs = job['config']['epochs'] - job['current_epoch']
            estimated_time = remaining_epochs * avg_time
            print(f"  预计剩余时间: {estimated_time/60:.1f}分钟")
    
    # 当前状态消息
    print(f"\n💬 状态消息: {job['message']}")
    
    # 最近日志
    if job.get('detailed_log') and len(job['detailed_log']) > 0:
        print(f"\n📝 最近训练日志:")
        recent_logs = job['detailed_log'][-3:]  # 显示最近3条
        for log in recent_logs:
            print(f"  {log}")
    
    print("\n" + "=" * 80)
    print(f"更新时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")

def display_final_results(job):
    """显示最终结果"""
    print("\n🎉 训练完成! 最终结果报告")
    print("=" * 60)
    
    print(f"📊 最终训练结果:")
    print(f"  实验名称: {job['config']['experiment_name']}")
    print(f"  模型类型: {job['config']['model_type']}")
    print(f"  完成轮次: {job['current_epoch']}/{job['config']['epochs']}")
    print(f"  最终训练损失: {job['train_loss']:.4f}")
    print(f"  最终验证损失: {job['val_loss']:.4f}")
    print(f"  最终训练准确率: {job['train_acc']*100:.2f}%")
    print(f"  最终验证准确率: {job['val_acc']*100:.2f}%")
    print(f"  🏆 最佳验证准确率: {job['best_acc']*100:.2f}%")
    
    if job.get('model_params'):
        print(f"  模型参数量: {job['model_params']:,} ({job['model_params']/1000000:.1f}M)")
    
    if job.get('epoch_times') and len(job['epoch_times']) > 0:
        avg_time = sum(job['epoch_times']) / len(job['epoch_times'])
        total_time = sum(job['epoch_times'])
        print(f"  平均轮次耗时: {avg_time:.1f}s")
        print(f"  总训练时间: {total_time:.1f}s ({total_time/60:.1f}分钟)")
    
    # 训练历史统计
    if job.get('acc_history') and len(job['acc_history']) > 0:
        max_acc = max(job['acc_history'])
        min_acc = min(job['acc_history'])
        print(f"\n📈 训练历史统计:")
        print(f"  准确率范围: {min_acc*100:.2f}% - {max_acc*100:.2f}%")
        print(f"  准确率提升: {(max_acc - min_acc)*100:.2f}%")
    
    # 显示完整训练日志
    if job.get('detailed_log') and len(job['detailed_log']) > 0:
        print(f"\n📝 完整训练日志:")
        for log in job['detailed_log']:
            print(f"  {log}")
    
    print("\n" + "=" * 60)
    print("🎯 高精度训练完成!")

if __name__ == "__main__":
    start_high_accuracy_training()
