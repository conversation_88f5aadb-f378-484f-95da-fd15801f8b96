#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
车牌识别系统优化服务器启动脚本
确保在pytorch_env环境中运行
"""

import os
import sys
import subprocess
import time
from pathlib import Path

def check_environment():
    """检查当前环境"""
    conda_env = os.environ.get('CONDA_DEFAULT_ENV')
    print("=" * 80)
    print("🚗 车牌识别系统 - 优化版启动")
    print("=" * 80)
    print(f"🐍 Python: {sys.executable}")
    print(f"🌍 当前环境: {conda_env or 'unknown'}")
    print(f"📁 工作目录: {os.getcwd()}")
    print("=" * 80)
    
    if conda_env != 'pytorch_env':
        print("⚠️  警告: 当前不在 pytorch_env 环境中")
        print("💡 请确保在 pytorch_env 环境中运行此脚本")
        print("🔧 激活命令: conda activate pytorch_env")
        print("=" * 80)
        return False
    
    print("✅ 环境检查通过")
    return True

def install_dependencies():
    """安装必要的依赖"""
    print("📦 检查依赖包...")
    
    dependencies = [
        "opencv-python",
        "pillow", 
        "fastapi",
        "uvicorn",
        "python-multipart"
    ]
    
    for dep in dependencies:
        try:
            if dep == "opencv-python":
                import cv2
                print(f"✅ {dep} 已安装")
            elif dep == "pillow":
                import PIL
                print(f"✅ {dep} 已安装")
            elif dep == "python-multipart":
                import multipart
                print(f"✅ {dep} 已安装")
            else:
                __import__(dep.replace('-', '_'))
                print(f"✅ {dep} 已安装")
        except ImportError:
            print(f"📥 安装 {dep}...")
            try:
                subprocess.run([sys.executable, "-m", "pip", "install", dep], 
                             check=True, capture_output=True)
                print(f"✅ {dep} 安装成功")
            except subprocess.CalledProcessError as e:
                print(f"❌ {dep} 安装失败")
                return False
    
    return True

def start_backend():
    """启动后端服务"""
    backend_dir = Path("src/web_app_v2/backend")
    
    if not backend_dir.exists():
        print(f"❌ 后端目录不存在: {backend_dir}")
        return False
    
    print("🚀 启动 FastAPI 后端服务...")
    
    try:
        # 切换到后端目录
        original_dir = os.getcwd()
        os.chdir(backend_dir)
        
        # 直接运行main.py
        subprocess.run([sys.executable, "main.py"], check=True)
        
    except KeyboardInterrupt:
        print("\n🛑 服务已停止")
    except subprocess.CalledProcessError as e:
        print(f"❌ 服务启动失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 发生错误: {e}")
        return False
    finally:
        os.chdir(original_dir)
    
    return True

def show_instructions():
    """显示使用说明"""
    print("\n" + "=" * 80)
    print("📱 前端启动说明")
    print("=" * 80)
    print("如果需要启动React前端，请在新终端中执行:")
    print("   cd src/web_app_v2/frontend")
    print("   npm install")
    print("   npm run dev")
    print("   然后访问: http://localhost:3000")
    print("=" * 80)
    print("🌐 后端服务地址:")
    print("   - API: http://127.0.0.1:8000")
    print("   - 文档: http://127.0.0.1:8000/api/docs")
    print("=" * 80)

def main():
    """主函数"""
    if not check_environment():
        return
    
    if not install_dependencies():
        print("❌ 依赖安装失败")
        return
    
    show_instructions()
    
    print("🚀 正在启动后端服务...")
    print("💡 按 Ctrl+C 停止服务")
    print("=" * 80)
    
    start_backend()

if __name__ == "__main__":
    main()
