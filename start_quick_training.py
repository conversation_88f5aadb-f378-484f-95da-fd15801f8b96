#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
启动快速训练测试
Quick Training Test

Author: License Plate Recognition Team
Date: 2025-07-30
"""

import requests
import json
import time

def start_quick_training():
    """启动快速训练"""
    print("🚀 启动快速训练测试")
    print("=" * 50)
    
    # 快速训练配置
    config = {
        "experiment_name": "快速训练演示",
        "model_type": "resnet18",
        "batch_size": 64,
        "learning_rate": 0.001,
        "epochs": 10,
        "dataset_size": 1000,
        "use_pretrained": True,
        "optimizer_type": "adam",
        "scheduler_type": "step",
        "data_augmentation": False,
        "early_stopping": True,
        "patience": 5,
        "weight_decay": 0.0001
    }
    
    try:
        # 启动训练
        response = requests.post('http://127.0.0.1:8007/api/training/start', json=config)
        result = response.json()
        
        print("✅ 训练已启动!")
        print(f"任务ID: {result['job_id']}")
        print(f"实验名称: {config['experiment_name']}")
        print(f"模型类型: {config['model_type']}")
        print(f"训练轮次: {config['epochs']}")
        print(f"数据集大小: {config['dataset_size']}")
        
        job_id = result['job_id']
        
        print("\n📊 开始监控训练进度...")
        print("按 Ctrl+C 停止监控")
        print("-" * 50)
        
        # 监控训练进度
        while True:
            try:
                status_response = requests.get(f'http://127.0.0.1:8007/api/training/status/{job_id}')
                job = status_response.json()
                
                print(f"\r状态: {job['status']} | 进度: {job['progress']:.1f}% | "
                      f"轮次: {job['current_epoch']}/{job['config']['epochs']} | "
                      f"验证准确率: {job['val_acc']*100:.2f}% | "
                      f"最佳: {job['best_acc']*100:.2f}%", end="")
                
                if job['status'] in ['completed', 'failed', 'stopped']:
                    print(f"\n\n🏁 训练结束! 状态: {job['status']}")
                    print(f"最终验证准确率: {job['val_acc']*100:.2f}%")
                    print(f"最佳验证准确率: {job['best_acc']*100:.2f}%")
                    
                    # 显示详细结果
                    print("\n📊 训练结果详情:")
                    print(f"  实验名称: {job['config']['experiment_name']}")
                    print(f"  模型类型: {job['config']['model_type']}")
                    print(f"  完成轮次: {job['current_epoch']}/{job['config']['epochs']}")
                    print(f"  训练损失: {job['train_loss']:.4f}")
                    print(f"  验证损失: {job['val_loss']:.4f}")
                    print(f"  训练准确率: {job['train_acc']*100:.2f}%")
                    print(f"  验证准确率: {job['val_acc']*100:.2f}%")
                    print(f"  最佳准确率: {job['best_acc']*100:.2f}%")
                    
                    if job.get('model_params'):
                        print(f"  模型参数: {job['model_params']:,} ({job['model_params']/1000000:.1f}M)")
                    
                    if job.get('epoch_times') and len(job['epoch_times']) > 0:
                        avg_time = sum(job['epoch_times']) / len(job['epoch_times'])
                        total_time = sum(job['epoch_times'])
                        print(f"  平均轮次耗时: {avg_time:.1f}s")
                        print(f"  总训练时间: {total_time:.1f}s ({total_time/60:.1f}分钟)")
                    
                    # 显示最近日志
                    if job.get('detailed_log') and len(job['detailed_log']) > 0:
                        print("\n📝 训练日志 (最近10条):")
                        recent_logs = job['detailed_log'][-10:]
                        for log in recent_logs:
                            print(f"  {log}")
                    
                    break
                
                time.sleep(2)
                
            except KeyboardInterrupt:
                print(f"\n\n⏹️ 监控已停止")
                print(f"训练任务 {job_id} 仍在后台运行")
                print("可以通过网页界面 http://127.0.0.1:8007 继续监控")
                break
            except Exception as e:
                print(f"\n❌ 监控出错: {e}")
                break
                
    except Exception as e:
        print(f"❌ 启动训练失败: {e}")

if __name__ == "__main__":
    start_quick_training()
