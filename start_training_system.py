#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
启动训练系统
Start Training System

启动基于API的CCPD训练系统

Author: License Plate Recognition Team
Date: 2025-07-30
"""

import os
import sys
import subprocess
import time
import threading
from pathlib import Path

def start_api_server():
    """启动API服务器"""
    print("正在启动训练API服务器...")
    
    # 切换到项目目录
    project_dir = Path(__file__).parent
    os.chdir(project_dir)
    
    # 启动API服务器
    cmd = [sys.executable, "src/training/training_api_server.py"]
    
    try:
        process = subprocess.Popen(cmd, 
                                 stdout=subprocess.PIPE, 
                                 stderr=subprocess.PIPE,
                                 text=True)
        
        print(f"API服务器已启动，PID: {process.pid}")
        print("API地址: http://127.0.0.1:8001")
        print("API文档: http://127.0.0.1:8001/docs")
        
        return process
        
    except Exception as e:
        print(f"启动API服务器失败: {e}")
        return None

def start_training_client():
    """启动训练客户端"""
    print("\n等待API服务器启动...")
    time.sleep(5)  # 等待服务器启动
    
    print("正在启动训练客户端...")
    
    # 切换到项目目录
    project_dir = Path(__file__).parent
    os.chdir(project_dir)
    
    # 启动训练客户端
    cmd = [sys.executable, "src/training/training_client.py"]
    
    try:
        process = subprocess.Popen(cmd)
        print(f"训练客户端已启动，PID: {process.pid}")
        return process
        
    except Exception as e:
        print(f"启动训练客户端失败: {e}")
        return None

def main():
    """主函数"""
    print("=" * 60)
    print("CCPD车牌识别训练系统")
    print("基于API的分布式训练架构")
    print("=" * 60)
    
    # 检查环境
    print("检查Python环境...")
    print(f"Python版本: {sys.version}")
    print(f"工作目录: {os.getcwd()}")
    
    # 检查必要的包
    required_packages = ['torch', 'torchvision', 'fastapi', 'uvicorn', 'requests', 
                        'matplotlib', 'seaborn', 'pandas', 'opencv-python', 'pillow']
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"✓ {package}")
        except ImportError:
            missing_packages.append(package)
            print(f"✗ {package} (缺失)")
    
    if missing_packages:
        print(f"\n警告：缺少以下包: {', '.join(missing_packages)}")
        print("请使用以下命令安装：")
        print(f"pip install {' '.join(missing_packages)}")
        
        response = input("\n是否继续启动系统？(y/n): ")
        if response.lower() != 'y':
            return
    
    print("\n" + "=" * 60)
    
    # 启动API服务器
    api_process = start_api_server()
    if not api_process:
        print("无法启动API服务器，退出")
        return
    
    try:
        # 在单独线程中启动客户端
        client_thread = threading.Thread(target=start_training_client)
        client_thread.daemon = True
        client_thread.start()
        
        print("\n系统已启动！")
        print("=" * 60)
        print("可用的操作：")
        print("1. 访问 http://127.0.0.1:8001/docs 查看API文档")
        print("2. 训练客户端将自动开始训练演示")
        print("3. 按 Ctrl+C 停止系统")
        print("=" * 60)
        
        # 等待用户中断
        api_process.wait()
        
    except KeyboardInterrupt:
        print("\n正在停止系统...")
        
        # 终止API服务器
        if api_process:
            api_process.terminate()
            api_process.wait()
        
        print("系统已停止")

if __name__ == "__main__":
    main()
