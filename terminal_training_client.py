#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
终端版训练客户端
Terminal Training Client

用于测试完整车牌识别训练系统的终端界面

Author: License Plate Recognition Team
Date: 2025-07-30
"""

import requests
import time
import json
import os
from datetime import datetime
from typing import Dict, Any, List

class TerminalTrainingClient:
    """终端训练客户端"""
    
    def __init__(self, base_url: str = "http://127.0.0.1:8007"):
        self.base_url = base_url
        self.session = requests.Session()
        
    def print_header(self):
        """打印标题"""
        print("=" * 80)
        print("🚗 车牌识别训练系统 - 终端客户端")
        print("=" * 80)
        
    def test_connection(self) -> bool:
        """测试连接"""
        try:
            response = self.session.get(f"{self.base_url}/api/test")
            if response.status_code == 200:
                data = response.json()
                print(f"✅ 连接成功!")
                print(f"   服务器版本: {data.get('version', 'N/A')}")
                print(f"   PyTorch版本: {data.get('pytorch_version', 'N/A')}")
                print(f"   计算设备: {data.get('device', 'N/A')}")
                print(f"   CUDA支持: {'✅' if data.get('cuda_available') else '❌'}")
                return True
            else:
                print(f"❌ 连接失败: HTTP {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 连接失败: {e}")
            return False
    
    def get_training_presets(self) -> Dict[str, Dict]:
        """获取训练预设"""
        return {
            "quick": {
                "experiment_name": "快速训练测试",
                "model_type": "resnet18",
                "batch_size": 64,
                "learning_rate": 0.001,
                "epochs": 20,
                "dataset_size": 2000,
                "use_pretrained": True,
                "optimizer_type": "adam",
                "scheduler_type": "step",
                "data_augmentation": False,
                "early_stopping": True,
                "patience": 5,
                "weight_decay": 0.0001
            },
            "standard": {
                "experiment_name": "标准训练",
                "model_type": "resnet50",
                "batch_size": 32,
                "learning_rate": 0.001,
                "epochs": 50,
                "dataset_size": 5000,
                "use_pretrained": True,
                "optimizer_type": "adamw",
                "scheduler_type": "cosine",
                "data_augmentation": True,
                "early_stopping": True,
                "patience": 10,
                "weight_decay": 0.0001
            },
            "high_accuracy": {
                "experiment_name": "高精度训练",
                "model_type": "resnet50",
                "batch_size": 16,
                "learning_rate": 0.0005,
                "epochs": 100,
                "dataset_size": 10000,
                "use_pretrained": True,
                "optimizer_type": "adamw",
                "scheduler_type": "cosine",
                "data_augmentation": True,
                "early_stopping": True,
                "patience": 15,
                "weight_decay": 0.0001
            }
        }
    
    def start_training(self, config: Dict[str, Any]) -> str:
        """启动训练"""
        try:
            response = self.session.post(
                f"{self.base_url}/api/training/start",
                json=config,
                headers={"Content-Type": "application/json"}
            )
            
            if response.status_code == 200:
                data = response.json()
                job_id = data["job_id"]
                print(f"✅ 训练任务已启动!")
                print(f"   任务ID: {job_id}")
                print(f"   实验名称: {config['experiment_name']}")
                print(f"   模型类型: {config['model_type']}")
                print(f"   训练轮次: {config['epochs']}")
                return job_id
            else:
                print(f"❌ 启动训练失败: HTTP {response.status_code}")
                print(f"   错误信息: {response.text}")
                return ""
                
        except Exception as e:
            print(f"❌ 启动训练失败: {e}")
            return ""
    
    def get_job_status(self, job_id: str) -> Dict[str, Any]:
        """获取任务状态"""
        try:
            response = self.session.get(f"{self.base_url}/api/training/status/{job_id}")
            if response.status_code == 200:
                return response.json()
            else:
                print(f"❌ 获取状态失败: HTTP {response.status_code}")
                return {}
        except Exception as e:
            print(f"❌ 获取状态失败: {e}")
            return {}
    
    def list_jobs(self) -> List[Dict[str, Any]]:
        """列出所有任务"""
        try:
            response = self.session.get(f"{self.base_url}/api/training/jobs")
            if response.status_code == 200:
                return response.json()["jobs"]
            else:
                print(f"❌ 获取任务列表失败: HTTP {response.status_code}")
                return []
        except Exception as e:
            print(f"❌ 获取任务列表失败: {e}")
            return []
    
    def stop_training(self, job_id: str) -> bool:
        """停止训练"""
        try:
            response = self.session.post(f"{self.base_url}/api/training/stop/{job_id}")
            if response.status_code == 200:
                data = response.json()
                print(f"✅ {data['message']}")
                return True
            else:
                print(f"❌ 停止训练失败: HTTP {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 停止训练失败: {e}")
            return False
    
    def monitor_training(self, job_id: str, refresh_interval: int = 3):
        """监控训练进度"""
        print(f"\n📊 开始监控训练任务: {job_id}")
        print("按 Ctrl+C 停止监控")
        print("-" * 80)
        
        try:
            while True:
                job = self.get_job_status(job_id)
                if not job:
                    print("❌ 无法获取任务状态")
                    break
                
                # 清屏并显示状态
                os.system('cls' if os.name == 'nt' else 'clear')
                self.display_job_details(job)
                
                # 检查是否完成
                if job["status"] in ["completed", "failed", "stopped"]:
                    print(f"\n🏁 训练已结束，状态: {job['status']}")
                    break
                
                time.sleep(refresh_interval)
                
        except KeyboardInterrupt:
            print(f"\n⏹️ 监控已停止")
    
    def display_job_details(self, job: Dict[str, Any]):
        """显示任务详情"""
        print("=" * 80)
        print(f"🚗 训练监控 - {job['config']['experiment_name']}")
        print("=" * 80)
        
        # 基本信息
        print(f"任务ID: {job['job_id']}")
        print(f"状态: {job['status'].upper()}")
        print(f"进度: {job['progress']:.1f}%")
        print(f"当前轮次: {job['current_epoch']}/{job['config']['epochs']}")
        
        # 训练指标
        print("\n📊 训练指标:")
        print(f"  训练损失: {job['train_loss']:.4f}")
        print(f"  验证损失: {job['val_loss']:.4f}")
        print(f"  训练准确率: {job['train_acc']:.4f} ({job['train_acc']*100:.2f}%)")
        print(f"  验证准确率: {job['val_acc']:.4f} ({job['val_acc']*100:.2f}%)")
        print(f"  最佳准确率: {job['best_acc']:.4f} ({job['best_acc']*100:.2f}%)")
        print(f"  当前学习率: {job['learning_rate']:.6f}")
        
        # 模型信息
        print("\n🔧 模型配置:")
        config = job['config']
        print(f"  模型类型: {config['model_type']}")
        print(f"  批次大小: {config['batch_size']}")
        print(f"  优化器: {config['optimizer_type']}")
        print(f"  调度器: {config['scheduler_type']}")
        print(f"  数据增强: {'✅' if config['data_augmentation'] else '❌'}")
        print(f"  早停机制: {'✅' if config['early_stopping'] else '❌'}")
        
        if job.get('model_params'):
            print(f"  模型参数: {job['model_params']:,} ({job['model_params']/1000000:.1f}M)")
        
        # 性能信息
        if job.get('epoch_times') and len(job['epoch_times']) > 0:
            avg_time = sum(job['epoch_times']) / len(job['epoch_times'])
            last_time = job['epoch_times'][-1]
            print(f"\n⏱️ 性能信息:")
            print(f"  平均轮次耗时: {avg_time:.1f}s")
            print(f"  最近轮次耗时: {last_time:.1f}s")
            
            if job['current_epoch'] > 0:
                remaining_epochs = job['config']['epochs'] - job['current_epoch']
                estimated_time = remaining_epochs * avg_time
                print(f"  预计剩余时间: {estimated_time/60:.1f}分钟")
        
        # 当前状态消息
        print(f"\n💬 状态消息: {job['message']}")
        
        # 最近日志
        if job.get('detailed_log') and len(job['detailed_log']) > 0:
            print("\n📝 最近日志:")
            recent_logs = job['detailed_log'][-5:]  # 显示最近5条
            for log in recent_logs:
                print(f"  {log}")
        
        print("\n" + "=" * 80)
        print(f"更新时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    def run_interactive_mode(self):
        """运行交互模式"""
        self.print_header()
        
        if not self.test_connection():
            print("❌ 无法连接到训练服务器，请确保服务器已启动")
            return
        
        presets = self.get_training_presets()
        
        while True:
            print("\n" + "=" * 50)
            print("🎛️ 训练控制菜单")
            print("=" * 50)
            print("1. 快速训练 (ResNet18, 20轮)")
            print("2. 标准训练 (ResNet50, 50轮)")
            print("3. 高精度训练 (ResNet50, 100轮)")
            print("4. 自定义训练")
            print("5. 查看所有任务")
            print("6. 监控训练任务")
            print("7. 停止训练任务")
            print("0. 退出")
            print("-" * 50)
            
            choice = input("请选择操作 (0-7): ").strip()
            
            if choice == "0":
                print("👋 再见!")
                break
            elif choice == "1":
                job_id = self.start_training(presets["quick"])
                if job_id:
                    self.monitor_training(job_id)
            elif choice == "2":
                job_id = self.start_training(presets["standard"])
                if job_id:
                    self.monitor_training(job_id)
            elif choice == "3":
                job_id = self.start_training(presets["high_accuracy"])
                if job_id:
                    self.monitor_training(job_id)
            elif choice == "4":
                self.custom_training_wizard()
            elif choice == "5":
                self.show_all_jobs()
            elif choice == "6":
                self.monitor_job_wizard()
            elif choice == "7":
                self.stop_job_wizard()
            else:
                print("❌ 无效选择，请重试")
    
    def custom_training_wizard(self):
        """自定义训练向导"""
        print("\n🔧 自定义训练配置")
        print("-" * 30)
        
        try:
            config = {}
            config["experiment_name"] = input("实验名称 [自定义训练]: ").strip() or "自定义训练"
            
            print("模型类型: 1=ResNet18, 2=ResNet50, 3=EfficientNet")
            model_choice = input("选择模型 [2]: ").strip() or "2"
            model_map = {"1": "resnet18", "2": "resnet50", "3": "efficientnet"}
            config["model_type"] = model_map.get(model_choice, "resnet50")
            
            config["batch_size"] = int(input("批次大小 [32]: ").strip() or "32")
            config["learning_rate"] = float(input("学习率 [0.001]: ").strip() or "0.001")
            config["epochs"] = int(input("训练轮次 [50]: ").strip() or "50")
            config["dataset_size"] = int(input("数据集大小 [5000]: ").strip() or "5000")
            
            config["use_pretrained"] = input("使用预训练模型? [y/n, 默认y]: ").strip().lower() != "n"
            config["data_augmentation"] = input("启用数据增强? [y/n, 默认y]: ").strip().lower() != "n"
            config["early_stopping"] = input("启用早停? [y/n, 默认y]: ").strip().lower() != "n"
            
            if config["early_stopping"]:
                config["patience"] = int(input("早停耐心值 [10]: ").strip() or "10")
            else:
                config["patience"] = 10
            
            print("优化器: 1=Adam, 2=AdamW, 3=SGD")
            opt_choice = input("选择优化器 [2]: ").strip() or "2"
            opt_map = {"1": "adam", "2": "adamw", "3": "sgd"}
            config["optimizer_type"] = opt_map.get(opt_choice, "adamw")
            
            print("调度器: 1=余弦退火, 2=阶梯衰减, 3=指数衰减")
            sched_choice = input("选择调度器 [1]: ").strip() or "1"
            sched_map = {"1": "cosine", "2": "step", "3": "exponential"}
            config["scheduler_type"] = sched_map.get(sched_choice, "cosine")
            
            config["weight_decay"] = float(input("权重衰减 [0.0001]: ").strip() or "0.0001")
            
            print(f"\n📋 配置摘要:")
            print(f"  实验: {config['experiment_name']}")
            print(f"  模型: {config['model_type']}")
            print(f"  轮次: {config['epochs']}")
            print(f"  批次: {config['batch_size']}")
            print(f"  学习率: {config['learning_rate']}")
            
            confirm = input("\n确认启动训练? [y/n]: ").strip().lower()
            if confirm == "y":
                job_id = self.start_training(config)
                if job_id:
                    self.monitor_training(job_id)
            else:
                print("❌ 训练已取消")
                
        except (ValueError, KeyboardInterrupt):
            print("❌ 配置无效或已取消")
    
    def show_all_jobs(self):
        """显示所有任务"""
        jobs = self.list_jobs()
        if not jobs:
            print("📝 暂无训练任务")
            return
        
        print(f"\n📋 所有训练任务 ({len(jobs)}个)")
        print("-" * 80)
        
        for job in jobs:
            status_emoji = {"pending": "⏳", "running": "🔄", "completed": "✅", "failed": "❌", "stopped": "⏹️"}
            emoji = status_emoji.get(job["status"], "❓")
            
            print(f"{emoji} {job['job_id']} - {job['config']['experiment_name']}")
            print(f"   状态: {job['status']} | 进度: {job['progress']:.1f}% | 轮次: {job['current_epoch']}/{job['config']['epochs']}")
            print(f"   验证准确率: {job['val_acc']*100:.2f}% | 最佳: {job['best_acc']*100:.2f}%")
            print()
    
    def monitor_job_wizard(self):
        """监控任务向导"""
        jobs = self.list_jobs()
        if not jobs:
            print("📝 暂无训练任务")
            return
        
        print("\n📊 选择要监控的任务:")
        for i, job in enumerate(jobs):
            print(f"{i+1}. {job['job_id']} - {job['config']['experiment_name']} ({job['status']})")
        
        try:
            choice = int(input("请选择任务编号: ").strip()) - 1
            if 0 <= choice < len(jobs):
                self.monitor_training(jobs[choice]['job_id'])
            else:
                print("❌ 无效选择")
        except (ValueError, KeyboardInterrupt):
            print("❌ 无效输入")
    
    def stop_job_wizard(self):
        """停止任务向导"""
        jobs = [job for job in self.list_jobs() if job["status"] == "running"]
        if not jobs:
            print("📝 暂无正在运行的任务")
            return
        
        print("\n⏹️ 选择要停止的任务:")
        for i, job in enumerate(jobs):
            print(f"{i+1}. {job['job_id']} - {job['config']['experiment_name']}")
        
        try:
            choice = int(input("请选择任务编号: ").strip()) - 1
            if 0 <= choice < len(jobs):
                self.stop_training(jobs[choice]['job_id'])
            else:
                print("❌ 无效选择")
        except (ValueError, KeyboardInterrupt):
            print("❌ 无效输入")

def main():
    """主函数"""
    client = TerminalTrainingClient()
    client.run_interactive_mode()

if __name__ == "__main__":
    main()
