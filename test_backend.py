#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试后端服务是否可以正常启动
"""

import os
import sys
from pathlib import Path

def test_imports():
    """测试导入"""
    print("🔍 测试模块导入...")
    
    try:
        import fastapi
        print("✅ FastAPI 导入成功")
    except ImportError as e:
        print(f"❌ FastAPI 导入失败: {e}")
        return False
    
    try:
        import uvicorn
        print("✅ Uvicorn 导入成功")
    except ImportError as e:
        print(f"❌ Uvicorn 导入失败: {e}")
        return False
    
    try:
        import cv2
        print("✅ OpenCV 导入成功")
    except ImportError as e:
        print(f"⚠️ OpenCV 导入失败: {e}")
        print("💡 将使用模拟模式")
    
    return True

def test_backend():
    """测试后端"""
    print("=" * 60)
    print("🧪 车牌识别系统后端测试")
    print("=" * 60)
    print(f"🐍 Python: {sys.executable}")
    print(f"🌍 环境: {os.environ.get('CONDA_DEFAULT_ENV', 'unknown')}")
    print("=" * 60)
    
    if not test_imports():
        return False
    
    # 测试后端main.py
    backend_dir = Path("src/web_app_v2/backend")
    if not backend_dir.exists():
        print(f"❌ 后端目录不存在: {backend_dir}")
        return False
    
    main_file = backend_dir / "main.py"
    if not main_file.exists():
        print(f"❌ main.py 不存在: {main_file}")
        return False
    
    print("✅ 后端文件检查通过")
    
    # 尝试导入后端模块
    try:
        sys.path.insert(0, str(backend_dir))
        import main
        print("✅ 后端模块导入成功")
        
        # 检查FastAPI应用
        if hasattr(main, 'app'):
            print("✅ FastAPI 应用对象存在")
            return True
        else:
            print("❌ FastAPI 应用对象不存在")
            return False
            
    except Exception as e:
        print(f"❌ 后端模块导入失败: {e}")
        return False

def main():
    """主函数"""
    if test_backend():
        print("=" * 60)
        print("🎉 后端测试通过！")
        print("💡 可以尝试启动完整服务")
        print("=" * 60)
    else:
        print("=" * 60)
        print("❌ 后端测试失败！")
        print("💡 请检查环境和依赖")
        print("=" * 60)

if __name__ == "__main__":
    main()
