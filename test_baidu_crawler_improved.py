# -*- coding: utf-8 -*-
"""
测试改进后的百度图片爬虫
Test Improved Baidu Image Crawler

验证使用真实百度图片搜索URL格式的爬虫功能
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.utils.chinese_support import setup_chinese_environment
from src.data_collection.baidu_image_crawler import BaiduImageCrawler


def test_baidu_search_url():
    """测试百度图片搜索URL格式"""
    print("=" * 60)
    print("测试百度图片搜索URL格式")
    print("=" * 60)
    
    # 设置中文环境
    setup_chinese_environment()
    
    # 创建爬虫实例
    crawler = BaiduImageCrawler(
        output_dir="data/test_baidu_improved",
        max_workers=4
    )
    
    # 测试关键词
    test_keywords = [
        "车牌",
        "汽车牌照", 
        "license plate",
        "蓝牌车",
        "新能源车牌"
    ]
    
    print("测试搜索功能...")
    for keyword in test_keywords:
        print(f"\n搜索关键词: {keyword}")
        
        try:
            # 搜索图片
            image_urls = crawler.search_baidu_images(keyword, max_images=20)
            
            print(f"找到 {len(image_urls)} 张图片")
            
            if len(image_urls) > 0:
                print("✓ 搜索成功")
                
                # 显示前3个结果的详细信息
                for i, img_info in enumerate(image_urls[:3]):
                    print(f"  图片 {i+1}:")
                    print(f"    缩略图: {img_info.get('thumb_url', 'N/A')[:80]}...")
                    print(f"    中等尺寸: {img_info.get('middle_url', 'N/A')[:80]}...")
                    print(f"    原图: {img_info.get('original_url', 'N/A')[:80]}...")
                    print(f"    标题: {img_info.get('title', 'N/A')[:50]}...")
                    print(f"    尺寸: {img_info.get('width', 0)}x{img_info.get('height', 0)}")
                    print(f"    方法: {img_info.get('method', 'json')}")
                    print()
            else:
                print("✗ 搜索失败或无结果")
                
        except Exception as e:
            print(f"✗ 搜索出错: {str(e)}")
    
    return True


def test_download_functionality():
    """测试下载功能"""
    print("=" * 60)
    print("测试下载功能")
    print("=" * 60)
    
    # 创建爬虫实例
    crawler = BaiduImageCrawler(
        output_dir="data/test_baidu_download",
        max_workers=2
    )
    
    try:
        # 小规模测试下载
        print("开始小规模下载测试...")
        stats = crawler.crawl_license_plates(target_images=10)
        
        print(f"下载统计: {stats}")
        
        if stats['successful_downloads'] > 0:
            print("✓ 下载功能正常")
            
            # 检查下载的文件
            download_dir = Path("data/test_baidu_download/images")
            if download_dir.exists():
                downloaded_files = list(download_dir.glob("*.jpg"))
                print(f"实际下载文件数: {len(downloaded_files)}")
                
                if len(downloaded_files) > 0:
                    print("下载的文件示例:")
                    for i, file_path in enumerate(downloaded_files[:3]):
                        file_size = file_path.stat().st_size
                        print(f"  {file_path.name}: {file_size} bytes")
            
            return True
        else:
            print("✗ 下载功能异常")
            return False
            
    except Exception as e:
        print(f"✗ 下载测试失败: {str(e)}")
        return False


def test_url_format_compatibility():
    """测试URL格式兼容性"""
    print("=" * 60)
    print("测试URL格式兼容性")
    print("=" * 60)
    
    from urllib.parse import quote
    
    # 测试您提供的URL格式
    test_keyword = "车牌"
    encoded_keyword = quote(test_keyword)
    
    # 构建测试URL
    test_urls = [
        # 原始JSON API格式
        f"https://image.baidu.com/search/acjson?tn=resultjson_com&logid=&ipn=rj&ct=201326592&is=&fp=result&queryWord={encoded_keyword}&cl=2&lm=-1&ie=utf-8&oe=utf-8&word={encoded_keyword}&pn=0&rn=30",
        
        # 您提供的HTML页面格式
        f"https://image.baidu.com/search/index?tn=baiduimage&ipn=r&ct=201326592&cl=2&lm=&st=-1&fm=index&fr=&hs=0&xthttps=111110&sf=1&fmq=&pv=&ic=0&nc=1&z=&se=&showtab=0&fb=0&width=&height=&face=0&istype=2&ie=utf-8&word={encoded_keyword}"
    ]
    
    print("测试URL格式:")
    for i, url in enumerate(test_urls):
        print(f"URL {i+1}: {url[:100]}...")
    
    # 测试实际访问
    import requests
    
    crawler = BaiduImageCrawler(output_dir="data/test_url_format")
    headers = crawler.get_random_headers()
    
    for i, url in enumerate(test_urls):
        try:
            print(f"\n测试URL {i+1}...")
            response = requests.get(url, headers=headers, timeout=10)
            print(f"状态码: {response.status_code}")
            print(f"响应长度: {len(response.text)} 字符")
            
            if response.status_code == 200:
                print("✓ URL访问成功")
                
                # 检查响应内容类型
                content_type = response.headers.get('content-type', '')
                print(f"内容类型: {content_type}")
                
                if 'json' in content_type:
                    try:
                        data = response.json()
                        if 'data' in data:
                            print(f"JSON数据项数: {len(data['data'])}")
                        print("✓ JSON格式解析成功")
                    except:
                        print("✗ JSON格式解析失败")
                elif 'html' in content_type:
                    print("✓ HTML格式响应")
                    # 检查是否包含图片相关内容
                    if 'objURL' in response.text or 'thumbURL' in response.text:
                        print("✓ 包含图片URL信息")
                    else:
                        print("? 未明确检测到图片URL信息")
            else:
                print(f"✗ URL访问失败，状态码: {response.status_code}")
                
        except Exception as e:
            print(f"✗ URL测试出错: {str(e)}")
    
    return True


def main():
    """主测试函数"""
    print("开始测试改进后的百度图片爬虫")
    print("使用真实百度图片搜索URL格式")
    print("=" * 80)
    
    test_results = []
    
    # 测试1: URL格式兼容性
    test_results.append(("URL格式兼容性", test_url_format_compatibility()))
    
    # 测试2: 搜索功能
    test_results.append(("百度图片搜索", test_baidu_search_url()))
    
    # 测试3: 下载功能
    test_results.append(("图片下载功能", test_download_functionality()))
    
    # 输出测试结果
    print("\n" + "=" * 80)
    print("测试结果汇总")
    print("=" * 80)
    
    passed_tests = 0
    total_tests = len(test_results)
    
    for test_name, result in test_results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed_tests += 1
    
    print(f"\n总体结果: {passed_tests}/{total_tests} 测试通过")
    
    if passed_tests == total_tests:
        print("🎉 所有测试通过！改进后的百度图片爬虫工作正常。")
        print("\n建议下一步:")
        print("1. 运行完整数据采集: python src/data_collection/run_data_collection.py --mode baidu --target 1000")
        print("2. 或运行完整流程: python src/data_collection/run_data_collection.py --mode full")
    else:
        print("⚠️  部分测试失败，可能需要进一步调整爬虫策略。")
        print("这可能是由于百度的反爬虫机制或网络连接问题。")
    
    return passed_tests == total_tests


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
