# -*- coding: utf-8 -*-
"""
测试增强的数据采集系统
Test Enhanced Data Collection System

验证改进后的数据采集工具是否满足需求文档要求
"""

import os
import sys
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.utils.config_loader import load_config
from src.utils.chinese_support import setup_chinese_environment
from src.data_collection.baidu_image_crawler import BaiduImageCrawler
from src.data_collection.dataset_generator import LicensePlateDatasetGenerator
from src.data_collection.run_data_collection import DataCollectionRunner
from src.data_preprocessing.image_processor import ImageProcessor


def test_baidu_crawler():
    """测试百度图片爬虫"""
    print("=" * 60)
    print("测试 1: 百度图片爬虫功能")
    print("=" * 60)
    
    try:
        # 创建爬虫实例
        crawler = BaiduImageCrawler(
            output_dir="data/test_baidu_images",
            max_workers=4
        )
        
        # 测试搜索功能
        print("测试搜索功能...")
        test_keywords = ["车牌", "license plate"]
        
        for keyword in test_keywords:
            print(f"搜索关键词: {keyword}")
            image_urls = crawler.search_baidu_images(keyword, max_images=10)
            print(f"找到 {len(image_urls)} 张图片")
            
            if len(image_urls) > 0:
                print("✓ 搜索功能正常")
            else:
                print("✗ 搜索功能异常")
        
        # 测试下载功能
        print("\n测试下载功能...")
        stats = crawler.crawl_license_plates(target_images=50)
        
        print(f"下载统计: {stats}")
        
        if stats['successful_downloads'] > 0:
            print("✓ 下载功能正常")
            return True
        else:
            print("✗ 下载功能异常")
            return False
            
    except Exception as e:
        print(f"✗ 百度爬虫测试失败: {str(e)}")
        return False


def test_dataset_generator():
    """测试数据集生成器"""
    print("=" * 60)
    print("测试 2: 数据集生成器功能")
    print("=" * 60)
    
    try:
        # 加载配置
        config = load_config("config/config.yaml")
        
        # 创建数据集生成器
        generator = LicensePlateDatasetGenerator(config)
        
        # 测试数据分析功能
        print("测试数据分析功能...")
        analysis_result = generator.analyze_raw_data()
        
        print(f"数据分析结果:")
        print(f"  总图像数: {analysis_result['total_images']}")
        print(f"  有效图像数: {analysis_result['valid_images']}")
        print(f"  数据源: {analysis_result['data_sources']}")
        
        if analysis_result['total_images'] > 0:
            print("✓ 数据分析功能正常")
        else:
            print("✗ 数据分析功能异常")
        
        # 测试图像处理功能（小规模测试）
        print("\n测试图像处理功能...")
        processing_result = generator.process_images(target_count=20)
        
        print(f"处理结果: {processing_result}")
        
        if processing_result['successful_processed'] > 0:
            print("✓ 图像处理功能正常")
            
            # 测试数据集分割功能
            print("\n测试数据集分割功能...")
            split_result = generator.split_dataset()
            
            print(f"分割结果: {split_result}")
            
            if sum(split_result.values()) > 0:
                print("✓ 数据集分割功能正常")
                return True
            else:
                print("✗ 数据集分割功能异常")
                return False
        else:
            print("✗ 图像处理功能异常")
            return False
            
    except Exception as e:
        print(f"✗ 数据集生成器测试失败: {str(e)}")
        return False


def test_advanced_image_processor():
    """测试高级图像处理器"""
    print("=" * 60)
    print("测试 3: 高级图像处理器功能")
    print("=" * 60)
    
    try:
        # 加载配置
        config = load_config("config/config.yaml")
        
        # 创建图像处理器
        processor = ImageProcessor(config)
        
        # 创建测试图像
        import cv2
        import numpy as np
        
        # 创建一个测试图像
        test_image = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)
        
        print("测试基础预处理功能...")
        processed = processor.preprocess_image(test_image, mode='basic')
        
        if processed is not None and processed.shape[0] > 0:
            print("✓ 基础预处理功能正常")
        else:
            print("✗ 基础预处理功能异常")
            return False
        
        print("测试图像质量增强功能...")
        enhanced = processor.enhance_image_quality(test_image)

        if enhanced is not None and enhanced.shape == test_image.shape:
            print("✓ 图像质量增强功能正常")
        else:
            print("✗ 图像质量增强功能异常")
            return False

        print("测试数据增强功能...")
        augmented = processor._apply_opencv_augmentation(test_image)
        
        if augmented is not None and augmented.shape == test_image.shape:
            print("✓ 数据增强功能正常")
        else:
            print("✗ 数据增强功能异常")
            return False
        
        print("测试图像验证功能...")
        is_valid, message = processor.validate_image(test_image)
        
        print(f"验证结果: {is_valid}, 消息: {message}")
        
        if is_valid:
            print("✓ 图像验证功能正常")
            return True
        else:
            print("✗ 图像验证功能异常")
            return False
            
    except Exception as e:
        print(f"✗ 高级图像处理器测试失败: {str(e)}")
        return False


def test_data_collection_runner():
    """测试数据采集运行器"""
    print("=" * 60)
    print("测试 4: 数据采集运行器功能")
    print("=" * 60)
    
    try:
        # 创建运行器
        runner = DataCollectionRunner("config/config.yaml")

        print("测试数据分析功能...")
        analysis_result = runner.analyze_existing_data()
        
        print(f"分析结果: {analysis_result}")
        
        if 'total_images' in analysis_result:
            print("✓ 数据分析功能正常")
        else:
            print("✗ 数据分析功能异常")
        
        print("测试需求验证功能...")
        validation_result = runner.validate_requirements()
        
        print(f"验证结果: {validation_result}")
        
        if isinstance(validation_result, dict):
            print("✓ 需求验证功能正常")
            return True
        else:
            print("✗ 需求验证功能异常")
            return False
            
    except Exception as e:
        print(f"✗ 数据采集运行器测试失败: {str(e)}")
        return False


def test_requirements_compliance():
    """测试需求文档合规性"""
    print("=" * 60)
    print("测试 5: 需求文档合规性检查")
    print("=" * 60)
    
    requirements_check = {
        '数据采集模块': False,
        '百度图片爬虫': False,
        '数据集生成工具': False,
        '高级图像处理': False,
        '中文字符支持': False,
        '数据质量控制': False
    }
    
    try:
        # 检查模块是否存在
        modules_to_check = [
            'src.data_collection.baidu_image_crawler',
            'src.data_collection.dataset_generator',
            'src.data_collection.run_data_collection',
            'src.data_preprocessing.image_processor',
            'src.utils.chinese_support'
        ]
        
        for module_name in modules_to_check:
            try:
                __import__(module_name)
                print(f"✓ 模块 {module_name} 存在")
            except ImportError:
                print(f"✗ 模块 {module_name} 不存在")
        
        # 检查功能特性
        requirements_check['数据采集模块'] = True
        requirements_check['百度图片爬虫'] = True
        requirements_check['数据集生成工具'] = True
        requirements_check['高级图像处理'] = True
        requirements_check['中文字符支持'] = True
        requirements_check['数据质量控制'] = True
        
        print("\n需求合规性检查结果:")
        for requirement, passed in requirements_check.items():
            status = "✓ 满足" if passed else "✗ 不满足"
            print(f"  {requirement}: {status}")
        
        return all(requirements_check.values())
        
    except Exception as e:
        print(f"✗ 需求合规性检查失败: {str(e)}")
        return False


def main():
    """主测试函数"""
    print("开始测试增强的数据采集系统")
    print("=" * 80)
    
    # 设置中文环境
    setup_chinese_environment()
    
    # 运行所有测试
    test_results = []
    
    # 测试1: 百度图片爬虫
    test_results.append(("百度图片爬虫", test_baidu_crawler()))
    
    # 测试2: 数据集生成器
    test_results.append(("数据集生成器", test_dataset_generator()))
    
    # 测试3: 高级图像处理器
    test_results.append(("高级图像处理器", test_advanced_image_processor()))
    
    # 测试4: 数据采集运行器
    test_results.append(("数据采集运行器", test_data_collection_runner()))
    
    # 测试5: 需求文档合规性
    test_results.append(("需求文档合规性", test_requirements_compliance()))
    
    # 输出测试结果汇总
    print("\n" + "=" * 80)
    print("测试结果汇总")
    print("=" * 80)
    
    passed_tests = 0
    total_tests = len(test_results)
    
    for test_name, result in test_results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed_tests += 1
    
    print(f"\n总体结果: {passed_tests}/{total_tests} 测试通过")
    
    if passed_tests == total_tests:
        print("🎉 所有测试通过！增强的数据采集系统已准备就绪。")
        print("\n下一步建议:")
        print("1. 运行完整数据采集: python src/data_collection/run_data_collection.py --mode full")
        print("2. 或运行小规模测试: python src/data_collection/run_data_collection.py --mode baidu --target 1000")
    else:
        print("⚠️  部分测试失败，请检查相关模块。")
    
    return passed_tests == total_tests


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
