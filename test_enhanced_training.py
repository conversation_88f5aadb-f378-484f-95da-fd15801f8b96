#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版训练API测试客户端
Enhanced Training API Test Client

测试高精度车牌识别训练功能

Author: License Plate Recognition Team
Date: 2025-07-30
"""

import requests
import time
import json
from typing import Dict, Any

class EnhancedTrainingClient:
    """增强版训练API客户端"""
    
    def __init__(self, api_base_url: str = "http://127.0.0.1:8005"):
        self.api_base_url = api_base_url
        self.session = requests.Session()
    
    def test_connection(self) -> Dict[str, Any]:
        """测试API连接"""
        try:
            response = self.session.get(f"{self.api_base_url}/api/test")
            response.raise_for_status()
            return response.json()
        except Exception as e:
            return {"error": str(e)}
    
    def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态"""
        try:
            response = self.session.get(f"{self.api_base_url}/api/status")
            response.raise_for_status()
            return response.json()
        except Exception as e:
            return {"error": str(e)}
    
    def start_high_accuracy_training(self) -> str:
        """启动高精度训练"""
        config = {
            "experiment_name": "高精度车牌识别训练-ResNet50",
            "model_type": "resnet50",
            "batch_size": 32,
            "learning_rate": 0.0005,
            "epochs": 100,
            "dataset_size": 10000,
            "use_pretrained": True,
            "optimizer_type": "adamw",
            "scheduler_type": "cosine",
            "data_augmentation": True,
            "early_stopping": True,
            "patience": 15
        }
        
        try:
            response = self.session.post(
                f"{self.api_base_url}/api/training/start",
                json=config
            )
            response.raise_for_status()
            result = response.json()
            return result["job_id"]
        except Exception as e:
            print(f"启动训练失败: {e}")
            return None
    
    def start_quick_training(self) -> str:
        """启动快速训练"""
        config = {
            "experiment_name": "快速训练-ResNet18",
            "model_type": "resnet18",
            "batch_size": 64,
            "learning_rate": 0.001,
            "epochs": 30,
            "dataset_size": 3000,
            "use_pretrained": True,
            "optimizer_type": "adam",
            "scheduler_type": "step",
            "data_augmentation": True,
            "early_stopping": True,
            "patience": 10
        }
        
        try:
            response = self.session.post(
                f"{self.api_base_url}/api/training/start",
                json=config
            )
            response.raise_for_status()
            result = response.json()
            return result["job_id"]
        except Exception as e:
            print(f"启动训练失败: {e}")
            return None
    
    def get_job_status(self, job_id: str) -> Dict[str, Any]:
        """获取训练任务状态"""
        try:
            response = self.session.get(f"{self.api_base_url}/api/training/status/{job_id}")
            response.raise_for_status()
            return response.json()
        except Exception as e:
            return {"error": str(e)}
    
    def list_jobs(self) -> Dict[str, Any]:
        """列出所有训练任务"""
        try:
            response = self.session.get(f"{self.api_base_url}/api/training/jobs")
            response.raise_for_status()
            return response.json()
        except Exception as e:
            return {"error": str(e)}
    
    def stop_training(self, job_id: str) -> Dict[str, Any]:
        """停止训练任务"""
        try:
            response = self.session.post(f"{self.api_base_url}/api/training/stop/{job_id}")
            response.raise_for_status()
            return response.json()
        except Exception as e:
            return {"error": str(e)}
    
    def monitor_training(self, job_id: str, max_duration: int = 300):
        """监控训练进度"""
        print(f"开始监控训练任务: {job_id}")
        print("=" * 80)
        
        start_time = time.time()
        last_epoch = 0
        
        while time.time() - start_time < max_duration:
            status = self.get_job_status(job_id)
            
            if "error" in status:
                print(f"获取状态失败: {status['error']}")
                break
            
            # 显示进度
            if status["current_epoch"] > last_epoch:
                last_epoch = status["current_epoch"]
                print(f"Epoch {status['current_epoch']}/{status['config']['epochs']} | "
                      f"进度: {status['progress']:.1f}% | "
                      f"训练损失: {status['train_loss']:.4f} | "
                      f"验证准确率: {status['val_acc']:.4f} | "
                      f"最佳准确率: {status['best_acc']:.4f} | "
                      f"学习率: {status['learning_rate']:.6f}")
            
            # 检查训练状态
            if status["status"] in ["completed", "failed", "stopped"]:
                print(f"\n训练结束: {status['status']}")
                print(f"最终消息: {status['message']}")
                if status["status"] == "completed":
                    print(f"🎉 训练成功完成！最佳验证准确率: {status['best_acc']:.4f}")
                break
            
            time.sleep(2)  # 每2秒检查一次
        
        print("=" * 80)

def main():
    """主函数"""
    print("🚗 增强版车牌识别训练API测试客户端")
    print("=" * 60)
    
    # 创建客户端
    client = EnhancedTrainingClient()
    
    # 测试连接
    print("1. 测试API连接...")
    result = client.test_connection()
    if "error" in result:
        print(f"❌ 连接失败: {result['error']}")
        return
    else:
        print(f"✅ 连接成功: {result['message']}")
    
    # 获取系统状态
    print("\n2. 获取系统状态...")
    status = client.get_system_status()
    if "error" not in status:
        print(f"✅ PyTorch版本: {status['pytorch_version']}")
        print(f"✅ 计算设备: {status['device']}")
        print(f"✅ CUDA支持: {'是' if status['cuda_available'] else '否'}")
        print(f"✅ CPU使用率: {status['system_info']['cpu_usage']}")
        print(f"✅ 内存使用率: {status['system_info']['memory_usage']}")
    
    # 启动高精度训练
    print("\n3. 启动高精度训练...")
    job_id = client.start_high_accuracy_training()
    
    if job_id:
        print(f"✅ 训练任务已启动，任务ID: {job_id}")
        
        # 监控训练进度
        print("\n4. 监控训练进度...")
        client.monitor_training(job_id, max_duration=600)  # 最多监控10分钟
        
        # 显示最终状态
        print("\n5. 最终训练状态...")
        final_status = client.get_job_status(job_id)
        if "error" not in final_status:
            print(f"实验名称: {final_status['config']['experiment_name']}")
            print(f"模型类型: {final_status['config']['model_type']}")
            print(f"数据集大小: {final_status['config']['dataset_size']}")
            print(f"训练状态: {final_status['status']}")
            print(f"最佳准确率: {final_status['best_acc']:.4f}")
            print(f"训练轮次: {final_status['current_epoch']}")
            
            if final_status['best_acc'] > 0.8:
                print("🎉 恭喜！训练达到了高精度目标（>80%）")
            elif final_status['best_acc'] > 0.6:
                print("👍 训练效果良好（>60%）")
            else:
                print("⚠️ 训练精度较低，建议调整参数")
    else:
        print("❌ 启动训练失败")
    
    # 列出所有任务
    print("\n6. 当前所有训练任务:")
    jobs = client.list_jobs()
    if "error" not in jobs:
        for job in jobs["jobs"]:
            print(f"- {job['job_id']}: {job['config']['experiment_name']} "
                  f"({job['status']}) - 最佳准确率: {job['best_acc']:.4f}")
    
    print("\n测试完成！")

if __name__ == "__main__":
    main()
