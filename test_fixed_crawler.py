# -*- coding: utf-8 -*-
"""
测试修复后的百度图片爬虫
Test Fixed Baidu Image Crawler
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.utils.chinese_support import setup_chinese_environment
from src.data_collection.baidu_image_crawler import BaiduImageCrawler


def test_search_only():
    """只测试搜索功能，不下载"""
    print("测试修复后的百度图片爬虫 - 仅搜索")
    print("=" * 50)
    
    # 设置中文环境
    setup_chinese_environment()
    
    # 创建爬虫实例
    crawler = BaiduImageCrawler(
        output_dir="data/test_fixed",
        max_workers=1
    )
    
    # 测试关键词（包括之前失败的）
    test_keywords = [
        "车牌",           # 中文 - 应该成功
        "蓝牌车",         # 中文 - 应该成功
        "license plate",  # 英文 - 之前失败
        "混合动力车牌",   # 中文 - 之前失败
    ]
    
    results = {}
    
    for keyword in test_keywords:
        print(f"\n测试关键词: {keyword}")
        
        try:
            # 只搜索，不下载
            image_urls = crawler.search_baidu_images(keyword, max_images=10)
            
            if len(image_urls) > 0:
                print(f"✓ 搜索成功 - 找到 {len(image_urls)} 张图片")
                
                # 显示第一个结果的详细信息
                first_img = image_urls[0]
                print(f"  方法: {first_img.get('method', 'unknown')}")
                print(f"  缩略图: {first_img.get('thumb_url', 'N/A')[:60]}...")
                print(f"  标题: {first_img.get('title', 'N/A')[:40]}...")
                
                results[keyword] = {
                    'status': 'success',
                    'count': len(image_urls),
                    'method': first_img.get('method', 'unknown')
                }
            else:
                print(f"✗ 搜索失败 - 未找到图片")
                results[keyword] = {
                    'status': 'failed',
                    'count': 0,
                    'method': 'none'
                }
                
        except Exception as e:
            print(f"✗ 搜索出错: {str(e)}")
            results[keyword] = {
                'status': 'error',
                'count': 0,
                'method': 'error',
                'error': str(e)
            }
    
    # 总结结果
    print(f"\n" + "=" * 50)
    print("测试结果总结:")
    print("=" * 50)
    
    success_count = 0
    for keyword, result in results.items():
        status_icon = "✓" if result['status'] == 'success' else "✗"
        print(f"{status_icon} {keyword}: {result['status']} ({result['count']} 张图片, 方法: {result['method']})")
        if result['status'] == 'success':
            success_count += 1
    
    print(f"\n成功率: {success_count}/{len(test_keywords)} ({success_count/len(test_keywords)*100:.1f}%)")
    
    if success_count == len(test_keywords):
        print("🎉 所有测试通过！爬虫修复成功！")
    elif success_count > 0:
        print("⚠️ 部分测试通过，爬虫部分修复成功")
    else:
        print("❌ 所有测试失败，需要进一步修复")
    
    return results


def test_small_download():
    """测试小规模下载"""
    print(f"\n" + "=" * 50)
    print("测试小规模下载功能")
    print("=" * 50)
    
    # 设置中文环境
    setup_chinese_environment()
    
    # 创建爬虫实例
    crawler = BaiduImageCrawler(
        output_dir="data/test_download",
        max_workers=1
    )
    
    try:
        # 只下载3张图片进行测试
        stats = crawler.crawl_license_plates(target_images=3)
        
        print(f"下载统计:")
        print(f"  目标图片数: {stats.get('target_images', 0)}")
        print(f"  成功下载: {stats.get('successful_downloads', 0)}")
        print(f"  失败下载: {stats.get('failed_downloads', 0)}")
        print(f"  总耗时: {stats.get('total_time', 0):.2f} 秒")
        
        if stats.get('successful_downloads', 0) > 0:
            print("✓ 下载功能正常")
            return True
        else:
            print("✗ 下载功能异常")
            return False
            
    except Exception as e:
        print(f"✗ 下载测试失败: {str(e)}")
        return False


def main():
    """主测试函数"""
    print("开始测试修复后的百度图片爬虫")
    
    # 测试1: 搜索功能
    search_results = test_search_only()
    
    # 测试2: 如果搜索成功，测试下载功能
    success_count = sum(1 for r in search_results.values() if r['status'] == 'success')
    
    if success_count > 0:
        download_success = test_small_download()
        
        if download_success:
            print(f"\n🎉 爬虫修复完成！可以正常使用了！")
            print("建议运行完整的数据采集:")
            print("python src/data_collection/run_data_collection.py --mode baidu --target 1000")
        else:
            print(f"\n⚠️ 搜索功能正常，但下载功能需要进一步调试")
    else:
        print(f"\n❌ 搜索功能仍有问题，需要进一步修复")
    
    print(f"\n测试完成！")


if __name__ == "__main__":
    main()
