#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试优化后的百度图片爬虫
包括翻页、去重、动态加载等功能
"""

import sys
import os
import time
import json
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.data_collection.baidu_image_crawler import BaiduImageCrawler
from src.utils.logger import setup_logger

def test_optimized_crawler():
    """测试优化后的爬虫功能"""
    print("=" * 60)
    print("测试优化后的百度图片爬虫")
    print("=" * 60)
    
    # 设置日志
    log_config = {
        'logging': {
            'level': 'INFO',
            'log_file': 'logs/test_crawler.log'
        }
    }
    logger = setup_logger(log_config)
    
    # 创建爬虫实例
    output_dir = "data/test_optimized_crawler"
    crawler = BaiduImageCrawler(output_dir=output_dir, max_workers=6)
    
    # 测试关键词列表
    test_keywords = [
        "车牌",
        "蓝牌车", 
        "license plate",
        "新能源车牌"
    ]
    
    results = {}
    
    for keyword in test_keywords:
        print(f"\n{'='*40}")
        print(f"测试关键词: {keyword}")
        print(f"{'='*40}")
        
        start_time = time.time()
        
        try:
            # 搜索图片（测试翻页和去重功能）
            print(f"开始搜索关键词: {keyword}")
            image_urls = crawler.search_baidu_images(keyword, max_images=100)
            
            search_time = time.time() - start_time
            
            if image_urls:
                print(f"✓ 搜索成功: 找到 {len(image_urls)} 张图片")
                print(f"  搜索耗时: {search_time:.2f} 秒")
                
                # 分析图片来源方法
                methods = {}
                for img in image_urls:
                    method = img.get('method', 'unknown')
                    methods[method] = methods.get(method, 0) + 1
                
                print(f"  图片来源分析:")
                for method, count in methods.items():
                    print(f"    {method}: {count} 张")
                
                # 测试下载功能（下载前10张）
                print(f"  开始下载前10张图片...")
                download_start = time.time()
                
                download_results = []
                for i, img_info in enumerate(image_urls[:10]):
                    result = crawler.download_image(img_info)
                    download_results.append(result is not None)
                    if i % 5 == 4:  # 每5张显示一次进度
                        print(f"    已处理 {i+1}/10 张图片")
                
                download_time = time.time() - download_start
                success_count = sum(download_results)
                
                print(f"  下载完成: {success_count}/10 张成功")
                print(f"  下载耗时: {download_time:.2f} 秒")
                
                results[keyword] = {
                    'status': 'success',
                    'search_count': len(image_urls),
                    'search_time': search_time,
                    'download_success': success_count,
                    'download_time': download_time,
                    'methods': methods
                }
                
            else:
                print(f"✗ 搜索失败: 未找到图片")
                results[keyword] = {
                    'status': 'failed',
                    'search_count': 0,
                    'search_time': search_time,
                    'download_success': 0,
                    'download_time': 0,
                    'methods': {}
                }
                
        except Exception as e:
            print(f"✗ 测试失败: {str(e)}")
            results[keyword] = {
                'status': 'error',
                'error': str(e),
                'search_count': 0,
                'search_time': 0,
                'download_success': 0,
                'download_time': 0,
                'methods': {}
            }
        
        # 短暂休息避免请求过快
        time.sleep(2)
    
    # 输出测试总结
    print(f"\n{'='*60}")
    print("测试总结")
    print(f"{'='*60}")
    
    total_images = 0
    total_downloads = 0
    successful_keywords = 0
    
    for keyword, result in results.items():
        status_symbol = "✓" if result['status'] == 'success' else "✗"
        print(f"{status_symbol} {keyword}: {result['search_count']} 张图片, {result['download_success']} 张下载成功")
        
        if result['status'] == 'success':
            successful_keywords += 1
            total_images += result['search_count']
            total_downloads += result['download_success']
    
    print(f"\n总体统计:")
    print(f"  成功关键词: {successful_keywords}/{len(test_keywords)}")
    print(f"  总搜索图片: {total_images} 张")
    print(f"  总下载成功: {total_downloads} 张")
    
    # 检查爬虫统计信息
    print(f"\n爬虫内部统计:")
    stats = crawler.download_stats
    for key, value in stats.items():
        print(f"  {key}: {value}")
    
    # 保存测试结果
    results_file = Path(output_dir) / "test_results.json"
    with open(results_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    
    print(f"\n测试结果已保存到: {results_file}")
    
    # 检查去重功能
    print(f"\n去重功能测试:")
    print(f"  已下载URL数量: {len(crawler.downloaded_urls)}")
    print(f"  已见过URL数量: {len(crawler.seen_urls)}")
    
    return results

def test_pagination_and_deduplication():
    """专门测试翻页和去重功能"""
    print(f"\n{'='*60}")
    print("专项测试: 翻页和去重功能")
    print(f"{'='*60}")
    
    output_dir = "data/test_pagination"
    crawler = BaiduImageCrawler(output_dir=output_dir, max_workers=4)
    
    # 设置更多页面进行测试
    crawler.max_pages_per_keyword = 5
    
    keyword = "车牌"
    print(f"测试关键词: {keyword}")
    print(f"最大页面数: {crawler.max_pages_per_keyword}")
    
    # 搜索更多图片测试翻页
    image_urls = crawler.search_baidu_images(keyword, max_images=150)
    
    print(f"搜索结果: {len(image_urls)} 张图片")
    
    # 分析页面分布
    page_distribution = {}
    for img in image_urls:
        page = img.get('page', 0)
        page_distribution[page] = page_distribution.get(page, 0) + 1
    
    print(f"页面分布:")
    for page in sorted(page_distribution.keys()):
        print(f"  第 {page} 页: {page_distribution[page]} 张图片")
    
    # 检查URL去重效果
    all_urls = []
    for img in image_urls:
        urls = [img.get('original_url'), img.get('middle_url'), img.get('thumb_url')]
        all_urls.extend([url for url in urls if url])
    
    unique_urls = set(all_urls)
    print(f"URL去重统计:")
    print(f"  总URL数量: {len(all_urls)}")
    print(f"  去重后数量: {len(unique_urls)}")
    print(f"  去重率: {(1 - len(unique_urls)/len(all_urls))*100:.1f}%")

if __name__ == "__main__":
    try:
        # 基础功能测试
        results = test_optimized_crawler()
        
        # 翻页和去重专项测试
        test_pagination_and_deduplication()
        
        print(f"\n{'='*60}")
        print("所有测试完成！")
        print(f"{'='*60}")
        
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        print(f"\n测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
