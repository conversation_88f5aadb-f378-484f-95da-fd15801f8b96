#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的服务器测试脚本
"""

import os
import sys
from pathlib import Path

def main():
    print("=" * 60)
    print("🧪 服务器测试")
    print("=" * 60)
    print(f"🐍 Python: {sys.executable}")
    print(f"🌍 环境: {os.environ.get('CONDA_DEFAULT_ENV', 'unknown')}")
    print("=" * 60)
    
    # 测试导入
    try:
        import fastapi
        print("✅ FastAPI 可用")
    except ImportError:
        print("❌ FastAPI 不可用")
        return
    
    try:
        import uvicorn
        print("✅ Uvicorn 可用")
    except ImportError:
        print("❌ Uvicorn 不可用")
        return
    
    # 测试后端文件
    backend_file = Path("src/web_app_v2/backend/main.py")
    if backend_file.exists():
        print("✅ 后端文件存在")
    else:
        print("❌ 后端文件不存在")
        return
    
    print("=" * 60)
    print("🚀 尝试启动服务...")
    print("💡 如果成功，请访问: http://127.0.0.1:8000/api/docs")
    print("=" * 60)
    
    # 切换到后端目录并启动
    os.chdir("src/web_app_v2/backend")
    
    try:
        # 直接导入并运行
        import main
        print("✅ 后端模块导入成功")
        
        # 启动服务
        uvicorn.run(
            "main:app",
            host="127.0.0.1",
            port=8000,
            reload=True,
            log_level="info"
        )
        
    except Exception as e:
        print(f"❌ 启动失败: {e}")

if __name__ == "__main__":
    main()
