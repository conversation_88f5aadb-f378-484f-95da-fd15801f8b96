#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
独立训练API服务器
Standalone Training API Server

独立运行的训练API服务器，避免模块依赖问题

Author: License Plate Recognition Team
Date: 2025-07-30
"""

import os
import sys
import asyncio
import uvicorn
import signal
import time
import json
import uuid
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, List, Optional

try:
    from fastapi import FastAPI, BackgroundTasks, HTTPException
    from fastapi.responses import JSONResponse, FileResponse, HTMLResponse
    from fastapi.middleware.cors import CORSMiddleware
    from pydantic import BaseModel
except ImportError:
    print("请安装FastAPI: pip install fastapi uvicorn python-multipart")
    sys.exit(1)

try:
    import torch
    import torch.nn as nn
    import torch.optim as optim
    import torchvision.models as models
    import torchvision.transforms as transforms
    from torch.utils.data import DataLoader, Dataset
except ImportError:
    print("请安装PyTorch: pip install torch torchvision")
    sys.exit(1)

try:
    import numpy as np
    from PIL import Image
    import matplotlib.pyplot as plt
    import seaborn as sns
    import pandas as pd
except ImportError:
    print("请安装必要的包: pip install numpy pillow matplotlib seaborn pandas")
    sys.exit(1)


class TrainingConfig(BaseModel):
    """训练配置模型"""
    experiment_name: Optional[str] = None
    model_type: str = "resnet18"
    batch_size: int = 32
    learning_rate: float = 0.001
    epochs: int = 30
    num_workers: int = 2


class TrainingStatus(BaseModel):
    """训练状态模型"""
    job_id: str
    status: str  # "pending", "running", "completed", "failed", "interrupted"
    current_epoch: int
    total_epochs: int
    train_loss: float
    val_loss: float
    train_acc: float
    val_acc: float
    best_acc: float
    progress: float
    message: str
    start_time: Optional[str] = None
    end_time: Optional[str] = None


class MockDataset(Dataset):
    """模拟数据集"""
    
    def __init__(self, size=1000, transform=None):
        self.size = size
        self.transform = transform
        
        # 中国车牌字符映射
        self.provinces = ['京', '津', '沪', '渝', '冀', '豫', '云', '辽', '黑', '湘', '皖', '鲁', '新', '苏', '浙', '赣', '鄂', '桂', '甘', '晋', '蒙', '陕', '吉', '闽', '贵', '粤', '青', '藏', '川', '宁', '琼']
        self.letters = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'J', 'K', 'L', 'M', 'N', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z']
        self.digits = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9']
        
        # 所有字符
        self.all_chars = self.provinces + self.letters + self.digits
        self.char_to_idx = {char: idx for idx, char in enumerate(self.all_chars)}
        self.idx_to_char = {idx: char for idx, char in enumerate(self.all_chars)}
        
    def __len__(self):
        return self.size
    
    def __getitem__(self, idx):
        # 生成随机图像 (3, 64, 64)
        image = torch.randn(3, 64, 64)
        
        # 随机选择一个字符作为标签
        label = np.random.randint(0, len(self.all_chars))
        
        if self.transform:
            # 转换为PIL图像再应用变换
            image_pil = transforms.ToPILImage()(image)
            image = self.transform(image_pil)
        
        return image, label


class StandaloneTrainingAPI:
    """独立训练API服务器"""
    
    def __init__(self):
        """初始化API服务器"""
        self.app = FastAPI(title="CCPD训练API", version="1.0.0")
        self.training_jobs = {}  # 存储训练任务
        
        # 添加CORS中间件
        self.app.add_middleware(
            CORSMiddleware,
            allow_origins=["*"],
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )
        
        self.setup_routes()
        print("独立训练API服务器初始化完成")
    
    def setup_routes(self):
        """设置API路由"""
        
        @self.app.get("/", response_class=HTMLResponse)
        async def root():
            """根路径 - 返回监控面板"""
            monitor_file = Path("src/training/training_monitor.html")
            if monitor_file.exists():
                with open(monitor_file, 'r', encoding='utf-8') as f:
                    return f.read()
            else:
                return """
                <h1>CCPD训练API服务器</h1>
                <p>服务器运行正常</p>
                <p>API文档: <a href="/docs">/docs</a></p>
                """
        
        @self.app.post("/api/training/start")
        async def start_training(config: TrainingConfig, background_tasks: BackgroundTasks):
            """启动训练任务"""
            try:
                # 生成任务ID
                job_id = str(uuid.uuid4())
                
                # 创建训练状态
                status = TrainingStatus(
                    job_id=job_id,
                    status="pending",
                    current_epoch=0,
                    total_epochs=config.epochs,
                    train_loss=0.0,
                    val_loss=0.0,
                    train_acc=0.0,
                    val_acc=0.0,
                    best_acc=0.0,
                    progress=0.0,
                    message="训练任务已创建，等待开始",
                    start_time=datetime.now().isoformat()
                )
                
                self.training_jobs[job_id] = status
                
                # 在后台启动训练
                background_tasks.add_task(self._run_training, job_id, config.dict())
                
                return {"job_id": job_id, "message": "训练任务已启动", "status": "pending"}
                
            except Exception as e:
                print(f"启动训练失败: {e}")
                raise HTTPException(status_code=500, detail=str(e))
        
        @self.app.get("/api/training/status/{job_id}")
        async def get_training_status(job_id: str):
            """获取训练状态"""
            if job_id not in self.training_jobs:
                raise HTTPException(status_code=404, detail="训练任务不存在")
            
            return self.training_jobs[job_id]
        
        @self.app.get("/api/training/jobs")
        async def list_training_jobs():
            """列出所有训练任务"""
            return {"jobs": list(self.training_jobs.values())}
        
        @self.app.post("/api/training/stop/{job_id}")
        async def stop_training(job_id: str):
            """停止训练任务"""
            if job_id not in self.training_jobs:
                raise HTTPException(status_code=404, detail="训练任务不存在")
            
            status = self.training_jobs[job_id]
            if status.status == "running":
                status.status = "interrupted"
                status.message = "训练已被用户中断"
                status.end_time = datetime.now().isoformat()
                
                return {"message": "训练任务已停止"}
            else:
                return {"message": "训练任务未在运行"}
        
        @self.app.delete("/api/training/delete/{job_id}")
        async def delete_training_job(job_id: str):
            """删除训练任务"""
            if job_id not in self.training_jobs:
                raise HTTPException(status_code=404, detail="训练任务不存在")
            
            # 删除训练任务记录
            del self.training_jobs[job_id]
            
            return {"message": "训练任务已删除"}
    
    async def _run_training(self, job_id: str, config: Dict[str, Any]):
        """在后台运行训练"""
        try:
            # 更新状态为运行中
            status = self.training_jobs[job_id]
            status.status = "running"
            status.message = "训练正在进行中"
            
            # 设备
            device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
            print(f"使用设备: {device}")
            
            # 创建模拟数据集
            train_transform = transforms.Compose([
                transforms.Resize((64, 64)),
                transforms.RandomRotation(10),
                transforms.ColorJitter(brightness=0.2, contrast=0.2),
                transforms.ToTensor(),
                transforms.Normalize(mean=[0.485, 0.456, 0.406], 
                                   std=[0.229, 0.224, 0.225])
            ])
            
            val_transform = transforms.Compose([
                transforms.Resize((64, 64)),
                transforms.ToTensor(),
                transforms.Normalize(mean=[0.485, 0.456, 0.406], 
                                   std=[0.229, 0.224, 0.225])
            ])
            
            # 创建数据集
            train_dataset = MockDataset(size=800, transform=train_transform)
            val_dataset = MockDataset(size=200, transform=val_transform)
            
            # 创建数据加载器
            train_loader = DataLoader(train_dataset, batch_size=config["batch_size"], 
                                     shuffle=True, num_workers=0)  # Windows下设为0
            val_loader = DataLoader(val_dataset, batch_size=config["batch_size"], 
                                   shuffle=False, num_workers=0)
            
            # 创建模型
            num_classes = len(train_dataset.all_chars)
            model = models.resnet18(pretrained=True)
            model.fc = nn.Linear(model.fc.in_features, num_classes)
            model = model.to(device)
            
            # 创建优化器和损失函数
            optimizer = optim.AdamW(model.parameters(), lr=config["learning_rate"], weight_decay=1e-4)
            criterion = nn.CrossEntropyLoss()
            scheduler = optim.lr_scheduler.CosineAnnealingWarmRestarts(optimizer, T_0=10, T_mult=2)
            
            best_accuracy = 0.0
            
            # 开始训练
            for epoch in range(config["epochs"]):
                # 检查是否被中断
                if status.status == "interrupted":
                    break
                
                # 训练一个epoch
                train_loss, train_acc = self._train_epoch(model, train_loader, optimizer, criterion, device)
                val_loss, val_acc = self._validate_epoch(model, val_loader, criterion, device)
                
                # 更新最佳准确率
                if val_acc > best_accuracy:
                    best_accuracy = val_acc
                
                # 更新API状态
                status.current_epoch = epoch + 1
                status.train_loss = train_loss
                status.val_loss = val_loss
                status.train_acc = train_acc
                status.val_acc = val_acc
                status.best_acc = best_accuracy
                status.progress = (epoch + 1) / config["epochs"] * 100
                status.message = f"Epoch {epoch + 1}/{config['epochs']} - Val Acc: {val_acc:.4f}"
                
                # 学习率调度
                scheduler.step()
                
                print(f"Epoch {epoch + 1}/{config['epochs']} - Train Loss: {train_loss:.4f}, Val Acc: {val_acc:.4f}")
                
                # 模拟训练时间
                await asyncio.sleep(1)
            
            # 训练完成
            if status.status != "interrupted":
                status.status = "completed"
                status.message = f"训练完成！最佳验证准确率: {best_accuracy:.4f}"
                status.end_time = datetime.now().isoformat()
                print(f"训练任务 {job_id} 完成")
            
        except Exception as e:
            # 训练失败
            status.status = "failed"
            status.message = f"训练失败: {str(e)}"
            status.end_time = datetime.now().isoformat()
            print(f"训练任务 {job_id} 失败: {e}")
    
    def _train_epoch(self, model, train_loader, optimizer, criterion, device):
        """训练一个epoch"""
        model.train()
        total_loss = 0.0
        correct = 0
        total = 0
        
        for batch_idx, (data, target) in enumerate(train_loader):
            data, target = data.to(device), target.to(device)
            
            optimizer.zero_grad()
            output = model(data)
            loss = criterion(output, target)
            loss.backward()
            optimizer.step()
            
            total_loss += loss.item()
            pred = output.argmax(dim=1, keepdim=True)
            correct += pred.eq(target.view_as(pred)).sum().item()
            total += target.size(0)
        
        avg_loss = total_loss / len(train_loader)
        accuracy = correct / total
        
        return avg_loss, accuracy
    
    def _validate_epoch(self, model, val_loader, criterion, device):
        """验证一个epoch"""
        model.eval()
        total_loss = 0.0
        correct = 0
        total = 0
        
        with torch.no_grad():
            for data, target in val_loader:
                data, target = data.to(device), target.to(device)
                
                output = model(data)
                loss = criterion(output, target)
                
                total_loss += loss.item()
                pred = output.argmax(dim=1, keepdim=True)
                correct += pred.eq(target.view_as(pred)).sum().item()
                total += target.size(0)
        
        avg_loss = total_loss / len(val_loader)
        accuracy = correct / total
        
        return avg_loss, accuracy
    
    def run(self, host: str = "127.0.0.1", port: int = 8001):
        """运行API服务器"""
        print(f"启动训练API服务器: http://{host}:{port}")
        print(f"监控面板: http://{host}:{port}")
        print(f"API文档: http://{host}:{port}/docs")
        uvicorn.run(self.app, host=host, port=port)


def main():
    """主函数"""
    print("=" * 60)
    print("CCPD独立训练API服务器")
    print("=" * 60)
    
    server = StandaloneTrainingAPI()
    server.run()


if __name__ == "__main__":
    main()
