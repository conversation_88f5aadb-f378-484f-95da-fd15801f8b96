#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
查看训练结果
View Training Results

Author: License Plate Recognition Team
Date: 2025-07-30
"""

import requests
import json

def view_training_results():
    """查看所有训练结果"""
    try:
        response = requests.get('http://127.0.0.1:8007/api/training/jobs')
        jobs = response.json()['jobs']
        
        print("🎉 车牌识别训练系统 - 训练结果报告")
        print("=" * 80)
        
        for i, job in enumerate(jobs, 1):
            print(f"\n📊 训练任务 {i}: {job['config']['experiment_name']}")
            print("-" * 60)
            
            # 基本信息
            print(f"任务ID: {job['job_id']}")
            print(f"状态: {job['status'].upper()}")
            print(f"模型类型: {job['config']['model_type']}")
            print(f"完成轮次: {job['current_epoch']}/{job['config']['epochs']}")
            
            # 训练结果
            print(f"\n🏆 训练结果:")
            print(f"  最终训练损失: {job['train_loss']:.4f}")
            print(f"  最终验证损失: {job['val_loss']:.4f}")
            print(f"  最终训练准确率: {job['train_acc']*100:.2f}%")
            print(f"  最终验证准确率: {job['val_acc']*100:.2f}%")
            print(f"  🥇 最佳验证准确率: {job['best_acc']*100:.2f}%")
            
            # 模型信息
            if job.get('model_params'):
                print(f"  模型参数量: {job['model_params']:,} ({job['model_params']/1000000:.1f}M)")
            
            # 性能信息
            if job.get('epoch_times') and len(job['epoch_times']) > 0:
                avg_time = sum(job['epoch_times']) / len(job['epoch_times'])
                total_time = sum(job['epoch_times'])
                print(f"  平均轮次耗时: {avg_time:.1f}s")
                print(f"  总训练时间: {total_time:.1f}s ({total_time/60:.1f}分钟)")
            
            # 训练配置
            print(f"\n🔧 训练配置:")
            config = job['config']
            print(f"  批次大小: {config['batch_size']}")
            print(f"  学习率: {config['learning_rate']}")
            print(f"  优化器: {config['optimizer_type']}")
            print(f"  调度器: {config['scheduler_type']}")
            print(f"  数据增强: {'✅' if config['data_augmentation'] else '❌'}")
            print(f"  早停机制: {'✅' if config['early_stopping'] else '❌'}")
            print(f"  数据集大小: {config['dataset_size']}")
            
            # 最终状态消息
            print(f"\n💬 最终状态: {job['message']}")
            
            # 显示部分训练日志
            if job.get('detailed_log') and len(job['detailed_log']) > 0:
                print(f"\n📝 训练日志 (最近5条):")
                recent_logs = job['detailed_log'][-5:]
                for log in recent_logs:
                    print(f"  {log}")
        
        # 总结
        print(f"\n" + "=" * 80)
        print("📈 训练总结:")
        completed_jobs = [job for job in jobs if job['status'] == 'completed']
        if completed_jobs:
            best_job = max(completed_jobs, key=lambda x: x['best_acc'])
            print(f"  总训练任务数: {len(jobs)}")
            print(f"  完成任务数: {len(completed_jobs)}")
            print(f"  🏆 最佳训练结果: {best_job['config']['experiment_name']}")
            print(f"  🥇 最高验证准确率: {best_job['best_acc']*100:.2f}%")
            print(f"  🔥 最佳模型: {best_job['config']['model_type']}")
        
        print(f"\n🌐 Web界面: http://127.0.0.1:8007")
        print("=" * 80)
        
    except Exception as e:
        print(f"❌ 获取结果失败: {e}")

if __name__ == "__main__":
    view_training_results()
